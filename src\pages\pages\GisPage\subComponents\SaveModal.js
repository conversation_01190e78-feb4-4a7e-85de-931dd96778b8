import React, { useState, useCallback, useMemo } from "react";

// material-ui
import {
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow,
    Alert,
    CircularProgress,
    Box
} from "@mui/material";

// config
import { renderTableHeaders, CELL_STYLE } from "../config/config";

function SaveModal({ open, setOpen, editedRows, setSaving, updateLandDataBatch, getTableData }) {
    const [isSaved, setIsSaved] = useState(false);
    const [msg, setMsg] = useState("");
    const [isProcessing, setIsProcessing] = useState(false);

    // 計算編輯的行數據（使用 useMemo 優化性能）
    const editedRowsArray = useMemo(() => Object.values(editedRows), [editedRows]);

    // 關閉對話框
    const handleClose = useCallback((_, reason) => {
        if (reason === "backdropClick" || isProcessing) return;
        setOpen(false);
        // 重置狀態
        setIsSaved(false);
        setMsg("");
    }, [setOpen, isProcessing]);

    // 重置組件狀態
    const resetState = useCallback(() => {
        setIsSaved(false);
        setMsg("");
        setIsProcessing(false);
    }, []);

    // 處理儲存結果的工具函數
    const formatSaveResult = useCallback((results) => {
        const successCount = results.filter(r => r.success).length;
        const failCount = results.filter(r => !r.success).length;

        if (failCount === 0) {
            return `成功儲存 ${successCount} 筆變更！`;
        } else {
            const failedIds = results.filter(r => !r.success).map(r => r.id);
            return `儲存完成！成功: ${successCount} 筆，失敗: ${failCount} 筆\n失敗的項目: ${failedIds.join(', ')}`;
        }
    }, []);

    // 執行儲存操作
    const handleSaveData = useCallback(async () => {
        if (editedRowsArray.length === 0) {
            return '沒有需要儲存的變更';
        }

        setIsProcessing(true);
        setSaving(true);

        try {
            const results = await updateLandDataBatch(editedRows);
            return formatSaveResult(results);
        } catch (error) {
            console.error("儲存過程發生錯誤:", error);
            return "儲存失敗，請稍後再試";
        } finally {
            setSaving(false);
            setIsProcessing(false);
        }
    }, [editedRowsArray.length, editedRows, setSaving, updateLandDataBatch, formatSaveResult]);

    // 執行儲存並處理後續操作
    const handleSave = useCallback(async () => {
        try {
            const resultMsg = await handleSaveData();
            setMsg(resultMsg);
            setIsSaved(true);

            // 3秒後自動關閉並刷新數據
            setTimeout(() => {
                handleClose();
                resetState();
                getTableData();
            }, 3000);
        } catch (error) {
            console.error("儲存操作失敗:", error);
            setMsg("儲存操作失敗，請稍後再試");
            setIsSaved(true);
        }
    }, [handleSaveData, handleClose, resetState, getTableData]);

    // 渲染表格內容
    const renderTableBody = () => (
        <TableBody>
            {editedRowsArray.map((row) => (
                <TableRow key={`${row.landName}-${row.landSerialNumber}`}>
                    <TableCell sx={CELL_STYLE}>{row.landName}</TableCell>
                    <TableCell sx={CELL_STYLE}>{row.landSerialNumber}</TableCell>
                    <TableCell sx={CELL_STYLE}>{row.x}</TableCell>
                    <TableCell sx={CELL_STYLE}>{row.y}</TableCell>
                    <TableCell sx={CELL_STYLE}>{row.lat}</TableCell>
                    <TableCell sx={CELL_STYLE}>{row.long}</TableCell>
                </TableRow>
            ))}
        </TableBody>
    );

    // 渲染儲存結果
    const renderSaveResult = () => (
        <Box sx={{ p: 2, textAlign: 'center' }}>
            <Alert
                severity={msg.includes('失敗') ? 'error' : 'success'}
                sx={{ mb: 2 }}
            >
                {msg}
            </Alert>
            {isProcessing && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                    <CircularProgress size={24} />
                </Box>
            )}
        </Box>
    );

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            maxWidth="lg"
            fullWidth
            disableEscapeKeyDown={isProcessing}
        >
            <DialogTitle>
                {isSaved ? '儲存結果' : `確認儲存 ${editedRowsArray.length} 筆變更`}
            </DialogTitle>

            <DialogContent>
                {isSaved ? renderSaveResult() : (
                    <Table>
                        {renderTableHeaders()}
                        {renderTableBody()}
                    </Table>
                )}
            </DialogContent>

            {!isSaved && (
                <DialogActions>
                    <Button
                        onClick={handleClose}
                        disabled={isProcessing}
                    >
                        取消
                    </Button>
                    <Button
                        onClick={handleSave}
                        variant="contained"
                        disabled={isProcessing || editedRowsArray.length === 0}
                        startIcon={isProcessing ? <CircularProgress size={20} /> : null}
                    >
                        {isProcessing ? '儲存中...' : '確認儲存'}
                    </Button>
                </DialogActions>
            )}
        </Dialog>
    );
}

export default SaveModal;
