import domtoimage from "dom-to-image";

const domToImage = ({ id, fileName = "image", quality = 0.95 }) => {
  const node = document.getElementById(id);
  // 防止barchart的Y軸擋住圖，就先將他移到最左邊，拍完照再移回來
  const yaxis = document.getElementById("yaxis");
  const barChartElement = document.getElementById("barChart__block");
  if (yaxis) {
    yaxis.setAttribute("transform", "translate(0, 0 )");
  }

  domtoimage
    .toPng(node, { quality })
    .then((dataUrl) => {
      const link = document.createElement("a");
      link.download = `${fileName}.png`;
      link.href = dataUrl;
      link.click();
      if (yaxis && barChartElement) {
        yaxis.setAttribute("transform", "translate(0, 0 )");
        yaxis.setAttribute(
          "transform",
          `translate(${barChartElement.scrollLeft}, 0)`
        );
      }
    })
    .catch((error) => {
      console.error("oops, something went wrong!", error);
    });
};

export default domToImage;
