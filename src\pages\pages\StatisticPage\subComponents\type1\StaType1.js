import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";

// scss
import "./StaType1.scss";

// material ui
import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import Box from "@mui/material/Box";

// api
import { useHistory, useLocation } from "react-router-dom";
import queryString from "query-string";
import { Api, readOntoData } from "../../../../../api/land/Api";

// utils
import { isEmpty } from "../../../../../utils";

// components
import SpecChart from "./SpecChart";
import statAct from "../../config/statAct";
import hisAddSearch from "../../../../../utils/hisAddSearch";

function StaType1({ statApi, dispatch }) {
  const history = useHistory();
  const location = useLocation();

  const [LNList, setLNList] = useState([]);
  const [landName, setLandName] = useState("");
  const [LSNList, setLSNList] = useState([]);
  const [landSNArr, setLandSNArr] = useState([]);
  const [data, setData] = useState([]);

  useEffect(() => {
    const apiStr = Api.getLNList();
    readOntoData(apiStr).then((result) => {
      setLNList(result.data);
    });

    // record export data header
    dispatch({
      type: statAct.SET_HEADERCOLS,
      payload: [
        { id: "landId", displayName: "landId" },
        { id: "landSN", displayName: "地號" },
        { id: "lmCount", displayName: "土地標示變更次數" },
        { id: "lrCount", displayName: "土地權利變更次數" },
      ],
    });

    // get url parameter
    const { landName: urlLN, landSN } = queryString.parse(location.search);
    if (urlLN) {
      setLandName(urlLN);
    }

    if (landSN) {
      const tmpArr = landSN.split(",");
      setLandSNArr(tmpArr);
    }
  }, []);

  useEffect(() => {
    if (isEmpty(landName)) {
      setLSNList([]);
      return;
    }

    const apiStr = Api.getLSList().replace("{landName}", landName);
    readOntoData(apiStr).then((result) => {
      setLSNList(result.data);
    });
  }, [landName]);

  useEffect(() => {
    if (!landName || isEmpty(landSNArr)) {
      setData([]);
      return;
    }
    const apiStr = statApi
      .replace("{landName}", landName)
      .replace("{landSN}", landSNArr.join(","));
    readOntoData(apiStr).then((res) => {
      setData(res.data);
    });
  }, [landName, landSNArr]);

  useEffect(() => {
    // 設定匯出資料
    dispatch({
      type: statAct.SET_EXPDATA,
      payload: data,
    });
  }, [data]);

  useEffect(() => {
    // set fileName
    dispatch({
      type: statAct.SET_FILENAME,
      payload: `${landName || ""}的地號相關資料`,
    });
  }, [landName]);

  const changeLN = (evt, value) => {
    setLandName(value);
    setLandSNArr([]);

    // record landName on url
    const { type } = queryString.parse(location.search);
    hisAddSearch(history, { type, landName: value || "" });
  };

  const changeLSN = (evt, valArr) => {
    setLandSNArr(valArr);

    const { type, landName: urlLN } = queryString.parse(location.search);
    hisAddSearch(history, { type, landName: urlLN, landSN: valArr.join(",") });
  };

  return (
    <Box className="StaType1">
      <Box className="DPArea">
        <Autocomplete
          disablePortal
          options={LNList.map((element) => element.landName)}
          renderInput={(params) => (
            <TextField {...params} label="土名（舊地段名）" />
          )}
          onChange={changeLN}
          value={landName}
        />
        <Autocomplete
          disablePortal
          options={LSNList.map((element) => element.landSerialNumber)}
          renderInput={(params) => (
            <TextField {...params} label="地番（地號）" />
          )}
          onChange={changeLSN}
          multiple
          limitTags={8}
          value={landSNArr}
          disableCloseOnSelect
        />
      </Box>
      <Box className="chartDiv">
        <SpecChart data={data} />
      </Box>
    </Box>
  );
}

StaType1.propTypes = {
  /** 讀取統計資料需要的API */
  statApi: PropTypes.string,
  /** 要回傳統計資料到上一層給CusDWBtn使用 */
  dispatch: PropTypes.func,
};

StaType1.defaultProps = {
  /** 讀取統計資料需要的API */
  statApi: "",
  /** 要回傳統計資料到上一層給CusDWBtn使用 */
  dispatch: () => {},
};

export default StaType1;
