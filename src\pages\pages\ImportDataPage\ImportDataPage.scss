* {
  box-sizing: border-box;
}

.ImportDataPage {
  padding: 2%;
  border: 1px solid var(--color-border);
  border-radius: 12px;
  background-color: var(--color-surface);
  box-shadow: var(--shadow-surface);

  // 頂部標題區域
  &__header {
    .MuiButton-outlined {
      border-color: var(--color-primary);
      color: var(--color-primary);
      
      &:hover {
        background-color: rgba(0, 140, 186, 0.08);
        border-color: var(--color-primary);
      }
    }
  }

  // 拖放區域
  .dropZone {
    min-height: 50vh;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 3px dashed var(--color-border-strong);
    border-radius: 16px;
    background-color: var(--color-surface-alt);
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      border-color: var(--color-primary);
      background-color: rgba(0, 140, 186, 0.05);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: translateY(0);
    }

    @media (max-width: 768px) {
      min-height: 40vh;
      border-width: 2px;
    }
  }

  // 拖放區域內容
  .typoBox {
    text-align: center;
    color: var(--color-text-secondary);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;

    .MuiTypography-h6 {
      color: var(--color-text-primary);
      font-weight: 500;
    }

    .MuiTypography-body2 {
      margin-top: 8px;
    }

    .MuiTypography-caption {
      margin-top: 4px;
      color: var(--color-text-muted);
    }
  }

  // 驗證進度條
  .MuiLinearProgress-root {
    border-radius: 4px;
    height: 6px;
    background-color: var(--color-surface-alt);

    .MuiLinearProgress-bar {
      background-color: var(--color-primary);
    }
  }

  // Alert 樣式
  .MuiAlert-root {
    border-radius: 8px;
    
    &.MuiAlert-standardSuccess {
      background-color: rgba(76, 175, 80, 0.1);
      border: 1px solid var(--color-success);
      
      .MuiAlert-icon {
        color: var(--color-success);
      }
    }

    &.MuiAlert-standardError {
      background-color: rgba(244, 67, 54, 0.1);
      border: 1px solid var(--color-danger);
      
      .MuiAlert-icon {
        color: var(--color-danger);
      }
    }

    &.MuiAlert-standardInfo {
      background-color: rgba(33, 150, 243, 0.1);
      border: 1px solid var(--color-info);
      
      .MuiAlert-icon {
        color: var(--color-info);
      }
    }
  }

  // 底部按鈕區域
  .bottomArea {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-top: 1.5rem;
    margin-top: 2rem;
    border-top: 1px solid var(--color-border-light);

    .MuiButton-root {
      padding: 10px 24px;
      font-weight: 500;
      
      &:disabled {
        opacity: 0.5;
      }
    }

    @media (max-width: 600px) {
      justify-content: center;
      
      .MuiButton-root {
        width: 100%;
      }
    }
  }

  // 檔案資訊列表
  .MuiList-root {
    width: 100%;
    
    .MuiListItem-root {
      background-color: var(--color-surface-alt);
      border-radius: 8px;
      margin-bottom: 8px;
      padding: 12px 16px;
    }
  }

  // 響應式調整
  @media (max-width: 960px) {
    padding: 16px;
  }

  @media (max-width: 600px) {
    padding: 12px;
    border-radius: 8px;

    &__header {
      .MuiButton-outlined {
        font-size: 0.875rem;
      }
    }
  }
}
