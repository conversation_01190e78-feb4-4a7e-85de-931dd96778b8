import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
  Typography,
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import LandscapeIcon from '@mui/icons-material/Landscape';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import SentimentSatisfiedAltIcon from '@mui/icons-material/SentimentSatisfiedAlt';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';

// Utils
import {
  formatRelativeTime,
  calculateDuration,
  formatDuration,
  isLongEditingDuration,
} from '../../../../utils/timeUtils';

/**
 * EditSessionList - 編輯狀態列表元件
 * 顯示所有正在編輯的土地資料
 * @param {Array} sessions - 編輯會話陣列
 */
function EditSessionList({ sessions }) {
  /**
   * 計算每個 session 的額外資訊
   */
  const enrichedSessions = useMemo(
    () =>
      sessions.map((session) => {
        const duration = calculateDuration(session.lockStartTime);
        const isLongDuration = isLongEditingDuration(duration);

        return {
          ...session,
          duration,
          isLongDuration,
          relativeTime: formatRelativeTime(session.lockStartTime),
          formattedDuration: formatDuration(duration),
        };
      }),
    [sessions]
  );

  // 空狀態
  if (!sessions || sessions.length === 0) {
    return (
      <Box
        sx={{
          textAlign: 'center',
          py: 8,
          px: 2,
          backgroundColor: 'var(--color-surface-alt)',
          borderRadius: 2,
          border: '1px dashed var(--color-border)',
        }}
      >
        <SentimentSatisfiedAltIcon 
          sx={{ 
            fontSize: '4rem', 
            color: 'var(--color-success)',
            mb: 2,
            opacity: 0.6
          }} 
        />
        <Typography 
          variant="h6" 
          gutterBottom
          sx={{ color: 'var(--color-text-secondary)' }}
        >
          目前沒有人在編輯
        </Typography>
        <Typography 
          variant="body2" 
          sx={{ color: 'var(--color-text-muted)' }}
        >
          所有土地資料都可以進行編輯操作
        </Typography>
      </Box>
    );
  }

  return (
    <>
      {/* 表格 */}
      <TableContainer component={Paper} elevation={0} sx={{ border: '1px solid var(--color-border-light)' }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'var(--color-surface-alt)' }}>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PersonIcon fontSize="small" />
                  編輯者
                </Box>
              </TableCell>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <LandscapeIcon fontSize="small" />
                  土地資料
                </Box>
              </TableCell>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AccessTimeIcon fontSize="small" />
                  開始時間
                </Box>
              </TableCell>
              <TableCell>編輯時長</TableCell>
              <TableCell align="center">狀態</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {enrichedSessions.map((session, index) => (
              <TableRow
                key={session.landId || index}
                sx={{
                  '&:hover': {
                    backgroundColor: 'var(--color-hover)',
                  },
                  ...(session.isLongDuration && {
                    backgroundColor: 'rgba(255, 152, 0, 0.08)',
                  }),
                }}
              >
                {/* 編輯者 */}
                <TableCell>
                  <Typography variant="body2" fontWeight={500}>
                    {session.lockUser}
                  </Typography>
                </TableCell>

                {/* 土地資料 */}
                <TableCell>
                  <Typography variant="body2">
                    {session.landName} - {session.landSerialNumber}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ID: {session.landId}
                  </Typography>
                </TableCell>

                {/* 開始時間 */}
                <TableCell>
                  <Typography variant="body2">
                    {session.relativeTime}
                  </Typography>
                </TableCell>

                {/* 編輯時長 */}
                <TableCell>
                  <Typography
                    variant="body2"
                    color={session.isLongDuration ? 'warning.main' : 'text.primary'}
                    fontWeight={session.isLongDuration ? 600 : 400}
                  >
                    {session.formattedDuration}
                  </Typography>
                  {session.isLongDuration && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                      <WarningAmberIcon sx={{ fontSize: '0.875rem', color: 'var(--color-warning)' }} />
                      <Typography 
                        variant="caption" 
                        sx={{ color: 'var(--color-warning)' }}
                      >
                        編輯時間較長
                      </Typography>
                    </Box>
                  )}
                </TableCell>

                {/* 狀態 */}
                <TableCell align="center">
                  <Chip
                    label="編輯中"
                    size="small"
                    color="success"
                    sx={{
                      fontWeight: 500,
                      '& .MuiChip-label': {
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5,
                      },
                    }}
                    icon={
                      <Box
                        component="span"
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          backgroundColor: 'success.main',
                          display: 'inline-block',
                          animation: 'pulse 2s ease-in-out infinite',
                          '@keyframes pulse': {
                            '0%, 100%': {
                              opacity: 1,
                            },
                            '50%': {
                              opacity: 0.5,
                            },
                          },
                        }}
                      />
                    }
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* 提示訊息 */}
      {enrichedSessions.some((s) => s.isLongDuration) && (
        <Box
          sx={{
            mt: 2,
            p: 2,
            backgroundColor: 'rgba(255, 152, 0, 0.08)',
            borderLeft: '4px solid var(--color-warning)',
            borderRadius: 1,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
            <WarningAmberIcon sx={{ color: 'var(--color-warning)', fontSize: '1.25rem' }} />
            <Typography 
              variant="body2" 
              fontWeight={500}
              sx={{ color: 'var(--color-text-primary)' }}
            >
              警告：有編輯者的編輯時間超過 30 分鐘
            </Typography>
          </Box>
          <Typography 
            variant="caption" 
            sx={{ color: 'var(--color-text-secondary)', ml: 4 }}
          >
            長時間編輯可能表示遇到問題或忘記取消編輯，建議確認狀況
          </Typography>
        </Box>
      )}
    </>
  );
}

EditSessionList.propTypes = {
  sessions: PropTypes.arrayOf(
    PropTypes.shape({
      landId: PropTypes.string.isRequired,
      lockUser: PropTypes.string.isRequired,
      lockStartTime: PropTypes.string,
      landName: PropTypes.string.isRequired,
      landSerialNumber: PropTypes.string.isRequired,
    })
  ).isRequired,
};

export default EditSessionList;
