.Header {
  height: 10%;
  margin: 6px 8px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  border: 1px solid var(--color-border);
  background-color: var(--color-surface);
  box-shadow: var(--shadow-surface);

  &__section {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  &__left {
    flex-wrap: wrap;
  }

  &__right {
    justify-content: flex-end;
  }

  &__actions {
    justify-content: flex-end;
    flex-wrap: wrap;
    gap: 8px;
  }

  &__menuToggle {
    color: var(--color-primary);
    padding: 6px;
  }

  &__drawer {
    width: 260px;
    padding: 16px 20px;
    display: flex;
    flex-direction: column;
    height: 100%;
    box-sizing: border-box;
    gap: 12px;
  }

  &__drawerHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .HeaderItem {
    min-width: 100px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    button {
      width: 100%;
    }

    &.is-active button {
      color: var(--color-primary);
      font-weight: 600;
    }
  }

  .hrLine {
    height: 4px;
    width: 100%;
    background-color: var(--color-primary);
    border-radius: 999px;
    margin-top: 4px;
  }
}

@media (max-width: 1200px) {
  .Header {
    flex-wrap: wrap;
    gap: 8px;

    .HeaderItem {
      min-width: 88px;
    }
  }
}

@media (max-width: 960px) {
  .Header {
    padding: 8px 12px;

    .HeaderItem {
      min-width: auto;

      button {
        padding: 6px 12px;
      }
    }
  }
}

@media (max-width: 600px) {
  .Header {
    margin: 4px;
    padding: 8px 10px;

    &__left {
      flex: 1;
    }

    &__actions {
      width: 100%;
    }
  }
}
