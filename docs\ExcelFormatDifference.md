# Excel 檔案格式差異說明

## 📊 您的檔案 vs 系統標準

### 發現的差異

根據您上傳的 Excel 檔案第一列，發現以下欄位名稱與系統不符：

#### 1. 日期欄位格式錯誤

**您的檔案**：
```
Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent-->year、month、day
Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent-->year、month、day
```

**系統標準**：
```
Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent
Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent
```

❌ **問題**：欄位名稱後面多了 `-->year、month、day`

✅ **解決方法**：移除 `-->year、month、day`，日期應直接填寫 `YYYY-MM-DD` 格式（例：`2022-08-15`）

---

#### 2. 所有權人欄位錯誤

**您的檔案**：
```
Land-->hasEvent-->LandRights-->hasOwner-->label
```

**系統標準**：
```
Land-->hasEvent-->LandRights-->hasOwner-->Owner
```

❌ **問題**：最後應為 `Owner` 而非 `label`

✅ **解決方法**：將 `label` 改為 `Owner`

---

## 🔧 快速修正指南

### 方法一：手動修正（適合小幅修改）

1. 開啟您的 Excel 檔案
2. 找到第一列（表頭）
3. 修正以下欄位：

| 欄位位置 | 原始欄位名稱（錯誤） | 正確欄位名稱 |
|---------|---------------------|-------------|
| 第 13 欄 | `Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent-->year、month、day` | `Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent` |
| 第 19 欄 | `Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent-->year、month、day` | `Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent` |
| 第 21 欄 | `Land-->hasEvent-->LandRights-->hasOwner-->label` | `Land-->hasEvent-->LandRights-->hasOwner-->Owner` |

4. 儲存後重新上傳

### 方法二：使用範本（推薦）

1. 在系統中點擊「**下載範本**」按鈕
2. 開啟下載的範本檔案
3. 將您現有資料複製貼上到範本中（從第 4 列開始）
4. 確認表頭（第 1-3 列）保持不變
5. 儲存後上傳

---

## 📋 完整的標準表頭列表

以下是系統要求的 21 個標準欄位（順序不可調換）：

```
1.  Land-->collectionPlace
2.  Land-->operator
3.  Land-->boxNumber
4.  Land-->number
5.  Land-->landName
6.  Land-->landSerialNumber
7.  Land-->abstract
8.  Land-->source
9.  Land-->pawnRight
10. Land-->plowingRight
11. Land-->hasEvent-->LandMark-->landMarkNumber
12. Land-->hasEvent-->LandMark-->cause
13. Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent
14. Land-->hasEvent-->LandMark-->landGrades
15. Land-->hasEvent-->LandMark-->landCategory
16. Land-->hasEvent-->LandMark-->landArea
17. Land-->hasEvent-->LandMark-->landRent
18. Land-->hasEvent-->LandRights-->landRightsNumber
19. Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent
20. Land-->hasEvent-->LandRights-->cause
21. Land-->hasEvent-->LandRights-->hasOwner-->Owner
```

---

## 💡 常見錯誤提醒

### 1. 箭頭符號
- ✅ 正確：`-->` （兩個減號 + 一個大於號）
- ❌ 錯誤：`->` （一個減號 + 一個大於號）

### 2. 日期格式
- ✅ 正確：直接填寫 `2022-08-15`
- ❌ 錯誤：欄位名稱包含 `-->year、month、day`

### 3. 大小寫
- ✅ 正確：`Land-->hasEvent-->LandRights-->hasOwner-->Owner`
- ❌ 錯誤：`Land-->hasEvent-->LandRights-->hasOwner-->label`

### 4. 欄位順序
- 所有 21 個欄位必須按照上述順序排列
- 不可增加或減少欄位
- 不可調換順序

---

## 🎯 修正後應該看到

修正後重新上傳，系統應該會顯示：

### 如果還有其他問題
```
❌ 驗證失敗
發現 X 個錯誤，請修正後重新上傳

📊 表頭格式錯誤 [展開查看詳細資訊]
  └─ 具體的錯誤說明...

📋 資料內容錯誤 [展開查看詳細資訊]
  └─ 具體的錯誤說明...
```

### 如果修正成功
```
✅ 驗證通過
共 X 筆資料，0 個錯誤
預計匯入時間：約 Y 分鐘
```

---

## 📞 仍然遇到問題？

如果修正後仍然看到「發現 個錯誤」（錯誤數量顯示異常），可能是：

1. **瀏覽器快取問題**
   - 重新整理頁面（Ctrl + F5 或 Cmd + Shift + R）
   - 清除瀏覽器快取後重試

2. **Firebase 連線問題**
   - 系統會自動使用預設表頭配置
   - 請確認網路連線正常

3. **檔案編碼問題**
   - 確認 Excel 檔案格式為 `.xlsx` 或 `.xls`
   - 避免使用特殊符號或全形字元

---

**最後更新**: 2025-10-01  
**版本**: 1.0.0
