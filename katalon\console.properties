kobitonDeviceId=
remoteWebDriverType=Selenium
adoPlanID=
testcloudMobileId=
deviceId=
testOpsProjectId=
testcloudMobileDeviceId=
testcloudAppId=
testOpsBuildId=
testOps.serverUrl=
testcloudEnvironmentID=
adoTestRunName=
testcloudTunnel=
kobitonUserName=
adoPlanId=
testcloudMobileID=
kobitonToken=
testOps.enabled=
testcloudMobileDeviceID=
testOpsReleaseId=
qTestDestId=
appiumDirectory=
qTestDestType=
testcloudEnvironmentId=
remoteWebDriverUrl=
overrideTestcloudAppId=
