import { allowGetIn } from "../config/App-layout";
import authority from "../config/App-authority";

/**
 * 檢查使用者是否有權限執行特定操作
 * @param {string} action - 操作名稱 (如 'Edit', 'Search' 等)
 * @param {string} userRole - 使用者角色
 * @returns {boolean} - 是否有權限
 */
export const hasPermission = (action, userRole) => {
  const item = {
    authority: authority[action] || [],
  };
  return allowGetIn(item, userRole);
};

/**
 * 檢查使用者是否可以訪問編輯頁面
 * @param {string} userRole - 使用者角色
 * @returns {boolean} - 是否可以訪問編輯頁面
 */
export const canAccessEdit = (userRole) => hasPermission("Edit", userRole);

export default {
  hasPermission,
  canAccessEdit,
};
