// firebase
import { getFirestore, doc, getDoc, setDoc } from "firebase/firestore";
import { fbApp } from "../../Component/Authenticate/FirebaseLayer";

const db = getFirestore(fbApp);
const cloudStorage = {};

/**
 *
 * @param collectName
 * @param docName
 * @param docData
 * @returns {boolean|*}
 */
cloudStorage.insertQuery = (collectName, docName, docData) =>
  setDoc(doc(db, collectName, docName), docData)
    .then(() => true)
    .catch((error) => {
      console.error("cloudStorage.insertQuery:error: ", error);
      return false;
    });

// /**
//  *
//  * @param document
//  * @param id
//  * @returns {boolean|*}
//  */
// cloudStorage.updateQuery = (document, id) => {
//   if (id) {
//     return ref
//       .collection("storedQuery")
//       .doc(id)
//       .update(document)
//       .then(() => true)
//       .catch((error) => {
//         console.error("cloudStorage.updateQuery:error: ", error);
//         return false;
//       });
//   }
//   return false;
// };
//
// /**
//  *
//  * @param id
//  * @returns {boolean|*}
//  */
// cloudStorage.deleteQuery = (id) =>
//   ref
//     .collection("storedQuery")
//     .doc(id)
//     .delete()
//     .then(() => true)
//     .catch((error) => {
//       console.error("cloudStorage.deleteQuery:error: ", error);
//       return false;
//     });

/**
 * get excelHeader list
 * */
cloudStorage.getExcelHeader = () =>
  getDoc(doc(db, "excelHeader", "header"))
    .then((docSnap) => {
      if (docSnap.exists) {
        // Object Convert to array
        return Object.keys(docSnap.data())
          .reduce((acc, nextKey) => {
            const newObj = { id: nextKey, ...docSnap.data()[nextKey] };
            acc.push(newObj);
            return acc;
          }, [])
          .sort((itemA, itemB) => itemA.order - itemB.order);
      }
      return { error: "doc or collection does't exist" };
    })
    .catch((error) => {
      console.error("cloudStorage.deleteQuery:error: ", error);
    });

export default cloudStorage;
