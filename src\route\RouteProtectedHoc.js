import React, { useContext } from "react";
import { Route } from "react-router-dom";
import { bool, any, object } from "prop-types";

import { StoreContext } from "../store/StoreProvider";
import { isEmpty } from "../utils";

const RouteProtectedHoc = ({ component: Component, ...rest }) => {
  // To avoid page go to homepage when refreshing it
  const isLogin = JSON.parse(localStorage.getItem("isLogin"));

  // eslint-disable-next-line no-unused-vars
  const [state, _] = useContext(StoreContext);

  const { user } = state;

  if (isLogin || !isEmpty(user) || rest.public) {
    return <Route {...rest} render={(props) => <Component {...props} />} />;
  }

  return <Route to={{ pathname: "SignIn" }} />;
};

RouteProtectedHoc.propTypes = {
  component: any,
  isLoggedIn: bool,
  rest: object,
  props: object,
};

export default RouteProtectedHoc;
