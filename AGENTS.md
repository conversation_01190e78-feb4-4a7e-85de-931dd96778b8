# Repository Guidelines

## Project Structure & Module Organization
Source lives in `src/` with feature folders: `pages/` for routed views, `Component/` for shared UI (PascalCase matches component names), `api/` for remote calls, `store/` for Redux state, and `utils/` for data helpers. Routing config sits in `src/route/` and global styles in `src/scss/`. Static assets and the HTML shell stay under `public/`. UI automation resources reside in `katalon/`; keep generated reports out of commits by pruning before pushes.

## Build, Test, and Development Commands
Run `npm install` once per clone to hydrate dependencies. Use `npm start` for the CRA dev server with hot reload. `npm test` launches Jest in watch mode; press `a` for the full suite. Build deployable assets with `npm run build`, which outputs to `build/` using the production env vars.

## Coding Style & Naming Conventions
All JavaScript and JSX follow the Airbnb ESLint preset with Prettier integration; run `npx eslint src --ext .js,.jsx` before opening a PR. Keep indentation at two spaces, prefer functional components, and name files with `PascalCase.jsx` for components and `camelCase.js` for utilities. SCSS modules are kebab-case. Co-locate translations and copy near their owning page for easier localization upkeep.

## Testing Guidelines
Unit tests should live beside the implementation as `<name>.test.js` and focus on rendering logic, store interactions, and utility branches. Snapshot tests are acceptable for high-risk UI changes but update them deliberately. Katalon scenarios under `katalon/` cover end-to-end flows; sync with QA before altering shared objects. Always ensure `npm test -- --watch=false` passes locally, and attach relevant Katalon evidence when touching UI workflows.

## Commit & Pull Request Guidelines
Recent history mixes bracketed tags (e.g., `[fix] refine search`) with descriptive Mandarin summaries; keep the first line under ~60 characters and include context in the body if needed. Group work into focused commits. Pull requests must link related issues, describe the solution, list test commands executed, and add screenshots or GIFs for UI-affecting changes. Request review from a domain owner before merging.

## Environment & Configuration
Copy `.env.development` to `.env.local` for local overrides; never hard-code keys. Firebase credentials and GIS endpoints load from env vars consumed in `src/config/` and `src/api/`. For new toggles, document expected defaults in the PR and update the sample env files.