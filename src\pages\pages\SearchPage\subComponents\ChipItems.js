import React, { useContext } from "react";
import { Chip, Grid, Stack, Typography } from "@mui/material";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";

import { StoreContext } from "../../../../store/StoreProvider";

function ChipItems() {
  const [state] = useContext(StoreContext);
  const { searchColList } = state.search;

  const summaries = searchColList
    .map((group) => {
      const selected = group.items.filter((item) => item.select);
      return {
        classType: group.classType,
        classLabel: group.classLabel,
        selectedCount: selected.length,
        totalCount: group.items.length,
      };
    })
    .filter((summary) => summary.selectedCount > 0);

  if (summaries.length === 0) {
    return (
      <Grid item xs={12} className="selectionSummary">
        <Typography variant="body2" color="text.secondary">
          尚未選擇搜尋欄位，請透過左側篩選面板挑選需要的欄位。
        </Typography>
      </Grid>
    );
  }

  return (
    <Grid item xs={12} className="selectionSummary">
      <Stack spacing={1.5}>
        <Typography
          variant="subtitle2"
          color="var(--color-text-secondary)"
        >
          已選欄位摘要
        </Typography>
        <Stack direction="row" flexWrap="wrap" gap={1}>
          {summaries.map((summary) => (
            <Chip
              key={summary.classType}
              color="primary"
              variant="outlined"
              label={`${summary.classLabel}：${summary.selectedCount}/${summary.totalCount}`}
              deleteIcon={<ArrowForwardIcon />}
              onDelete={() => {
                const target = document.querySelector(
                  `[data-filter-group="${summary.classType}"]`
                );
                if (target) {
                  target.scrollIntoView({ behavior: "smooth", block: "start" });
                  target.focus({ preventScroll: true });
                }
              }}
            />
          ))}
        </Stack>
      </Stack>
    </Grid>
  );
}

export default ChipItems;
