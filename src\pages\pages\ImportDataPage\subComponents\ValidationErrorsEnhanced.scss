.validation-errors-enhanced {
  
  .validation-errors-list {
    .MuiAccordion-root {
      box-shadow: none;
      border: 1px solid rgba(0, 0, 0, 0.12);
      
      &:before {
        display: none;
      }
      
      &.Mui-expanded {
        margin: 0 0 8px 0;
      }
    }
    
    code {
      padding: 2px 6px;
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 3px;
      font-family: 'Courier New', monospace;
      font-size: 0.875em;
    }
  }
  
  .validation-errors-table {
    .error-cell {
      background-color: rgba(211, 47, 47, 0.1);
      border: 2px solid #d32f2f;
      font-weight: bold;
    }
    
    .MuiTableCell-head {
      position: sticky;
      top: 0;
      z-index: 10;
    }
  }
}
