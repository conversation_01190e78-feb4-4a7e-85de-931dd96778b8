import React, {useState} from 'react';
import {Box} from "@mui/material";
import HistoryIcon from "@mui/icons-material/History";
import HistoryPopper from "../PopWindow/HistoryPopper";

function HistoryButton() {

    const [historyIconPos, setHistoryIconPos] = useState(null);
    const [openHistoryPopper, setOpenHistoryPopper] = useState(false);

    const historyPopperControl = (event) => {
        setHistoryIconPos(event.currentTarget);
        setOpenHistoryPopper(!openHistoryPopper);
    };

    return (
        <Box style={{cursor: "pointer"}}>
            <HistoryIcon fontSize="large" onClick={historyPopperControl}/>
            <HistoryPopper
                historyIconPos={historyIconPos}
                openHistoryPopper={openHistoryPopper}
                setOpenHistoryPopper={setOpenHistoryPopper}
            />
        </Box>
    );
}

export default HistoryButton;