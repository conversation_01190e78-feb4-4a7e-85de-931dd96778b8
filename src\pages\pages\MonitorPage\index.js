import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Snackbar,
  Stack,
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import DashboardIcon from '@mui/icons-material/Dashboard';
import EditNoteIcon from '@mui/icons-material/EditNote';

// Styles
import './MonitorPage.scss';

// API
import { Api, readOntoData } from '../../../api/land/Api';

// Components
import CustomLoading from '../../../Component/CustomLoading/CustomLoading';
import EditSessionList from './subComponents/EditSessionList';

// Utils
import { formatFullDateTime } from '../../../utils/timeUtils';

/**
 * MonitorPage - 編輯監控頁面
 * 顯示當前正在編輯的土地資料和編輯者資訊
 * 僅 Admin 和 Developer 角色可訪問
 */
function MonitorPage() {
  // State management
  const [editSessions, setEditSessions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  /**
   * 獲取所有編輯中的土地資料
   */
  const fetchEditSessions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const apiStr = Api.getLandLockList();
      const response = await readOntoData(apiStr);

      if (response.error) {
        throw new Error(response.error);
      }

      // response.data 格式：
      // [{ landId, lockUser, lockStartTime, landName, landSerialNumber }]
      setEditSessions(response.data || []);
      setLastUpdate(Date.now());
    } catch (err) {
      setError(err.message || '獲取資料時發生錯誤');
      setSnackbar({
        open: true,
        message: '獲取資料失敗，請稍後再試',
        severity: 'error',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  /**
   * 手動刷新資料
   */
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    fetchEditSessions().then(() => {
      setSnackbar({
        open: true,
        message: '資料已更新',
        severity: 'success',
      });
    });
  }, [fetchEditSessions]);

  /**
   * 關閉提示訊息
   */
  const handleCloseSnackbar = useCallback(() => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  }, []);

  // 初始載入資料
  useEffect(() => {
    fetchEditSessions();
  }, [fetchEditSessions]);

  return (
    <Box className="monitor-page">
      {/* 標題區 */}
      <Box className="monitor-header">
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          spacing={2}
          sx={{ mb: 3 }}
        >
          <Box>
            <Typography 
              variant="h4" 
              component="h1" 
              gutterBottom
              sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: 1.5,
                color: 'var(--color-primary)'
              }}
            >
              <DashboardIcon sx={{ fontSize: '2rem', color: 'var(--color-info)' }} />
              編輯追蹤監控
            </Typography>
            {lastUpdate && (
              <Typography variant="body2" color="text.secondary">
                最後更新：{formatFullDateTime(lastUpdate)}
              </Typography>
            )}
          </Box>

          {/* 刷新按鈕 */}
          <Button
            variant="outlined"
            startIcon={
              refreshing ? (
                <CircularProgress size={20} />
              ) : (
                <RefreshIcon />
              )
            }
            onClick={handleRefresh}
            disabled={refreshing || loading}
            sx={{
              minWidth: 120,
              ...(refreshing && {
                '& .MuiCircularProgress-root': {
                  animation: 'spin 1s linear infinite',
                },
              }),
            }}
          >
            {refreshing ? '更新中...' : '刷新'}
          </Button>
        </Stack>
      </Box>

      {/* 內容區 */}
      <Card elevation={2}>
        <CardContent>
          {loading && <CustomLoading />}

          {!loading && error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {!loading && !error && (
            <>
              {/* 狀態摘要 */}
              <Box sx={{ mb: 3 }}>
                <Typography 
                  variant="h6" 
                  gutterBottom
                  sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: 1,
                    color: 'var(--color-text-primary)'
                  }}
                >
                  <EditNoteIcon sx={{ color: 'var(--color-success)' }} />
                  EditPage 編輯狀態
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {editSessions.length > 0
                    ? `目前有 ${editSessions.length} 人正在編輯土地資料`
                    : '目前沒有人在編輯'}
                </Typography>
              </Box>

              {/* 編輯狀態列表 */}
              <EditSessionList sessions={editSessions} />
            </>
          )}
        </CardContent>
      </Card>

      {/* 提示訊息 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default MonitorPage;
