import React, { useContext, useEffect, useState } from "react";

import "./AuthorityPage.scss";
import { Box, Tabs, Tab, ToggleButtonGroup, ToggleButton } from "@mui/material";
import ValidUser from "./subComponents/ValidUser";
import InvalidUser from "./subComponents/InvalidUser";
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";
import {
  getUsers,
  getClientUsers,
} from '../../../Component/Authenticate/firebase/realtimeDatabase';
import { isEmpty } from '../../../utils';
import NoUser from './subComponents/NoUser';
// import FrontendAuthorityPlaceholder from "./subComponents/FrontendAuthorityPlaceholder";
import FrontendAuthority from './subComponents/FrontendAuthority';

function AuthorityPage() {
  const [scopeTab, setScopeTab] = useState(0);
  const [dashboardStatus, setDashboardStatus] = useState("valid");

  const [state, dispatch] = useContext(StoreContext);
  const { userInfo, roles } = state.authority;
  const [validUser, setValidUser] = useState([]);
  const [invalidUser, setInvalidUser] = useState([]);

  useEffect(() => {
    dispatch({
      type: Act.SET_AUTPAGELOADING,
      payload: true,
    });
    getUsers().then((result = {}) => {
      const tmpUserInfo = [];
      Object.keys(result).forEach((id) => {
        tmpUserInfo.push(result[id]);
      });
      dispatch({
        type: Act.SET_AUTPAGELOADING,
        payload: false,
      });
      dispatch({
        type: Act.SET_USERINFO,
        payload: tmpUserInfo,
      });
    });
    getClientUsers().then((result = {}) => {
      const tmpClientUsers = [];
      Object.keys(result).forEach((id) => {
        tmpClientUsers.push(result[id]);
      });
      dispatch({
        type: Act.SET_CLIENTUSERINFO,
        payload: tmpClientUsers,
      });
    });
  }, []);

  useEffect(() => {
    const tmpArr = userInfo.filter((info) => {
      let exist = false;
      roles.forEach((role) => {
        if (info.role.indexOf(role.toLowerCase()) >= 0) {
          exist = true;
        }
      });
      return exist;
    });
    setValidUser(tmpArr);

    const tmpArr2 = userInfo.filter((info) => {
      let exist = true;
      roles.forEach((role) => {
        if (info.role.indexOf(role.toLowerCase()) >= 0) {
          exist = false;
        }
      });
      return exist;
    });
    setInvalidUser(tmpArr2);
  }, [userInfo, roles]);

  const handleDashboardStatusChange = (event, newValue) => {
    if (newValue !== null) {
      setDashboardStatus(newValue);
    }
  };

  return (
    <Box className="AuthorityPage mainBox_shadowMain">
      <Box className="AuthorityPage__tabWrapper" sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={scopeTab}
          onChange={(event, newValue) => setScopeTab(newValue)}
          variant="fullWidth"
        >
          <Tab label="Dashboard 權限" />
          <Tab label="前台權限" />
        </Tabs>
      </Box>

      {scopeTab === 0 && (
        <Box className="AuthorityPage__panel">
          <Box className="AuthorityPage__filterBar">
            <ToggleButtonGroup
              value={dashboardStatus}
              exclusive
              onChange={handleDashboardStatusChange}
              size="small"
              color="primary"
            >
              <ToggleButton value="valid">已授權</ToggleButton>
              <ToggleButton value="invalid">待授權</ToggleButton>
            </ToggleButtonGroup>
          </Box>

          {dashboardStatus === 'valid' && !isEmpty(validUser) && <ValidUser />}
          {dashboardStatus === 'valid' && isEmpty(validUser) && <NoUser name="使用者" />}

          {dashboardStatus === 'invalid' && !isEmpty(invalidUser) && <InvalidUser />}
          {dashboardStatus === 'invalid' && isEmpty(invalidUser) && <NoUser name="申請人" />}
        </Box>
      )}

      {scopeTab === 1 && <FrontendAuthority />}
    </Box>
  );
}

export default AuthorityPage;
