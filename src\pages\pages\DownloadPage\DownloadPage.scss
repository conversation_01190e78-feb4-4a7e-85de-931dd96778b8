@use "../../../scss/common";

// Download Page 統一樣式
.DownloadPage {
  // 統一的 Tab 樣式
  .MuiTabs-root {
    .MuiTab-root {
      text-transform: none;
      font-weight: 600;
      font-size: 1rem;
      color: var(--color-text-secondary);
      
      &.Mui-selected {
        color: var(--color-primary);
      }
    }
    
    .MuiTabs-indicator {
      background-color: var(--color-primary);
      height: 3px;
      border-radius: 2px;
    }
  }
}

// LandData 組件樣式
.LandData {
  padding: 24px 0;
  
  .topArea {
    background: var(--color-surface);
    border-radius: 12px;
    border: 1px solid var(--color-border-light);
    
    .MuiGrid-container {
      padding: 16px;
    }
  }
  
  .InputColumn {
    display: flex;
    justify-content: center;
    margin-top: 16px;
  }
  
  .DownLoadBtn {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-top: 16px;
  }
}

// EditorLand 組件樣式
.EditorLand {
  padding: 24px 0;
  
  .topArea {
    background: var(--color-surface);
    border-radius: 12px 12px 0 0;
    border: 1px solid var(--color-border-light);
    border-bottom: none;
  }
  
  .middleArea {
    background: var(--color-surface-alt);
    border-left: 1px solid var(--color-border-light);
    border-right: 1px solid var(--color-border-light);
  }
  
  .bottomArea {
    background: var(--color-surface);
    border-radius: 0 0 12px 12px;
    border: 1px solid var(--color-border-light);
    border-top: none;
    justify-content: flex-end;
  }
  
  // Material-UI TextField 日期選擇器樣式
  .MuiTextField-root {
    .MuiInputLabel-root {
      color: var(--color-text-secondary);
      font-weight: 500;
    }
    
    .MuiOutlinedInput-root {
      background-color: var(--color-surface);
      
      &.Mui-focused {
        .MuiOutlinedInput-notchedOutline {
          border-color: var(--color-primary);
          border-width: 2px;
        }
      }
      
      &:hover:not(.Mui-focused) {
        .MuiOutlinedInput-notchedOutline {
          border-color: var(--color-primary);
        }
      }
    }
  }
}

// 統一的分隔線樣式
.MuiDivider-root {
  background-color: var(--color-border-light);
  margin: 8px 0;
}
