import React, { useContext, useEffect, useState } from "react";
import Pagination from "@mui/material/Pagination";
import "./CustomPagination.css";
import { StoreContext } from "../../store/StoreProvider";
import { isEmpty } from "../../utils";
import Act from "../../store/actions";

function CustomPagination({ pageShowCount }) {
  const [state, dispatch] = useContext(StoreContext);
  const { resultList, pageNumber } = state.search;
  const [pageTotalCount, setPageTotalCount] = useState(0);

  useEffect(() => {
    if (isEmpty(resultList)) return;
    const mod5 = resultList.length % pageShowCount;
    if (mod5 === 0) {
      setPageTotalCount(Math.floor(resultList.length / pageShowCount));
    } else {
      setPageTotalCount(Math.floor(resultList.length / pageShowCount) + 1);
    }
  }, [resultList]);

  const handlePageChange = (event, value) => {
    dispatch({
      type: Act.SET_PAGENUMBER,
      payload: value,
    });
  };

  return (
    <div className="CustomPagination">
      <Pagination
        count={pageTotalCount}
        page={pageNumber}
        onChange={handlePageChange}
      />
    </div>
  );
}

export default CustomPagination;
