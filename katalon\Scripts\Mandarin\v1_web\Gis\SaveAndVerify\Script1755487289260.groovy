import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject
import static com.kms.katalon.core.testobject.ObjectRepository.findWindowsObject
import com.kms.katalon.core.checkpoint.Checkpoint as Checkpoint
import com.kms.katalon.core.cucumber.keyword.CucumberBuiltinKeywords as CucumberKW
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling as FailureHandling
import com.kms.katalon.core.testcase.TestCase as TestCase
import com.kms.katalon.core.testdata.TestData as TestData
import com.kms.katalon.core.testng.keyword.TestNGBuiltinKeywords as TestNGKW
import com.kms.katalon.core.testobject.TestObject as TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI
import com.kms.katalon.core.windows.keyword.WindowsBuiltinKeywords as Windows
import internal.GlobalVariable as GlobalVariable
import org.openqa.selenium.Keys as Keys

// 打開save modal
WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Gis/button_save'))
// 等待save modal 內的儲存按鈕出現
WebUI.waitForElementVisible(findTestObject('Object Repository/Mandarin/v1_web/Gis/save_modal_save_button'), 5)
//WebUI.acceptAlert()
// 點save modal 內的儲存按鈕
WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Gis/save_modal_save_button'))

// 驗證輸入值是否正確
WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/119-1_twd97_x'), 'value'), '148485')
WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/119-1_twd97_y'), 'value'), '2406567')
WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/1261_twd97_x'), 'value'), '148485')
WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/1261_twd97_y'), 'value'), '2406567')
WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Gis/pagination'))

WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/249_twd97_x'), 'value'), '148485')
WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/249_twd97_y'), 'value'), '2406567')
