import React, { useMemo } from "react";

// material ui
import Button from "@mui/material/Button";
import DownloadIcon from "@mui/icons-material/Download";

// excel
import ReactExport from "react-data-export";

const ExcelMultiSheet = ({ filename, excelData }) => {
  const { ExcelFile } = ReactExport;
  const { ExcelSheet, ExcelColumn } = ReactExport.ExcelFile;

  // 表單的排序
  const sheetOrder = Object.keys(excelData).sort(
    (stA, stB) => excelData[stA].order - excelData[stB].order
  );

  const MemoziedExcelFile = useMemo(
    () => (
      <ExcelFile 
        filename={filename}
        element={
          <Button
            variant="contained"
            color="success"
            size="medium"
            startIcon={<DownloadIcon />}
            sx={{
              minWidth: 120,
              fontWeight: 600,
              boxShadow: 2,
              textTransform: 'none',
              fontSize: '0.95rem',
              px: 3,
              py: 1,
              borderRadius: 2,
              backgroundColor: '#4caf50',
              '&:hover': {
                backgroundColor: '#45a049',
                boxShadow: 4,
                transform: 'translateY(-1px)',
              },
              transition: 'all 0.2s ease',
            }}
          >
            下載 Excel
          </Button>
        }
      >
        {sheetOrder.map((stName) => {
          let { headers } = excelData[stName];
          let newData = excelData[stName].data;

          // 空表單，幫忙補值。
          if (!newData) {
            newData = [];
            newData[0] = {};
            headers.forEach((h) => {
              newData[0][h.id] = "";
            });
          }

          // headers 排序
          headers = headers.sort((itemA, itemB) => itemA.seq - itemB.seq);

          return (
            <ExcelSheet key={stName} data={newData} name={stName}>
              {headers.map((item) => (
                <ExcelColumn key={item.id} label={item.label} value={item.id} />
              ))}
            </ExcelSheet>
          );
        })}
      </ExcelFile>
    ),
    [filename, excelData]
  );
  return MemoziedExcelFile;
};

export default ExcelMultiSheet;
