# 權限管控規則文件

**最後更新**: 2025-10-02  
**版本**: 1.0.0

---

## 📋 目錄

1. [快速開始](#快速開始)
2. [角色定義](#角色定義)
3. [環境定義](#環境定義)
4. [權限矩陣](#權限矩陣)
5. [實作指南](#實作指南)
6. [API 參考](#api-參考)
7. [常見問題](#常見問題)

---

## 🚀 快速開始

### 5 分鐘上手權限控制

#### 1. Import 權限工具
```javascript
import { canEdit, getPermissionMessage, OPERATION } from '../../../utils/permissionUtils';
```

#### 2. 檢查權限
```javascript
const [state] = useContext(StoreContext);
const { user } = state;

if (!canEdit(user)) {
  alert(getPermissionMessage(user, OPERATION.EDIT));
  return;
}
```

#### 3. 控制 UI 元素
```javascript
<Button 
  disabled={!canEdit(user)}
  onClick={handleEdit}
>
  編輯
</Button>
```

---

## 👥 角色定義

### 系統支援的三種角色

| 角色 | 英文 | 權限範圍 | 典型使用場景 |
|------|------|----------|------------|
| **管理員** | `admin` | 最高權限 | 系統管理、使用者管理 |
| **開發者** | `developer` | 環境限制 | 開發測試、座標編輯 |
| **編輯者** | `editor` | 內容編輯 | 資料編輯、內容維護 |

### 角色儲存格式

Firebase `roleDev` 欄位：
```javascript
// 單一角色
"admin"

// 多角色（逗號分隔，無空格）
"admin,developer"
"developer,editor"
"admin,editor,developer"
```

### 角色優先級

當使用者擁有多個角色時，權限以**最高權限**為準：

```
admin > developer > editor
```

**範例**：
- 使用者有 `"developer,editor"` → 擁有 developer 的所有權限
- 使用者有 `"admin,developer"` → 擁有 admin 的所有權限

---

## 🌍 環境定義

### 兩種環境

| 環境 | 英文 | 環境變數 | Badge 顏色 | 權限特性 |
|------|------|---------|-----------|----------|
| **正式站** | Production | `REACT_APP_ENV=production` | 🔴 紅色 | 嚴格限制 |
| **測試站** | Development | `REACT_APP_ENV=development` | 🔵 藍色 | 寬鬆測試 |

### 環境判斷

```javascript
import { getCurrentEnvironment } from '../../../utils/environmentUtils';

const env = getCurrentEnvironment();
// 返回: 'production' 或 'development'

const isProduction = env === 'production';
```

### 環境配置

**.env.development**:
```bash
REACT_APP_ENV=development
```

**.env.production**:
```bash
REACT_APP_ENV=production
```

---

## 📊 權限矩陣

### 完整權限對照表

| 功能頁面 | Admin | Developer (測試站) | Developer (正式站) | Editor | 遊客 |
|---------|-------|-------------------|-------------------|--------|------|
| **EditPage** (地號編輯) | ✅ | ✅ | ❌ | ✅ | ❌ |
| **GisPage** (座標編輯) | ✅ | ✅ | ❌ | ✅ | ❌ |
| **ImportDataPage** (匯入資料) | ✅ | ✅ | ✅ | ❌ | ❌ |
| **AuthorityPage** (使用者管理) | ✅ | ❌ | ❌ | ❌ | ❌ |
| **MonitorPage** (監控查看) | ✅ | ✅ | ✅ | ✅ | ✅ |

### 權限說明

#### EditPage & GisPage (編輯權限)
```javascript
✅ Admin: 所有環境都可以編輯
✅ Editor: 所有環境都可以編輯
⚠️ Developer: 僅測試站可以編輯，正式站唯讀
❌ 其他: 無法編輯
```

#### ImportDataPage (匯入權限)
```javascript
✅ Admin: 所有環境都可以匯入
✅ Developer: 所有環境都可以匯入
❌ Editor: 無法匯入（沒有資料匯入權限）
❌ 其他: 無法匯入
```

#### AuthorityPage (管理權限)
```javascript
✅ Admin: 可以管理所有使用者
❌ 其他: 唯讀，無法修改使用者權限
```

#### MonitorPage (查看權限)
```javascript
✅ 所有人: 都可以查看
```

---

## 💻 實作指南

### 1. 頁面級權限控制

#### 方法 A: 完全阻擋頁面訪問

```javascript
import { canImport } from '../../../utils/permissionUtils';
import { StoreContext } from '../../../store/StoreProvider';

function ImportDataPage() {
  const [state] = useContext(StoreContext);
  const { user } = state;

  // 檢查權限
  if (!canImport(user)) {
    return (
      <div className="no-permission">
        <h2>權限不足</h2>
        <p>您沒有匯入資料的權限</p>
      </div>
    );
  }

  return (
    <div className="ImportDataPage">
      {/* 頁面內容 */}
    </div>
  );
}
```

#### 方法 B: 唯讀模式（推薦用於複雜頁面）

```javascript
import { canEdit } from '../../../utils/permissionUtils';

function EditPage() {
  const [state] = useContext(StoreContext);
  const { user } = state;
  const hasEditPermission = canEdit(user);

  return (
    <div className="EditPage">
      {/* 所有互動元素都加上 disabled */}
      <Button disabled={!hasEditPermission}>編輯</Button>
      <Button disabled={!hasEditPermission}>儲存</Button>
      <TextField disabled={!hasEditPermission} />
    </div>
  );
}
```

---

### 2. 按鈕級權限控制

#### 雙重防護（推薦）

UI 層 + 邏輯層雙重檢查：

```javascript
import { canEdit, getPermissionMessage, OPERATION } from '../../../utils/permissionUtils';

function SaveButton() {
  const [state] = useContext(StoreContext);
  const { user } = state;
  const hasEditPermission = canEdit(user);

  const handleSave = () => {
    // 邏輯層檢查（必須）
    if (!hasEditPermission) {
      alert(getPermissionMessage(user, OPERATION.EDIT));
      return;
    }

    // 執行儲存邏輯
    saveData();
  };

  return (
    <Button 
      // UI 層控制（建議）
      disabled={!hasEditPermission}
      onClick={handleSave}
    >
      儲存
    </Button>
  );
}
```

#### 為什麼需要雙重防護？

1. **UI disabled**: 提供視覺反饋，使用者知道按鈕無法使用
2. **邏輯檢查**: 防止繞過 UI 的惡意操作（如瀏覽器開發工具）

---

### 3. 使用 Snackbar 取代 alert

#### EditPage 範例（已整合）

```javascript
import PermissionSnackbar from '../../../Component/PermissionSnackbar';
import usePermissionSnackbar from '../../../Component/PermissionSnackbar/usePermissionSnackbar';

function EditPage() {
  const [state] = useContext(StoreContext);
  const { user } = state;
  
  // 初始化 Snackbar Hook
  const { snackbarState, showError, hideSnackbar } = usePermissionSnackbar();

  const handleSave = () => {
    if (!canEdit(user)) {
      // 使用 Snackbar 取代 alert
      showError(getPermissionMessage(user, OPERATION.EDIT));
      return;
    }
    // 儲存邏輯
  };

  return (
    <div className="EditPage">
      <ButtonArea showPermissionError={showError} />
      
      {/* Snackbar 組件 */}
      <PermissionSnackbar
        open={snackbarState.open}
        onClose={hideSnackbar}
        message={snackbarState.message}
        severity={snackbarState.severity}
      />
    </div>
  );
}
```

---

### 4. 頁面級權限提示（PermissionHint）

```javascript
import PermissionHint from '../../../Component/PermissionHint';
import usePermissionHint from '../../../Component/PermissionHint/usePermissionHint';

function EditPage() {
  const [state] = useContext(StoreContext);
  const { user } = state;
  
  // 自動生成權限提示
  const permissionHint = usePermissionHint(user, 'edit');

  return (
    <div className="EditPage">
      {/* 頁面頂部權限提示 */}
      <PermissionHint
        show={permissionHint.shouldShow}
        title={permissionHint.title}
        message={permissionHint.message}
        severity={permissionHint.severity}
      />
      
      {/* 頁面內容 */}
      <div className="content">...</div>
    </div>
  );
}
```

---

## 📚 API 參考

### 權限檢查函數 (permissionUtils.js)

#### `canEdit(user)`
檢查使用者是否有編輯權限

**參數**:
- `user` (Object): 使用者物件，需包含 `roleDev` 欄位

**返回值**:
- `boolean`: true = 有權限, false = 無權限

**規則**:
- Admin: ✅ 所有環境
- Editor: ✅ 所有環境
- Developer: ✅ 測試站, ❌ 正式站
- 其他: ❌ 無權限

**範例**:
```javascript
import { canEdit } from '../../../utils/permissionUtils';

const hasPermission = canEdit(user);
if (!hasPermission) {
  console.log('無編輯權限');
}
```

---

#### `canImport(user)`
檢查使用者是否有匯入資料權限

**規則**:
- Admin: ✅ 所有環境
- Developer: ✅ 所有環境
- Editor: ❌ 無權限
- 其他: ❌ 無權限

**範例**:
```javascript
import { canImport } from '../../../utils/permissionUtils';

if (canImport(user)) {
  // 顯示匯入功能
}
```

---

#### `canManageUsers(user)`
檢查使用者是否有管理使用者權限

**規則**:
- Admin: ✅ 所有環境
- 其他: ❌ 無權限

**範例**:
```javascript
import { canManageUsers } from '../../../utils/permissionUtils';

if (canManageUsers(user)) {
  // 顯示使用者管理功能
}
```

---

#### `getPermissionMessage(user, operation)`
取得權限錯誤訊息

**參數**:
- `user` (Object): 使用者物件
- `operation` (String): 操作類型，使用 `OPERATION` 常數

**返回值**:
- `string`: 格式化的權限錯誤訊息

**OPERATION 常數**:
```javascript
import { OPERATION } from '../../../utils/permissionUtils';

OPERATION.EDIT          // "編輯"
OPERATION.IMPORT        // "匯入資料"
OPERATION.MANAGE_USERS  // "管理使用者"
```

**訊息格式**:
```
您的角色（開發者）在正式站環境下沒有編輯權限。
如需協助，請聯絡系統管理員。
```

**範例**:
```javascript
import { getPermissionMessage, OPERATION } from '../../../utils/permissionUtils';

const message = getPermissionMessage(user, OPERATION.EDIT);
alert(message);
```

---

#### `getUserRoleDisplayText(user)`
取得使用者角色的中文顯示文字

**參數**:
- `user` (Object): 使用者物件

**返回值**:
- `string`: 中文角色名稱，多角色用頓號分隔

**範例**:
```javascript
import { getUserRoleDisplayText } from '../../../utils/permissionUtils';

// 單一角色
console.log(getUserRoleDisplayText({ roleDev: 'admin' }));
// 輸出: "管理員"

// 多角色
console.log(getUserRoleDisplayText({ roleDev: 'admin,developer' }));
// 輸出: "管理員、開發者"
```

---

### 環境工具函數 (environmentUtils.js)

#### `getCurrentEnvironment()`
取得當前環境

**返回值**:
- `'production'` 或 `'development'`

**範例**:
```javascript
import { getCurrentEnvironment } from '../../../utils/environmentUtils';

const env = getCurrentEnvironment();
if (env === 'production') {
  console.log('這是正式站');
}
```

---

#### `getEnvironmentBadge()`
取得環境 Badge 配置

**返回值**:
```javascript
{
  label: "正式站" | "測試站",
  color: "#c62828" | "#1565c0",
  bgColor: "#ffebee" | "#e3f2fd"
}
```

**範例**:
```javascript
import { getEnvironmentBadge } from '../../../utils/environmentUtils';

const badge = getEnvironmentBadge();
console.log(badge.label);  // "正式站" 或 "測試站"
```

---

### Snackbar Hook (usePermissionSnackbar)

#### 基本用法

```javascript
import usePermissionSnackbar from '../../../Component/PermissionSnackbar/usePermissionSnackbar';

function MyComponent() {
  const { 
    snackbarState,      // 狀態物件
    showError,          // 顯示錯誤
    showWarning,        // 顯示警告
    showInfo,           // 顯示提示
    showSuccess,        // 顯示成功
    hideSnackbar        // 關閉
  } = usePermissionSnackbar();

  const handleClick = () => {
    showError('權限不足');
  };

  return (
    <>
      <button onClick={handleClick}>測試</button>
      <PermissionSnackbar
        open={snackbarState.open}
        onClose={hideSnackbar}
        message={snackbarState.message}
        severity={snackbarState.severity}
      />
    </>
  );
}
```

---

### PermissionHint Hook (usePermissionHint)

#### 頁面類型

| pageType | 檢查權限 | 適用頁面 |
|----------|---------|---------|
| `'edit'` | `canEdit()` | EditPage, GisPage |
| `'import'` | `canImport()` | ImportDataPage |
| `'manage'` | `canManageUsers()` | AuthorityPage |
| `'view'` | 無限制 | MonitorPage |

#### 基本用法

```javascript
import usePermissionHint from '../../../Component/PermissionHint/usePermissionHint';

function EditPage() {
  const [state] = useContext(StoreContext);
  const { user } = state;
  
  const hint = usePermissionHint(user, 'edit');
  
  return (
    <div>
      <PermissionHint
        show={hint.shouldShow}
        title={hint.title}
        message={hint.message}
        severity={hint.severity}
      />
    </div>
  );
}
```

---

## ❓ 常見問題

### Q1: 如何判斷使用者是否有多個角色？

```javascript
const roles = user.roleDev?.split(',') || [];
const hasMultipleRoles = roles.length > 1;

console.log(roles);  // ['admin', 'developer']
```

---

### Q2: 如何檢查使用者是否有特定角色？

```javascript
const hasAdminRole = user.roleDev?.includes('admin');
const hasDeveloperRole = user.roleDev?.includes('developer');
```

---

### Q3: 如何在正式站和測試站顯示不同的行為？

```javascript
import { getCurrentEnvironment } from '../../../utils/environmentUtils';

const isProduction = getCurrentEnvironment() === 'production';

if (isProduction) {
  // 正式站邏輯
  console.log('正式站：嚴格模式');
} else {
  // 測試站邏輯
  console.log('測試站：寬鬆模式');
}
```

---

### Q4: 為什麼 Developer 在正式站不能編輯？

**設計理由**:
1. **資料安全**: 防止開發人員意外修改正式站資料
2. **權責分離**: 編輯正式站資料應由 Editor 或 Admin 負責
3. **測試環境**: Developer 可以在測試站自由測試功能

**解決方案**:
- 如果 Developer 需要在正式站編輯，請聯絡 Admin 添加 `editor` 角色
- 多角色範例: `"developer,editor"`

---

### Q5: 如何測試不同角色的權限？

#### 方法 1: Firebase Console 修改

1. 登入 Firebase Console
2. 找到 Realtime Database
3. 找到對應使用者的 `roleDev` 欄位
4. 修改為測試角色（如 `"developer"` 或 `"editor"`）
5. 重新整理頁面

#### 方法 2: 使用測試帳號

建立不同角色的測試帳號：
- `<EMAIL>` → `roleDev: "admin"`
- `<EMAIL>` → `roleDev: "developer"`
- `<EMAIL>` → `roleDev: "editor"`

---

### Q6: 權限檢查失敗時應該怎麼處理？

**推薦處理方式**（按優先級）:

1. **Snackbar 通知**（推薦）:
```javascript
showError(getPermissionMessage(user, OPERATION.EDIT));
```

2. **PermissionHint 提示**（頁面級）:
```javascript
const hint = usePermissionHint(user, 'edit');
<PermissionHint {...hint} />
```

3. **Alert 彈窗**（舊版）:
```javascript
alert(getPermissionMessage(user, OPERATION.EDIT));
```

---

### Q7: 如何為新頁面加入權限控制？

**步驟**:

1. **Import 權限工具**:
```javascript
import { canEdit, getPermissionMessage, OPERATION } from '../../../utils/permissionUtils';
```

2. **取得使用者物件**:
```javascript
const [state] = useContext(StoreContext);
const { user } = state;
```

3. **檢查權限**:
```javascript
const hasPermission = canEdit(user);  // 或 canImport, canManageUsers
```

4. **控制 UI**:
```javascript
<Button disabled={!hasPermission} onClick={handleAction}>
  操作按鈕
</Button>
```

5. **邏輯檢查**:
```javascript
const handleAction = () => {
  if (!hasPermission) {
    alert(getPermissionMessage(user, OPERATION.EDIT));
    return;
  }
  // 執行操作
};
```

---

### Q8: 權限系統的效能考量？

**最佳實踐**:

1. **使用 useMemo 快取權限檢查結果**:
```javascript
const hasEditPermission = useMemo(() => canEdit(user), [user]);
```

2. **避免在 render 中重複檢查**:
```javascript
// ❌ Bad
return (
  <div>
    {canEdit(user) && <EditButton />}
    {canEdit(user) && <SaveButton />}
    {canEdit(user) && <DeleteButton />}
  </div>
);

// ✅ Good
const hasEditPermission = useMemo(() => canEdit(user), [user]);
return (
  <div>
    {hasEditPermission && <EditButton />}
    {hasEditPermission && <SaveButton />}
    {hasEditPermission && <DeleteButton />}
  </div>
);
```

---

### Q9: 如何除錯權限問題？

**除錯技巧**:

```javascript
import { canEdit, getCurrentEnvironment } from '../../../utils/...';

// 1. 檢查使用者物件
console.log('User object:', user);
console.log('Role Dev:', user.roleDev);

// 2. 檢查當前環境
console.log('Current environment:', getCurrentEnvironment());

// 3. 檢查權限結果
console.log('Can edit:', canEdit(user));

// 4. 檢查角色陣列
const roles = user.roleDev?.split(',') || [];
console.log('User roles:', roles);

// 5. 檢查環境變數
console.log('REACT_APP_ENV:', process.env.REACT_APP_ENV);
```

---

### Q10: 權限系統的安全性考量？

**安全原則**:

1. ✅ **永遠在後端驗證權限** - 前端權限檢查只是 UI 層的輔助
2. ✅ **雙重防護** - UI disabled + 邏輯檢查
3. ✅ **最小權限原則** - 預設拒絕，明確授權
4. ✅ **審計日誌** - 記錄重要操作（未來實作）

**不要僅依賴前端權限檢查！**

---

## 📝 檢查清單

### 新增權限控制時的檢查項目

- [ ] Import 權限工具函數
- [ ] 取得使用者物件
- [ ] 加入權限檢查邏輯
- [ ] 控制 UI 元素 disabled 狀態
- [ ] 顯示權限錯誤訊息
- [ ] 使用 useMemo 優化效能
- [ ] 加入註解說明權限規則
- [ ] 測試不同角色的行為
- [ ] 測試不同環境的行為
- [ ] 更新文檔

---

## 🔗 相關文檔

- [Phase 1 Summary](./Phase1-Summary.md) - 基礎設施
- [Phase 2 Summary](./Phase2-Summary.md) - 頁面權限控制
- [Phase 3 Summary](./Phase3-Summary.md) - UI/UX 優化
- [PermissionSnackbar README](../src/Component/PermissionSnackbar/README.md)
- [PermissionHint README](../src/Component/PermissionHint/README.md)
- [Final Implementation Report](./Final-Implementation-Report.md)

---

## 📞 聯絡與支援

如有權限系統相關問題，請聯絡：
- 系統管理員
- 技術負責人

---

**文檔版本**: 1.0.0  
**最後更新**: 2025-10-02  
**維護者**: Development Team
