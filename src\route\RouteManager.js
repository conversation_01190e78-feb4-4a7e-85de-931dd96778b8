import React, { useContext } from "react";

import { Switch, Route } from "react-router-dom";

import RouteProtectedHoc from "./RouteProtectedHoc";
//
import { StoreContext } from "../store/StoreProvider";
//
import { allowGetIn, routes } from "../config/App-layout";
import NotAllow from "../pages/main/NotAllow";
import NotFound from "../pages/main/NotFound";

const RouteManager = () => {
  // store
  const [state] = useContext(StoreContext);
  const { user } = state;
  const { role } = user;

  return (
    <Switch>
      {routes &&
        routes
          .filter((route) => route.public)
          .map((route) => {
            if (allowGetIn(route, role)) {
              return (
                <RouteProtectedHoc
                  exact
                  key={route.id}
                  path={route.path}
                  public={route.public}
                  component={route.component}
                />
              );
            }
            return (
              <Route
                exact
                key={route.id}
                path={route.path}
                component={NotAllow}
              />
            );
          })}
      <Route component={NotFound} />
    </Switch>
  );
};
export default RouteManager;
