import React, { useContext, useEffect, useState } from "react";

// 3rd party
import { useDropzone } from "react-dropzone";
import * as Excel from "exceljs";

// scss
import "./ImportDataPage.scss";

// material ui
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Input from "@mui/material/Input";
import Typography from "@mui/material/Typography";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import FolderIcon from "@mui/icons-material/Folder";
import LinearProgress from "@mui/material/LinearProgress";
import Alert from "@mui/material/Alert";
import AlertTitle from "@mui/material/AlertTitle";
import Button from "@mui/material/Button";
import DownloadIcon from "@mui/icons-material/Download";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import LightbulbIcon from "@mui/icons-material/Lightbulb";

// components
import ImportDataBtn from "./subComponents/ImportDataBtn";
import ValidationErrorsEnhanced from "./subComponents/ValidationErrorsEnhanced";

// store
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";

// permission
import { canImport, getUserRoleDisplayText } from "../../../utils/permissionUtils";
import { getEnvironmentBadge } from "../../../utils/environmentUtils";

// firebase
import cloudStorage from "../../../api/firebase/cloudFirestore";

// utils
import { isEmpty } from "../../../utils";
import checkHeader from "./utils/excelHeader/checkHeader";
import checkBody from "./utils/excelBody/checkBody";
import { 
  generateTemplate, 
  generateExample, 
  downloadFile,
  DEFAULT_HEADERS 
} from "./utils/excelTemplate/generateTemplate";

// config
import { 
  checkRes, 
  specRow, 
  VALIDATION_STAGES, 
  VALIDATION_STAGE_TEXT 
} from "./config";

const errorMsg = [
  { errorCode: "too-many-files", chMsg: "上傳檔案超過一個" },
  { errorCode: "file-invalid-type", chMsg: "只接受副檔名為xlsx的檔案" },
];

function ImportDataPage() {
  const [state, dispatch] = useContext(StoreContext);
  const { user } = state;
  const hasImportPermission = canImport(user);
  const environmentBadge = getEnvironmentBadge();
  
  const [checkMsg, setCheckMsg] = useState(""); // 顯示上傳檔案名稱
  const [fileRejectMsg, setFileRejectMsg] = useState(""); // 顯示上傳檔案失敗後的錯誤訊息
  const [fbExHeader, setFbExHeader] = useState([]);
  const [isValidating, setIsValidating] = useState(false); // 驗證中狀態
  const [validationStage, setValidationStage] = useState(VALIDATION_STAGES.IDLE); // 驗證階段
  const [validationResult, setValidationResult] = useState(null); // 驗證結果 {pass, totalRows, errors}
  const [downloadMsg, setDownloadMsg] = useState(""); // 下載提示訊息
  const [detailedErrors, setDetailedErrors] = useState([]); // 詳細錯誤列表
  const [rawExcelData, setRawExcelData] = useState({}); // 原始 Excel 資料 (開發用)

  const { acceptedFiles, getRootProps, getInputProps, fileRejections } =
    useDropzone({
      multiple: false, // 不能一次丟多個檔案
      // accepted MIME type，只接受副檔名: ".xlsx", ".xls"
      accept: [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel",
      ],
      maxFiles: 1, // 上傳檔案數量總數最多一個
    });

  useEffect(() => {
    // get excelHeader info from firestore
    (async () => {
      const headers = await cloudStorage.getExcelHeader();
      // 如果 Firebase 載入失敗，使用預設表頭
      if (!headers || headers.error || !Array.isArray(headers) || headers.length === 0) {
        setFbExHeader(DEFAULT_HEADERS);
      } else {
        setFbExHeader(headers);
      }
    })();
  }, []);

  useEffect(() => {
    // console.log("fbExHeader ", fbExHeader);
    // setFileName(acceptedFiles);
    if (isEmpty(acceptedFiles)) {
      dispatch({
        type: Act.SET_FILEDATA,
        payload: {},
      });
      setIsValidating(false);
      setValidationStage(VALIDATION_STAGES.IDLE);
      setValidationResult(null);
      return;
    }

    // 開始驗證
    setIsValidating(true);
    setValidationStage(VALIDATION_STAGES.CHECKING_HEADER);

    // 檢查表格header
    const promises = acceptedFiles.map(async (file) => {
      const workbook = new Excel.Workbook();
      await workbook.xlsx.load(file);
      let pass = checkRes.success;
      let fileRes = `${file.name} - `;
      let totalRows = 0;
      const allErrors = []; // 收集所有錯誤
      const rawRowData = {}; // 收集原始資料 (開發用)
      
      workbook.eachSheet((worksheet) => {
        let resStr = "";
        // check header
        const headerRow = worksheet.getRow(1);
        
        // order
        const orderResult = checkHeader.order.checkMethod(
          headerRow.values,
          fbExHeader
        );
        if (orderResult.status === checkRes.failed) {
          pass = checkRes.failed;
          resStr += "表頭順序有誤。\n";
          // 將錯誤加入列表並標記工作表名稱，添加建議
          orderResult.errors.forEach(err => {
            allErrors.push({ 
              ...err, 
              sheetName: worksheet.name,
              severity: 'error',
              suggestion: err.suggestion || '請下載「範本」檔案，確認第一列的欄位 ID 順序與範本一致'
            });
          });
        }
        
        // header content
        const contentResult = checkHeader.content.checkMethod(headerRow.values, fbExHeader);
        if (contentResult.status === checkRes.failed) {
          pass = checkRes.failed;
          resStr += "表頭內容有誤。\n";
          // 將錯誤加入列表並標記工作表名稱，添加建議
          contentResult.errors.forEach(err => {
            allErrors.push({ 
              ...err, 
              sheetName: worksheet.name,
              severity: 'error',
              suggestion: err.suggestion || '請下載「範本」檔案，確認表頭標籤（Label）與範本一致'
            });
          });
        }

        // 更新驗證階段
        setValidationStage(VALIDATION_STAGES.CHECKING_CONTENT);

        /** record hasStartDate for each event
         *  lm: landMark
         *  lr: landRights
         * */
        let redStartDate = { lm: [], lr: [] };
        const { idIdx, startRow } = specRow;
        // check data body
        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber >= startRow) {
            totalRows += 1;
            
            // 收集原始資料 (開發用)
            if (!rawRowData[rowNumber]) {
              rawRowData[rowNumber] = {
                sheetName: worksheet.name,
                rowNumber,
                data: {}
              };
            }
            
            row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
              const colId = worksheet.getRow(idIdx).getCell(colNumber).value;
              
              // 收集此儲存格的原始值
              if (colId) {
                rawRowData[rowNumber].data[colId] = {
                  value: cell.value,
                  type: cell.type,
                  columnIndex: colNumber,
                  columnName: fbExHeader.find(h => h.id === colId)?.label || colId
                };
              }

              if (Object.hasOwn(checkBody, colId)) {
                let tmpStr = "";

                switch (colId) {
                  case "Land-->landName": {
                    if (cell.value) {
                      // reset record StartDate
                      redStartDate = { lm: [], lr: [] };
                      tmpStr = checkBody[colId].method(cell, worksheet);
                    }
                    break;
                  }
                  case "Land-->source": {
                    const checkList = [0, 1, 2, 3, 4, 5];
                    tmpStr = checkBody[colId].method(
                      cell,
                      worksheet,
                      checkList
                    );
                    break;
                  }
                  case "Land-->pawnRight":
                  case "Land-->plowingRight": {
                    const checkList = ["Y", "N"];
                    tmpStr = checkBody[colId].method(
                      cell,
                      worksheet,
                      checkList
                    );
                    break;
                  }
                  case "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent":
                  case "Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent": {
                    tmpStr = checkBody[colId].method(
                      cell,
                      worksheet,
                      redStartDate
                    );
                    break;
                  }
                  default:
                    tmpStr = checkBody[colId].method(cell, worksheet);
                    break;
                }

                pass =
                  pass === checkRes.success && !tmpStr
                    ? checkRes.success
                    : checkRes.failed;
                resStr += tmpStr;
                
                // 將資料錯誤加入結構化錯誤列表
                if (tmpStr) {
                  allErrors.push({
                    type: 'data_error',
                    sheetName: worksheet.name,
                    rowNumber,
                    columnIndex: colNumber,
                    columnName: fbExHeader.find(h => h.id === colId)?.label || colId,
                    message: tmpStr,
                    severity: 'error'
                  });
                }
              }
            });
          }
        });
        // console.log("resStr ", resStr);
        // console.log("pass ", pass);
        fileRes +=
          pass === checkRes.success
            ? `表單: ${worksheet.name} 檢查結果正確。\n`
            : `表單: ${worksheet.name} 檢查結果有誤，請參照下列資訊修改資料，請修改表格資料後重新執行上傳。\n ${resStr}`;
      });
      
      // 如果驗證失敗但沒有收集到錯誤，添加 fallback 錯誤訊息
      if (pass === checkRes.failed && allErrors.length === 0) {
        allErrors.push({
          type: 'system_error',
          message: '檔案格式驗證失敗，但無法識別具體錯誤',
          severity: 'error',
          suggestion: '請確認：1) 第一列是否為欄位 ID（如 Land-->landName），2) 是否有多餘或缺少的欄位，3) 下載「範本」比對檔案結構',
          details: '建議下載範本檔案，比對第一列的欄位 ID 是否完全一致'
        });
      }
      
      return { res: fileRes, pass, totalRows, errors: allErrors, rawRowData };
    });

    Promise.allSettled(promises)
      .then((data) => {
        if (!isEmpty(data) && data[0].status === 'fulfilled') {
          const { res, pass, totalRows, errors = [], rawRowData = {} } = data[0].value;
          setCheckMsg(res);

          // 設置詳細錯誤列表
          setDetailedErrors(errors);
          
          // 設置原始資料 (開發用)
          setRawExcelData(rawRowData);

          // 設置驗證結果
          setValidationStage(VALIDATION_STAGES.COMPLETED);
          setValidationResult({
            pass: pass === checkRes.success,
            totalRows: totalRows || 0,
            errorCount: errors.length,
            errors: pass === checkRes.failed ? res : null,
          });

          dispatch({
            type: Act.SET_FILEDATA,
            payload: pass === checkRes.failed ? {} : acceptedFiles[0],
          });
        } else if (data[0].status === 'rejected') {
          // Promise 被拒絕
          const errorMsg = `驗證過程發生錯誤：${data[0].reason?.message || '未知錯誤'}`;
          setCheckMsg(errorMsg);
          setValidationResult({
            pass: false,
            totalRows: 0,
            errorCount: 0,
            errors: errorMsg,
          });
        }
      })
      .catch((err) => {
        // 記錄錯誤但不使用 console.log
        const errorMsg = `驗證過程發生錯誤：${err?.message || '未知錯誤'}`;
        setCheckMsg(errorMsg);
        setDetailedErrors([{
          type: 'system_error',
          message: errorMsg,
          severity: 'error'
        }]);
        setValidationResult({
          pass: false,
          totalRows: 0,
          errorCount: 1,
          errors: errorMsg,
        });
      })
      .finally(() => {
        setIsValidating(false);
      });
  }, [acceptedFiles]);

  useEffect(() => {
    let tmpFileRejectMsg = "";
    if (fileRejections.length > 0) {
      tmpFileRejectMsg = errorMsg.find(
        (el) => el.errorCode === fileRejections[0].errors[0].code
      ).chMsg;
    }
    setFileRejectMsg(tmpFileRejectMsg);
  }, [fileRejections]);

  // 範本下載處理
  const handleDownloadTemplate = async () => {
    try {
      setDownloadMsg('正在生成範本檔案...');
      const blob = await generateTemplate(fbExHeader.length > 0 ? fbExHeader : undefined);
      downloadFile(blob, `土地資料匯入範本_${new Date().toLocaleDateString('zh-TW').replace(/\//g, '')}.xlsx`);
      setDownloadMsg('範本下載成功！');
      setTimeout(() => setDownloadMsg(""), 3000);
    } catch (error) {
      setDownloadMsg('範本下載失敗，請稍後再試');
      setTimeout(() => setDownloadMsg(""), 3000);
    }
  };

  const handleDownloadExample = async () => {
    try {
      setDownloadMsg('正在生成範例檔案...');
      const blob = await generateExample(fbExHeader.length > 0 ? fbExHeader : undefined);
      downloadFile(blob, `土地資料匯入範例_${new Date().toLocaleDateString('zh-TW').replace(/\//g, '')}.xlsx`);
      setDownloadMsg('範例檔案下載成功！');
      setTimeout(() => setDownloadMsg(""), 3000);
    } catch (error) {
      setDownloadMsg('範例檔案下載失敗，請稍後再試');
      setTimeout(() => setDownloadMsg(""), 3000);
    }
  };

  // 權限檢查：如果沒有匯入權限，顯示警告頁面
  if (!hasImportPermission) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '80vh',
          padding: 4
        }}
      >
        <Alert 
          severity="error" 
          sx={{ 
            maxWidth: 600, 
            width: '100%',
            mb: 3
          }}
        >
          <AlertTitle>
            <strong>無法存取匯入功能</strong>
          </AlertTitle>
          <Typography variant="body1" sx={{ mb: 2 }}>
            <strong>Developer 角色在正式站不允許匯入資料</strong>
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            目前環境：
            <Box
              component="span"
              sx={{
                display: 'inline-block',
                ml: 1,
                px: 1.5,
                py: 0.5,
                borderRadius: 1,
                backgroundColor: environmentBadge.bgColor,
                color: environmentBadge.color,
                fontSize: '0.875rem',
                fontWeight: 'bold'
              }}
            >
              {environmentBadge.label}
            </Box>
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            您的角色：<strong>{getUserRoleDisplayText(user)}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            如需匯入資料，請：
          </Typography>
          <ul style={{ marginTop: 8, marginBottom: 0, paddingLeft: 24 }}>
            <li>
              <Typography variant="body2" color="text.secondary">
                在<strong>測試站</strong>執行匯入作業
              </Typography>
            </li>
            <li>
              <Typography variant="body2" color="text.secondary">
                或聯絡管理員調整您的權限
              </Typography>
            </li>
          </ul>
        </Alert>
      </Box>
    );
  }

  return (
    <Box className="ImportDataPage">
      {/* 頂部說明與範本下載區域 */}
      <Box className="ImportDataPage__header" sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          資料匯入
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          請上傳符合格式的 Excel 檔案（.xlsx, .xls），系統將自動驗證資料格式
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <Button
            variant="outlined"
            size="small"
            startIcon={<DownloadIcon />}
            onClick={handleDownloadTemplate}
          >
            下載範本
          </Button>
          <Button
            variant="outlined"
            size="small"
            startIcon={<InsertDriveFileIcon />}
            onClick={handleDownloadExample}
          >
            下載範例檔案
          </Button>
        </Box>
        {downloadMsg && (
          <Alert severity="info" sx={{ mt: 2 }}>
            {downloadMsg}
          </Alert>
        )}
      </Box>

      {/* 拖放區域 */}
      <Grid container {...getRootProps({ className: "dropZone" })}>
        <Grid item xs={12}>
          <Input {...getInputProps()} />
          <Box className="typoBox">
            <CloudUploadIcon sx={{ fontSize: 60, color: 'var(--color-primary)', mb: 2, opacity: 0.6 }} />
            <Typography variant="h6" gutterBottom>
              拖曳檔案至此區域，或點擊選擇檔案
            </Typography>
            <Typography variant="body2" color="text.secondary">
              支援格式：.xlsx, .xls
            </Typography>
            <Typography variant="caption" color="text.secondary">
              檔案大小限制：最大 10MB
            </Typography>
          </Box>
        </Grid>
      </Grid>

      {/* 驗證進度指示器 */}
      {isValidating && (
        <Box sx={{ mt: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Typography variant="body2" color="text.secondary" sx={{ flexGrow: 1 }}>
              {VALIDATION_STAGE_TEXT[validationStage]}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              驗證中...
            </Typography>
          </Box>
          <LinearProgress />
        </Box>
      )}

      {/* 驗證結果顯示 */}
      {!isValidating && validationResult && (
        <Box sx={{ mt: 3 }}>
          <Alert
            severity={validationResult.pass ? "success" : "error"}
            icon={validationResult.pass ? <CheckCircleIcon /> : <ErrorIcon />}
          >
            <AlertTitle>
              {validationResult.pass ? "驗證通過" : "驗證失敗"}
            </AlertTitle>
            {validationResult.pass ? (
              <Box>
                <Typography variant="body2" gutterBottom>
                  共 <strong>{validationResult.totalRows}</strong> 筆資料，0 個錯誤
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  預計匯入時間：約 {Math.ceil(validationResult.totalRows / 100)} 分鐘
                </Typography>
              </Box>
            ) : (
              <Box>
                <Typography variant="body2" gutterBottom>
                  {validationResult.errorCount > 0 ? (
                    <>發現 <strong>{validationResult.errorCount}</strong> 個錯誤，請修正後重新上傳</>
                  ) : (
                    <>檔案格式驗證失敗，請檢查檔案內容</>
                  )}
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <LightbulbIcon sx={{ fontSize: 16 }} /> 提示：
                  {validationResult.errorCount > 0 
                    ? '請查看下方詳細錯誤資訊，或下載「範本」參考正確格式' 
                    : '請確認第一列是否為欄位 ID（非中文標題），並下載「範本」比對格式'}
                </Typography>
              </Box>
            )}
          </Alert>
          
          {/* 詳細錯誤訊息 */}
          {!validationResult.pass && detailedErrors.length > 0 && (
            <ValidationErrorsEnhanced 
              errors={detailedErrors} 
              rawExcelData={rawExcelData}
            />
          )}
        </Box>
      )}

      {/* 檔案資訊與錯誤訊息 */}
      {!isEmpty(checkMsg) && !validationResult && (
        <Grid container sx={{ mt: 2 }}>
          <List>
            <ListItem>
              <ListItemIcon>
                <FolderIcon />
              </ListItemIcon>
              <ListItemText>{checkMsg}</ListItemText>
            </ListItem>
            {fileRejectMsg !== "" && (
              <Alert severity="error" sx={{ width: '100%', mt: 1 }}>
                {fileRejectMsg}
              </Alert>
            )}
          </List>
        </Grid>
      )}

      {/* 底部按鈕區域 */}
      <Grid container className="bottomArea">
        <ImportDataBtn setCheckMsg={setCheckMsg} />
      </Grid>
    </Box>
  );
}

export default ImportDataPage;
