import React, { useEffect, useState, useMemo } from "react";

// material ui
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import Collapse from "@mui/material/Collapse";
import DateRangeIcon from "@mui/icons-material/DateRange";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import WorkHistoryIcon from "@mui/icons-material/WorkHistory";
import Pagination from "@mui/material/Pagination";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import SearchIcon from "@mui/icons-material/Search";
import InputAdornment from "@mui/material/InputAdornment";

// api
import { Api, readOntoData } from "../../../../api/land/Api";

// mock data
import {
  USE_MOCK_DATA,
  generateMockDateList,
  generateMockDateRecords,
  mockApiCall,
} from "../../../../utils/mockHistoryData";

/**
 * 歷史紀錄元件
 * 以日期分類顯示所有操作記錄，支援搜尋和雙層 pagination
 */
function DateClassify() {
  // 第一層：日期列表狀態
  const [allDates, setAllDates] = useState([]); // 所有日期
  const [searchTerm, setSearchTerm] = useState(""); // 搜尋關鍵字
  const [datePage, setDatePage] = useState(1); // 日期列表當前頁碼
  const DATES_PER_PAGE = 15; // 每頁顯示的日期數量

  // 第二層：每個日期的記錄分頁狀態 (key: dateStr, value: pageNumber)
  const [recordPages, setRecordPages] = useState({});
  const RECORDS_PER_PAGE = 10; // 每頁顯示的記錄數量

  // 載入日期列表
  useEffect(() => {
    const fetchDates = async () => {
      if (USE_MOCK_DATA) {
        // 使用假資料
        const mockData = await mockApiCall(() => generateMockDateList(90));
        const datesWithState = mockData.data
          .sort(
            (cur, next) =>
              Date.parse(next.YYYYMMDD) - Date.parse(cur.YYYYMMDD)
          )
          .map((el) => ({
            ...el,
            open: false,
            infoData: [],
            totalRecords: 0,
          }));
        setAllDates(datesWithState);
      } else {
        // 使用真實 API
        const apiStr = Api.getHistoryDateList();
        const res = await readOntoData(apiStr);
        const datesWithState = res.data
          .sort(
            (cur, next) =>
              Date.parse(next.YYYYMMDD) - Date.parse(cur.YYYYMMDD)
          )
          .map((el) => ({
            ...el,
            open: false,
            infoData: [],
            totalRecords: 0,
          }));
        setAllDates(datesWithState);
      }
    };
    fetchDates();
  }, []);

  // 過濾日期列表（根據搜尋關鍵字）
  const filteredDates = useMemo(() => {
    if (!searchTerm.trim()) {
      return allDates;
    }
    return allDates.filter((date) =>
      date.YYYYMMDD.includes(searchTerm.trim())
    );
  }, [allDates, searchTerm]);

  // 計算當前頁面要顯示的日期
  const paginatedDates = useMemo(() => {
    const startIndex = (datePage - 1) * DATES_PER_PAGE;
    const endIndex = startIndex + DATES_PER_PAGE;
    return filteredDates.slice(startIndex, endIndex);
  }, [filteredDates, datePage]);

  // 計算總頁數
  const totalDatePages = Math.ceil(filteredDates.length / DATES_PER_PAGE);

  // 處理搜尋輸入
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setDatePage(1); // 搜尋後重置到第一頁
  };

  // 處理日期點擊（展開/收合）
  const handleClick = async (dateStr) => {
    const tmpList = [...allDates];
    const findObj = tmpList.find((el) => el.YYYYMMDD === dateStr);

    if (findObj) {
      findObj.open = !findObj.open;

      if (findObj.open && findObj.infoData.length === 0) {
        // 第一次展開時載入資料
        if (USE_MOCK_DATA) {
          // 使用假資料
          const mockData = await mockApiCall(() =>
            generateMockDateRecords(dateStr, 30)
          );
          findObj.infoData = mockData.data;
          findObj.totalRecords = mockData.data.length;
        } else {
          // 使用真實 API
          const apiStr = Api.getHistoryDateListInfo().replace(
            "{dateStr}",
            dateStr
          );
          const tmpRes = await readOntoData(apiStr);
          findObj.infoData = tmpRes.data;
          findObj.totalRecords = tmpRes.data.length;
        }
        // 初始化該日期的記錄頁碼
        setRecordPages((prev) => ({ ...prev, [dateStr]: 1 }));
      }

      setAllDates(tmpList);
    }
  };

  // 處理記錄分頁變更
  const handleRecordPageChange = (dateStr, newPage) => {
    setRecordPages((prev) => ({ ...prev, [dateStr]: newPage }));
  };

  // 獲取當前頁面要顯示的記錄
  const getPaginatedRecords = (infoData, dateStr) => {
    const currentPage = recordPages[dateStr] || 1;
    const startIndex = (currentPage - 1) * RECORDS_PER_PAGE;
    const endIndex = startIndex + RECORDS_PER_PAGE;
    return infoData.slice(startIndex, endIndex);
  };

  return (
    <Box>
      {/* 搜尋框 */}
      <Box sx={{ mb: 2 }}>
        <TextField
          fullWidth
          placeholder="搜尋日期 (例如: 2024, 2024-01, 2024-01-15)"
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          size="small"
        />
      </Box>

      {/* 統計資訊 */}
      <Box
        sx={{
          p: 2,
          mb: 2,
          backgroundColor: "var(--color-surface-alt)",
          borderRadius: 1,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="body2" color="text.secondary">
          {searchTerm ? (
            <>
              找到 <strong>{filteredDates.length}</strong> 個日期（共{" "}
              {allDates.length} 個）
            </>
          ) : (
            <>
              共 <strong>{allDates.length}</strong> 個日期
            </>
          )}
        </Typography>
        {totalDatePages > 0 && (
          <Typography variant="body2" color="text.secondary">
            第 {datePage} / {totalDatePages} 頁
          </Typography>
        )}
      </Box>

      {/* 日期列表 */}
      <List component="nav">
        {paginatedDates.map((el) => {
          const { YYYYMMDD, open, infoData, totalRecords } = el;
          const currentRecordPage = recordPages[YYYYMMDD] || 1;
          const totalRecordPages = Math.ceil(totalRecords / RECORDS_PER_PAGE);
          const paginatedRecords = getPaginatedRecords(infoData, YYYYMMDD);

          return (
            <React.Fragment key={YYYYMMDD}>
              <ListItemButton onClick={() => handleClick(YYYYMMDD)}>
                <ListItemIcon>
                  <DateRangeIcon />
                </ListItemIcon>
                <ListItemText
                  primary={YYYYMMDD}
                  secondary={
                    totalRecords > 0 ? `${totalRecords} 筆記錄` : null
                  }
                />
                {open ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton>
              <Collapse in={open}>
                <Box sx={{ backgroundColor: "var(--color-surface-alt)", p: 2 }}>
                  {paginatedRecords.length > 0 ? (
                    <>
                      <List component="div" disablePadding>
                        {paginatedRecords.map((info) => (
                          <ListItem
                            key={info.hisId}
                            sx={{ pl: 4 }}
                            style={{
                              display: "grid",
                              gridTemplateColumns: "50px 10% 1fr",
                            }}
                          >
                            <ListItemIcon>
                              <WorkHistoryIcon />
                            </ListItemIcon>
                            <ListItemText primary={info.user} />
                            <ListItemText primary={info.action} />
                          </ListItem>
                        ))}
                      </List>

                      {/* 記錄分頁控制器 */}
                      {totalRecordPages > 1 && (
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "center",
                            mt: 2,
                          }}
                        >
                          <Pagination
                            count={totalRecordPages}
                            page={currentRecordPage}
                            onChange={(event, value) =>
                              handleRecordPageChange(YYYYMMDD, value)
                            }
                            size="small"
                            color="primary"
                          />
                        </Box>
                      )}
                    </>
                  ) : (
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ pl: 4, py: 2 }}
                    >
                      暫無操作記錄
                    </Typography>
                  )}
                </Box>
              </Collapse>
            </React.Fragment>
          );
        })}
      </List>

      {/* 日期列表分頁控制器 */}
      {totalDatePages > 1 && (
        <Box sx={{ display: "flex", justifyContent: "center", mt: 3, mb: 2 }}>
          <Pagination
            count={totalDatePages}
            page={datePage}
            onChange={(event, value) => setDatePage(value)}
            color="primary"
          />
        </Box>
      )}

      {/* 空狀態 */}
      {filteredDates.length === 0 && (
        <Box sx={{ textAlign: "center", py: 4 }}>
          <Typography variant="body1" color="text.secondary">
            {searchTerm ? `找不到包含 "${searchTerm}" 的日期` : "暫無歷史記錄"}
          </Typography>
        </Box>
      )}
    </Box>
  );
}

export default DateClassify;
