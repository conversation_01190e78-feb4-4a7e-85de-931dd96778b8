@use "../../../scss/common";

// 新增 CSS 變數支持
:root {
  --color-surface-variant: rgba(0, 0, 0, 0.02);
  --color-border-light: rgba(0, 0, 0, 0.08);
  --shadow-hover: 0 2px 8px rgba(0, 0, 0, 0.12);
  --color-primary-rgb: 25, 118, 210; /* Material UI primary blue */
  --gradient-primary: linear-gradient(45deg, #1976d2, #42a5f5);
  --gradient-surface: linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-variant) 100%);
}

[data-theme="dark"] {
  --color-surface-variant: rgba(255, 255, 255, 0.03);
  --color-border-light: rgba(255, 255, 255, 0.08);
  --shadow-hover: 0 2px 8px rgba(0, 0, 0, 0.3);
  --color-primary-rgb: 144, 202, 249; /* Material UI primary blue for dark theme */
  --gradient-primary: linear-gradient(45deg, #90caf9, #64b5f6);
  --gradient-surface: linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-variant) 100%);
}

.SearchPage {
  .SearchPage__layout {
    width: 100%;
  }

  .SearchPage__contentWrapper {
    width: 100%;
    flex: 1 1 0;
  }

  .SearchPage__content {
    width: 100%;
  }

  .SearchPage__filtersWrapper {
    flex-shrink: 0;
    width: auto;

    @media (max-width: 900px) {
      width: 100%;
      max-width: none;
    }

    @media (min-width: 900px) {
      max-width: 300px;
    }
    
    // 新增：在較小螢幕上的特殊處理
    @media (max-width: 768px) {
      .filtersPanel {
        margin-bottom: 16px;
      }
    }
  }

  .filtersPanel {
    position: sticky;
    top: 24px;
    height: calc(100vh - 120px); // 固定高度，留給 header 和 padding 空間
    max-height: calc(100vh - 120px);
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 300px;
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    box-shadow: var(--shadow-surface);
    border-radius: 12px;

    &.collapsed {
      align-items: center;
      justify-content: center;
      height: fit-content;
      max-height: fit-content;
      max-width: none;
      width: 72px;
      border: 1px solid var(--color-border);
      box-shadow: var(--shadow-surface);
      background-color: var(--color-surface);
    }

    // 新增：優化桌機版体驗
    @media (min-width: 1024px) {
      height: calc(100vh - 100px);
      max-height: calc(100vh - 100px);
    }

    // 小螢幕上保持彈性高度
    @media (max-width: 768px) {
      position: relative;
      height: auto;
      max-height: 50vh;
    }
  }

  .filtersContent {
    flex: 1;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    
    // 優化捕動条樣式
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    scrollbar-width: thin;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.2));
      border-radius: 4px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      
      &:hover {
        background: linear-gradient(180deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.3));
      }
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 4px;
    }
  }

  .filtersList {
    overscroll-behavior: contain;
    padding: 8px;
    padding-bottom: 24px;
    
    // 為 Accordion 群組增加間距
    .MuiAccordion-root {
      margin-bottom: 8px !important;
      border-radius: 8px !important;
      
      &:before {
        display: none;
      }
      
      &.Mui-expanded {
        margin-bottom: 8px !important;
      }
    }
  }

  .SearchPage__content {
    min-height: 60vh;
  }

  // 新增：左側面板的頭部和底部樣式
  .filtersPanel {
    .filtersHeader {
      flex-shrink: 0;
      padding: 12px 16px;
      border-bottom: 1px solid var(--color-border);
      background: linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-alt) 100%);
      
      .filterTitle {
        font-weight: 600;
        color: var(--color-text-primary);
        font-size: 1.1rem;
      }
      
      .filterStats {
        color: var(--color-text-secondary);
        font-size: 0.875rem;
      }
    }
    
    .filtersFooter {
      flex-shrink: 0;
      padding: 12px 16px;
      border-top: 1px solid var(--color-border);
      background: var(--color-surface-alt);
      
      .footerActions {
        display: flex;
        gap: 8px;
        justify-content: space-between;
        
        .MuiButton-root {
          font-size: 0.8rem;
          padding: 4px 12px;
        }
      }
    }
  }

  // 新增：Mobile/Tablet Drawer 專用樣式
  .MuiDrawer-paper {
    .filtersContent {
      height: 100%;
      
      .filtersHeader {
        background: var(--gradient-surface);
        border-bottom: 1px solid var(--color-border-light);
        border-radius: 0;
        
        // Mobile 專用的頭部樣式
        @media (max-width: 768px) {
          padding: 20px 16px 16px;
          
          .filterTitle {
            font-size: 1.2rem;
            font-weight: 700;
          }
          
          .filterStats {
            font-size: 0.9rem;
            margin-top: 4px;
          }
        }
      }
      
      .filtersContent {
        // Mobile Drawer 內容區域樣式
        @media (max-width: 768px) {
          padding: 16px 0;
        }
      }
      
      .filtersList {
        // Accordion 在 mobile drawer 中的樣式
        @media (max-width: 768px) {
          padding: 0 16px 16px;
          
          .MuiAccordion-root {
            border: 1px solid var(--color-border-light);
            box-shadow: var(--shadow-surface);
            margin-bottom: 12px !important;
            
            &:before {
              display: none;
            }
            
            .MuiAccordionSummary-root {
              padding: 12px 16px;
              min-height: 56px;
              
              &.Mui-expanded {
                min-height: 56px;
              }
            }
            
            .MuiAccordionDetails-root {
              padding: 8px 16px 16px;
            }
          }
        }
      }
      
      .filtersFooter {
        background: var(--color-surface);
        border-top: 1px solid var(--color-border-light);
        
        // Mobile 專用的底部樣式
        @media (max-width: 768px) {
          padding: 16px 20px 20px;
          
          .footerActions {
            gap: 12px;
            
            .MuiButton-root {
              font-size: 0.9rem;
              padding: 10px 16px;
              border-radius: 8px;
              text-transform: none;
              font-weight: 600;
              
              &[variant="contained"] {
                background: var(--gradient-primary);
                box-shadow: var(--shadow-surface);
              }
              
              &[variant="outlined"] {
                border-color: var(--color-border);
                
                &:hover {
                  background-color: var(--color-surface-variant);
                }
              }
            }
          }
        }
      }
    }
  }

  .SearchPage__controls {
    background-color: var(--color-surface);
    border-radius: 12px;
    padding: 16px;
    box-shadow: var(--shadow-surface);
    border: 1px solid var(--color-border);

    .chipGroupTitle {
      font-weight: 600;
      color: var(--color-text-secondary);
    }

    .chipGroupWrapper {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .chipGroup {
      & + .chipGroup {
        margin-top: 12px;
      }
    }

    .selectionSummary {
      margin-top: 12px;
    }
  }
}

.resultField {
  height: 60vh;
  border-radius: 12px;
  box-shadow: var(--shadow-surface);
  overflow-y: auto;
  padding: 16px 0;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);

  // 支持新的 Grid 佈局
  .resultGrid {
    .resultFieldItem {
      transition: all 0.2s ease;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-hover);
      }
      
      // 響應式字體大小
      @media (max-width: 600px) {
        .fieldLabel {
          font-size: 0.8125rem;
        }
        
        .fieldValue {
          font-size: 0.875rem;
        }
      }
      
      @media (min-width: 601px) and (max-width: 960px) {
        .fieldLabel {
          font-size: 0.875rem;
        }
        
        .fieldValue {
          font-size: 0.9375rem;
        }
      }
    }
  }

  // 空狀態樣式優化
  .emptyState {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: var(--color-text-muted);
    background: linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-variant) 100%);
    border-radius: 12px;
    border: 2px dashed var(--color-border-light);
    margin: 16px 0;
    
    .emptyStateTitle {
      font-size: 1.25rem;
      font-weight: 500;
      margin-bottom: 16px;
      
      @media (max-width: 600px) {
        font-size: 1.1rem;
      }
    }
    
    .emptyStateContent {
      text-align: center;
      max-width: 400px;
      line-height: 1.6;
      
      @media (max-width: 600px) {
        font-size: 0.875rem;
        max-width: 300px;
      }
    }
    
    .suggestions {
      margin-top: 24px;
      text-align: left;
      
      .suggestionItem {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
        font-size: 0.9rem;
        
        &::before {
          content: "•";
          color: var(--color-primary);
          margin-right: 8px;
          font-weight: bold;
        }
      }
    }
    
    @media (max-width: 600px) {
      min-height: 160px;
      padding: 16px;
      margin: 12px 0;
    }
  }

  // 搜尋統計資訊樣式
  .searchStats {
    background: linear-gradient(90deg, var(--color-surface-variant) 0%, rgba(var(--color-primary-rgb), 0.05) 100%);
    border-left: 4px solid var(--color-primary);
    transition: all 0.2s ease;
    
    &:hover {
      box-shadow: var(--shadow-hover);
    }
    
    .statsIcon {
      display: inline-block;
      margin-right: 8px;
      font-size: 1.1em;
    }
    
    @media (max-width: 600px) {
      font-size: 0.875rem;
      padding: 12px !important;
    }
  }

  // 歡迎狀態樣式
  .welcomeState {
    background: linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-variant) 100%);
    border: 2px dashed var(--color-border-light);
    border-radius: 16px;
    margin: 20px 0;
    padding: 40px 16px;
    text-align: center;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(var(--color-primary-rgb), 0.03) 0%, transparent 70%);
      animation: welcomePulse 4s ease-in-out infinite;
    }

    .welcomeTitle {
      position: relative;
      z-index: 2;
      background: linear-gradient(45deg, var(--color-primary), #42a5f5);
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
      font-weight: 600;
      margin-bottom: 24px;

      @media (max-width: 600px) {
        font-size: 1.75rem;
      }
    }

    .welcomeSubtitle {
      position: relative;
      z-index: 2;
      max-width: 600px;
      margin: 0 auto 32px;
      line-height: 1.6;
      font-weight: 500;
    }

    .featureGrid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 24px;
      max-width: 800px;
      margin: 0 auto;
      position: relative;
      z-index: 2;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }

    .featureCard {
      background: var(--color-surface);
      border: 1px solid var(--color-border-light);
      border-radius: 12px;
      padding: 24px;
      text-align: center;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-hover);
        border-color: var(--color-primary);
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(var(--color-primary-rgb), 0.1), transparent);
        transition: left 0.5s;
      }

      &:hover::after {
        left: 100%;
      }

      .featureIcon {
        font-size: 1.5rem;
        margin-bottom: 8px;
        color: var(--color-primary);
      }

      .featureTitle {
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--color-primary);
        position: relative;
        z-index: 1;
      }

      .featureDescription {
        color: var(--color-text-secondary);
        font-size: 0.9rem;
        line-height: 1.5;
        position: relative;
        z-index: 1;
      }
    }

    .welcomeTip {
      position: relative;
      z-index: 2;
      margin-top: 32px;
      padding: 16px;
      background: rgba(var(--color-primary-rgb), 0.1);
      border-radius: 8px;
      border: 1px solid rgba(var(--color-primary-rgb), 0.2);

      .tipText {
        color: var(--color-text-muted);
        font-style: italic;
        font-size: 0.9rem;
        margin: 0;
      }
    }

    @media (max-width: 600px) {
      padding: 24px 12px;
      margin: 12px 0;

      .welcomeTitle {
        font-size: 1.5rem !important;
        margin-bottom: 16px;
      }

      .welcomeSubtitle {
        font-size: 0.95rem;
        margin-bottom: 24px;
        padding: 0 8px;
      }

      .featureGrid {
        gap: 12px;
      }

      .featureCard {
        padding: 16px 12px;
        
        .featureTitle {
          font-size: 0.95rem;
        }
        
        .featureDescription {
          font-size: 0.8rem;
        }
      }

      .welcomeTip {
        margin-top: 24px;
        padding: 12px;
        
        .tipText {
          font-size: 0.8rem;
        }
      }
    }

    @media (max-width: 480px) {
      padding: 20px 8px;
      
      .welcomeTitle {
        font-size: 1.3rem !important;
      }
      
      .welcomeSubtitle {
        font-size: 0.9rem;
      }
      
      .featureCard {
        padding: 12px 8px;
      }
    }
  }

  @keyframes welcomePulse {
    0%, 100% {
      transform: scale(1) rotate(0deg);
      opacity: 0.3;
    }
    50% {
      transform: scale(1.1) rotate(180deg);
      opacity: 0.1;
    }
  }
}
