import React, { useState } from "react";

// material ui
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Chip from "@mui/material/Chip";
import Alert from "@mui/material/Alert";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ErrorIcon from "@mui/icons-material/Error";
import WarningIcon from "@mui/icons-material/Warning";
import TableChartIcon from "@mui/icons-material/TableChart";
import ViewColumnIcon from "@mui/icons-material/ViewColumn";

// scss
import "./ValidationErrors.scss";

/**
 * 驗證錯誤顯示元件
 * @param {Array} errors - 結構化錯誤陣列
 */
function ValidationErrors({ errors }) {
  const [expanded, setExpanded] = useState("header");

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  // 分類錯誤
  const headerErrors = errors.filter(e => e.type === "header_order" || e.type === "header_content");
  const dataErrors = errors.filter(e => e.type !== "header_order" && e.type !== "header_content");

  // 錯誤圖標
  const getErrorIcon = (severity) => (
    severity === "warning" ? (
      <WarningIcon sx={{ fontSize: 20, color: "var(--color-warning)" }} />
    ) : (
      <ErrorIcon sx={{ fontSize: 20, color: "var(--color-danger)" }} />
    )
  );

  return (
    <Box className="ValidationErrors">
      {/* 表頭錯誤 */}
      {headerErrors.length > 0 && (
        <Accordion 
          expanded={expanded === "header"} 
          onChange={handleChange("header")}
          className="error-accordion"
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            className="error-accordion-summary error"
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, width: "100%" }}>
              <ViewColumnIcon />
              <Typography sx={{ flexGrow: 1, fontWeight: 600 }}>
                表頭格式錯誤
              </Typography>
              <Chip 
                label={`${headerErrors.length} 個錯誤`} 
                size="small" 
                color="error"
              />
            </Box>
          </AccordionSummary>
          <AccordionDetails className="error-accordion-details">
            {headerErrors.map((error, index) => (
              <Alert 
                key={`header-${index}`}
                severity={error.severity || "error"}
                icon={getErrorIcon(error.severity)}
                sx={{ mb: 1 }}
              >
                <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
                  {error.message}
                </Typography>
                
                {error.columnIndex && (
                  <Typography variant="caption" color="text.secondary">
                    欄位位置：第 {error.columnIndex} 欄
                  </Typography>
                )}
                
                {error.expected && error.actual && (
                  <Box sx={{ mt: 1, pl: 2, borderLeft: "3px solid var(--color-danger)" }}>
                    <Typography variant="caption" component="div">
                      <strong>預期：</strong>{error.expected}
                    </Typography>
                    <Typography variant="caption" component="div">
                      <strong>實際：</strong>{error.actual}
                    </Typography>
                  </Box>
                )}
                
                {error.suggestion && (
                  <Typography variant="caption" sx={{ display: "block", mt: 1, color: "var(--color-warning)" }}>
                    💡 建議：{error.suggestion}
                  </Typography>
                )}
                
                {error.missing && error.missing.length > 0 && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="caption" sx={{ fontWeight: 600 }}>
                      缺少的欄位：
                    </Typography>
                    <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5, mt: 0.5 }}>
                      {error.missing.map((field, idx) => (
                        <Chip 
                          key={idx}
                          label={field.label || field.id || field}
                          size="small"
                          variant="outlined"
                          color="error"
                        />
                      ))}
                    </Box>
                  </Box>
                )}
                
                {error.extra && error.extra.length > 0 && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="caption" sx={{ fontWeight: 600 }}>
                      多餘的欄位：
                    </Typography>
                    <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5, mt: 0.5 }}>
                      {error.extra.map((field, idx) => (
                        <Chip 
                          key={idx}
                          label={field}
                          size="small"
                          variant="outlined"
                          color="warning"
                        />
                      ))}
                    </Box>
                  </Box>
                )}
              </Alert>
            ))}
            
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="caption">
                💡 <strong>解決方法：</strong>請下載「範本」或「範例檔案」，確保表頭格式完全正確
              </Typography>
            </Alert>
          </AccordionDetails>
        </Accordion>
      )}

      {/* 資料內容錯誤 */}
      {dataErrors.length > 0 && (
        <Accordion 
          expanded={expanded === "data"} 
          onChange={handleChange("data")}
          className="error-accordion"
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            className="error-accordion-summary error"
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, width: "100%" }}>
              <TableChartIcon />
              <Typography sx={{ flexGrow: 1, fontWeight: 600 }}>
                資料內容錯誤
              </Typography>
              <Chip 
                label={`${dataErrors.length} 個錯誤`} 
                size="small" 
                color="error"
              />
            </Box>
          </AccordionSummary>
          <AccordionDetails className="error-accordion-details">
            {dataErrors.slice(0, 50).map((error, index) => (
              <Alert 
                key={`data-${index}`}
                severity={error.severity || "error"}
                icon={getErrorIcon(error.severity)}
                sx={{ mb: 1 }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: "wrap" }}>
                  {error.sheetName && (
                    <Chip label={`工作表: ${error.sheetName}`} size="small" />
                  )}
                  {error.rowNumber && (
                    <Chip label={`第 ${error.rowNumber} 列`} size="small" color="primary" />
                  )}
                  {error.columnName && (
                    <Chip label={error.columnName} size="small" variant="outlined" />
                  )}
                </Box>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {error.message}
                </Typography>
                {error.value && (
                  <Typography variant="caption" color="text.secondary" sx={{ display: "block", mt: 0.5 }}>
                    當前值：<code>{error.value}</code>
                  </Typography>
                )}
              </Alert>
            ))}
            
            {dataErrors.length > 50 && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                <Typography variant="caption">
                  顯示前 50 個錯誤，總共 {dataErrors.length} 個錯誤。請先修正上述錯誤後重新上傳。
                </Typography>
              </Alert>
            )}
          </AccordionDetails>
        </Accordion>
      )}
    </Box>
  );
}

export default ValidationErrors;
