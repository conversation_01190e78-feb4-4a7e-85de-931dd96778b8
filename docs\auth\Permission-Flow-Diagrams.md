# 權限檢查流程圖

**視覺化權限判斷邏輯** 🔄

---

## 📋 目錄

1. [編輯權限流程](#編輯權限流程-canedit)
2. [匯入權限流程](#匯入權限流程-canimport)
3. [管理權限流程](#管理權限流程-canmanageusers)
4. [實作流程](#實作流程)
5. [錯誤處理流程](#錯誤處理流程)

---

## 🔄 編輯權限流程 (canEdit)

**適用頁面**: EditPage, GisPage

```
開始
  │
  ├─→ 檢查 user 物件是否存在？
  │   ├─ ❌ 不存在 → 返回 false (無權限)
  │   └─ ✅ 存在 → 繼續
  │
  ├─→ 檢查 roleDev 欄位是否存在？
  │   ├─ ❌ 不存在 → 返回 false (無權限)
  │   └─ ✅ 存在 → 繼續
  │
  ├─→ 解析角色陣列
  │   roles = roleDev.split(',')
  │   例如: "admin,developer" → ["admin", "developer"]
  │
  ├─→ 檢查是否包含 'admin'？
  │   ├─ ✅ 是 → 返回 true (Admin 最高權限)
  │   └─ ❌ 否 → 繼續
  │
  ├─→ 檢查是否包含 'editor'？
  │   ├─ ✅ 是 → 返回 true (Editor 可編輯)
  │   └─ ❌ 否 → 繼續
  │
  ├─→ 檢查是否包含 'developer'？
  │   ├─ ❌ 否 → 返回 false (無權限)
  │   └─ ✅ 是 → 繼續檢查環境
  │
  └─→ 檢查當前環境
      ├─ 測試站 (development) → 返回 true (Developer 可編輯)
      └─ 正式站 (production) → 返回 false (Developer 禁止編輯)
```

### 決策表

| 角色 | 測試站 | 正式站 | 結果 |
|------|--------|--------|------|
| admin | ✅ | ✅ | true |
| editor | ✅ | ✅ | true |
| developer | ✅ | ❌ | 視環境 |
| 其他 | ❌ | ❌ | false |

---

## 🔄 匯入權限流程 (canImport)

**適用頁面**: ImportDataPage

```
開始
  │
  ├─→ 檢查 user 物件是否存在？
  │   ├─ ❌ 不存在 → 返回 false
  │   └─ ✅ 存在 → 繼續
  │
  ├─→ 檢查 roleDev 欄位是否存在？
  │   ├─ ❌ 不存在 → 返回 false
  │   └─ ✅ 存在 → 繼續
  │
  ├─→ 解析角色陣列
  │   roles = roleDev.split(',')
  │
  ├─→ 檢查是否包含 'admin'？
  │   ├─ ✅ 是 → 返回 true (Admin 可匯入)
  │   └─ ❌ 否 → 繼續
  │
  └─→ 檢查是否包含 'developer'？
      ├─ ✅ 是 → 返回 true (Developer 可匯入)
      └─ ❌ 否 → 返回 false (Editor 無匯入權限)
```

### 決策表

| 角色 | 任何環境 | 結果 |
|------|----------|------|
| admin | ✅ | true |
| developer | ✅ | true |
| editor | ❌ | false |
| 其他 | ❌ | false |

---

## 🔄 管理權限流程 (canManageUsers)

**適用頁面**: AuthorityPage

```
開始
  │
  ├─→ 檢查 user 物件是否存在？
  │   ├─ ❌ 不存在 → 返回 false
  │   └─ ✅ 存在 → 繼續
  │
  ├─→ 檢查 roleDev 欄位是否存在？
  │   ├─ ❌ 不存在 → 返回 false
  │   └─ ✅ 存在 → 繼續
  │
  ├─→ 解析角色陣列
  │   roles = roleDev.split(',')
  │
  └─→ 檢查是否包含 'admin'？
      ├─ ✅ 是 → 返回 true (只有 Admin 可管理)
      └─ ❌ 否 → 返回 false (其他角色都不可管理)
```

### 決策表

| 角色 | 任何環境 | 結果 |
|------|----------|------|
| admin | ✅ | true |
| developer | ❌ | false |
| editor | ❌ | false |
| 其他 | ❌ | false |

---

## 🔄 實作流程

### 完整的權限控制實作流程

```
使用者操作
  │
  ├─→ 【前端 UI 層】
  │   │
  │   ├─→ 檢查權限
  │   │   hasPermission = canEdit(user)
  │   │
  │   ├─→ 控制 UI 元素
  │   │   <Button disabled={!hasPermission} />
  │   │
  │   └─→ 視覺反饋
  │       ├─ 有權限: 按鈕可點擊（藍色）
  │       └─ 無權限: 按鈕 disabled（灰色）
  │
  ├─→ 【使用者點擊按鈕】
  │   │
  │   ├─→ onClick 事件觸發
  │   │
  │   └─→ 執行 handleClick()
  │
  ├─→ 【邏輯檢查層】
  │   │
  │   ├─→ 再次檢查權限
  │   │   if (!canEdit(user)) {
  │   │
  │   ├─→ 顯示錯誤訊息
  │   │   │
  │   │   ├─ 方案 A: alert()
  │   │   ├─ 方案 B: Snackbar (推薦)
  │   │   └─ 方案 C: PermissionHint
  │   │
  │   └─→ return; // 阻止繼續執行
  │
  └─→ 【執行業務邏輯】
      │
      ├─→ 執行資料變更
      ├─→ 呼叫 API
      └─→ 更新 UI
```

### 雙重防護原理

```
第一層防護（UI）: disabled={!hasPermission}
  ↓
  用途：提供視覺反饋，減少誤操作
  繞過方式：開發者工具移除 disabled 屬性
  ↓
第二層防護（邏輯）: if (!hasPermission) return;
  ↓
  用途：真正的權限檢查，防止繞過
  無法繞過（除非修改 JavaScript 程式碼）
```

---

## 🔄 錯誤處理流程

### 方案 A: Alert（傳統方式）

```
權限檢查失敗
  │
  ├─→ 生成錯誤訊息
  │   message = getPermissionMessage(user, OPERATION.EDIT)
  │
  ├─→ 顯示 alert
  │   alert(message)
  │
  └─→ 使用者點擊「確定」
      │
      └─→ 對話框關閉
          └─→ 停留在當前頁面
```

**優點**: 簡單直接  
**缺點**: 阻斷式、體驗差、無法自動消失

---

### 方案 B: Snackbar（推薦）

```
權限檢查失敗
  │
  ├─→ 生成錯誤訊息
  │   message = getPermissionMessage(user, OPERATION.EDIT)
  │
  ├─→ 呼叫 showError()
  │   showError(message)
  │
  ├─→ Snackbar 顯示
  │   ├─ 位置: 頂部居中
  │   ├─ 顏色: 紅色（error）
  │   └─ 圖示: ❌
  │
  ├─→ 使用者可以：
  │   ├─ 等待 4 秒自動消失
  │   └─ 點擊 X 手動關閉
  │
  └─→ Snackbar 淡出
      └─→ 停留在當前頁面
```

**優點**: 優雅、不阻斷、自動消失  
**缺點**: 需要額外組件

---

### 方案 C: PermissionHint（頁面級提示）

```
進入頁面
  │
  ├─→ 檢查權限
  │   hint = usePermissionHint(user, 'edit')
  │
  ├─→ 判斷是否顯示提示
  │   if (hint.shouldShow)
  │
  ├─→ 在頁面頂部顯示 Banner
  │   ├─ 正式站 + 無權限: ⚠️ 警告（橙色）
  │   ├─ 測試站 + 無權限: ℹ️ 提示（藍色）
  │   └─ 正式站 + 有權限: ⚠️ 謹慎操作（橙色）
  │
  └─→ 持續顯示
      └─→ 不會自動消失
          └─→ 提醒使用者權限狀態
```

**優點**: 主動提示、持續可見  
**缺點**: 占用頁面空間

---

## 🎯 三種方案對比

| 特性 | Alert | Snackbar | PermissionHint |
|------|-------|----------|----------------|
| **時機** | 操作時 | 操作時 | 進入頁面時 |
| **位置** | 中央彈窗 | 頂部居中 | 頁面頂部 |
| **阻斷性** | ✅ 阻斷 | ❌ 不阻斷 | ❌ 不阻斷 |
| **自動消失** | ❌ 需點擊 | ✅ 4 秒 | ❌ 持續顯示 |
| **體驗** | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **實作複雜度** | ⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |

### 建議使用場景

| 場景 | 推薦方案 | 理由 |
|------|---------|------|
| 按鈕點擊權限檢查 | Snackbar | 優雅不阻斷 |
| 頁面進入權限提示 | PermissionHint | 主動告知 |
| 快速實作 | Alert | 簡單直接 |
| 已有 Snackbar 基礎設施 | 使用現有 Snackbar | 保持一致性 |

---

## 🔗 完整範例

### EditPage 完整流程

```
使用者進入 EditPage
  │
  ├─→ 【頁面載入】
  │   │
  │   ├─→ 取得 user 物件
  │   │   const { user } = state;
  │   │
  │   ├─→ 初始化 Snackbar Hook
  │   │   const { showError } = usePermissionSnackbar();
  │   │
  │   ├─→ 初始化 PermissionHint Hook
  │   │   const hint = usePermissionHint(user, 'edit');
  │   │
  │   ├─→ 檢查編輯權限
  │   │   const hasEditPermission = canEdit(user);
  │   │
  │   └─→ 渲染頁面
  │       ├─ PermissionHint Banner（如果需要）
  │       ├─ 編輯按鈕（disabled 控制）
  │       └─ 儲存按鈕（disabled 控制）
  │
  ├─→ 【使用者點擊「編輯」按鈕】
  │   │
  │   ├─→ EditButton onClick 觸發
  │   │
  │   ├─→ checkStatus() 執行
  │   │
  │   ├─→ 權限檢查
  │   │   if (!canEdit(user))
  │   │
  │   ├─→ 顯示 Snackbar 錯誤
  │   │   showError(getPermissionMessage(user, OPERATION.EDIT))
  │   │   "您的角色（開發者）在正式站環境下沒有編輯權限。"
  │   │
  │   └─→ return; // 阻止編輯
  │
  ├─→ 【使用者修改資料】
  │   │
  │   └─→ 資料暫存在 state
  │
  └─→ 【使用者點擊「儲存」按鈕】
      │
      ├─→ SaveButton onClick 觸發
      │
      ├─→ handleSave() 執行
      │
      ├─→ 再次權限檢查
      │   if (!canEdit(user))
      │
      ├─ 權限不足 ─→ 顯示 Snackbar 錯誤 → return;
      │
      └─ 權限充足 ─→ 執行儲存邏輯
          │
          ├─→ 驗證資料
          ├─→ 呼叫 API
          ├─→ 更新 Firebase
          └─→ 顯示成功訊息
```

---

## 📝 決策樹總結

```
我應該使用哪個權限函數？
  │
  ├─→ 編輯地號資料？
  │   └─→ canEdit()
  │
  ├─→ 編輯座標資料？
  │   └─→ canEdit()
  │
  ├─→ 匯入 Excel 資料？
  │   └─→ canImport()
  │
  ├─→ 管理使用者權限？
  │   └─→ canManageUsers()
  │
  └─→ 只是查看資料？
      └─→ 不需要權限檢查
```

```
我應該在哪裡加權限檢查？
  │
  ├─→ UI 層（必須）
  │   └─→ <Button disabled={!hasPermission} />
  │
  └─→ 邏輯層（必須）
      └─→ if (!hasPermission) return;
```

```
權限檢查失敗應該顯示什麼？
  │
  ├─→ 操作級錯誤？
  │   └─→ Snackbar（推薦）或 Alert
  │
  └─→ 頁面級提示？
      └─→ PermissionHint
```

---

**版本**: 1.0.0  
**更新**: 2025-10-02  
**相關文檔**: [權限管控規則文件](./Permission-Control-Guide.md)
