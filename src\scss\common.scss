@use "sass:map";

$color-primary: #008cba;
$color-primary-hover: #0076a5;
$color-primary-light: #e6f4fb;
$color-secondary: #1abc9c; // 原 accent 顏色，現為 secondary
$color-secondary-hover: #16a085;
$color-secondary-light: #d9f3ec;
$color-accent: #1abc9c; // 保持相容性
$color-danger: #f44336;
$color-warning: #ff9800;
$color-info: #2196f3;
$color-success: #4caf50;
$color-surface: #ffffff;
$color-surface-alt: #f5f7fa;
$color-background: #eef3f7;
$color-border: #d5e3eb;
$color-border-light: #e0e7ef;
$color-border-strong: #b7c9d6;
$color-text-primary: #1f2a37;
$color-text-secondary: #4b5563;
$color-text-muted: #6b7280;
$color-text-disabled: #9ca3af;
$shadow-surface: 0 16px 32px rgba(15, 23, 42, 0.12);
$shadow-hover: 0 2px 8px rgba(0, 0, 0, 0.12);
$color-primary-tint: #e6f4fb; // 保持相容性
$color-accent-tint: #d9f3ec; // 保持相容性

:root {
  --color-primary: #{$color-primary};
  --color-primary-hover: #{$color-primary-hover};
  --color-primary-light: #{$color-primary-light};
  --color-secondary: #{$color-secondary};
  --color-secondary-hover: #{$color-secondary-hover};
  --color-secondary-light: #{$color-secondary-light};
  --color-accent: #{$color-accent};
  --color-danger: #{$color-danger};
  --color-warning: #{$color-warning};
  --color-info: #{$color-info};
  --color-success: #{$color-success};
  --color-surface: #{$color-surface};
  --color-surface-alt: #{$color-surface-alt};
  --color-background: #{$color-background};
  --color-border: #{$color-border};
  --color-border-light: #{$color-border-light};
  --color-border-strong: #{$color-border-strong};
  --color-text-primary: #{$color-text-primary};
  --color-text-secondary: #{$color-text-secondary};
  --color-text-muted: #{$color-text-muted};
  --color-text-disabled: #{$color-text-disabled};
  --shadow-surface: #{$shadow-surface};
  --shadow-hover: #{$shadow-hover};
  --color-primary-tint: #{$color-primary-tint};
  --color-accent-tint: #{$color-accent-tint};
}

*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: "Noto Sans TC", sans-serif;
  // 不要設定全域文字顏色，避免覆蓋 Material-UI 的樣式(包含 mui icons)
  // color: var(--color-text-primary);
}

body {
  background: var(--color-background);
}

a {
  color: var(--color-primary);
  text-decoration: none;

  &:hover,
  &:focus {
    color: var(--color-primary-hover);
    text-decoration: underline;
  }
}

// Material-UI Button 組件樣式覆蓋
.MuiButton-containedPrimary {
  background-color: var(--color-primary) !important;
  color: #fff !important;

  &:hover,
  &:focus {
    background-color: var(--color-primary-hover) !important;
  }
}

.MuiButton-containedSecondary {
  background-color: var(--color-secondary) !important;
  color: #fff !important;

  &:hover,
  &:focus {
    background-color: var(--color-secondary-hover) !important;
  }
}

.MuiButton-outlinedPrimary {
  border-color: var(--color-primary) !important;
  color: var(--color-primary) !important;

  &:hover,
  &:focus {
    border-color: var(--color-primary-hover) !important;
    background-color: var(--color-primary-light) !important;
  }
}

.MuiButton-outlinedSecondary {
  border-color: var(--color-secondary) !important;
  color: var(--color-secondary) !important;

  &:hover,
  &:focus {
    border-color: var(--color-secondary-hover) !important;
    background-color: var(--color-secondary-light) !important;
  }
}

.MuiButton-textSecondary {
  color: var(--color-secondary) !important;

  &:hover,
  &:focus {
    background-color: var(--color-secondary-light) !important;
  }
}

// Material-UI Chip 組件樣式覆蓋
.MuiChip-outlinedPrimary {
  color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}

.MuiChip-outlinedSecondary {
  color: var(--color-secondary) !important;
  border-color: var(--color-secondary) !important;
}

.MuiChip-filledSecondary {
  background-color: var(--color-secondary) !important;
  color: #fff !important;
}

// Material-UI TextField 組件樣式覆蓋
.MuiOutlinedInput-root {
  border-radius: 8px !important;
  
  &.Mui-focused .MuiOutlinedInput-notchedOutline {
    border-color: var(--color-primary) !important;
  }
}

// Material-UI Accordion 組件樣式覆蓋
.MuiAccordion-root {
  border-radius: 8px !important;
  border: 1px solid var(--color-border) !important;
  
  &:before {
    display: none !important;
  }
  
  &.Mui-expanded {
    margin-bottom: 8px !important;
  }
}

// Material-UI Paper 組件樣式覆蓋
.MuiPaper-root {
  background-color: var(--color-surface) !important;
  border-radius: 12px !important;
}

// 通用顏色類別
.text-primary { color: var(--color-text-primary) !important; }
.text-secondary { color: var(--color-text-secondary) !important; }
.text-muted { color: var(--color-text-muted) !important; }
.text-disabled { color: var(--color-text-disabled) !important; }

.bg-primary { background-color: var(--color-primary) !important; }
.bg-secondary { background-color: var(--color-secondary) !important; }
.bg-surface { background-color: var(--color-surface) !important; }
.bg-surface-alt { background-color: var(--color-surface-alt) !important; }

.border-primary { border-color: var(--color-primary) !important; }
.border-secondary { border-color: var(--color-secondary) !important; }
.border-light { border-color: var(--color-border-light) !important; }

@mixin center {
  display: flex;
  justify-content: center;
  align-items: center;
}

$breakpoints: (
    "xs": 0px,
    "sm": 576px,
    "md": 768px,
    "lg": 992px,
    "xl": 1280px
);

// 通用模組
@mixin card-style {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  box-shadow: var(--shadow-surface);
}

@mixin hover-lift {
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-1px);
  }
}

// 通用類別
.card {
  @include card-style;
  padding: 1rem;
}

.card-hover {
  @include card-style;
  @include hover-lift;
  padding: 1rem;
}

.surface {
  background: var(--color-surface);
  border-radius: 8px;
}

.surface-alt {
  background: var(--color-surface-alt);
  border-radius: 8px;
}

.shadow-surface {
  box-shadow: var(--shadow-surface);
}

.shadow-hover {
  box-shadow: var(--shadow-hover);
}

.mainBox {
  &_shadowMain {
    @include card-style;
    min-height: 80vh;
    padding: 1.5rem;
  }
}

// Dark mode 支援
[data-theme="dark"] {
  --color-surface: #1a1a1a;
  --color-surface-alt: #2d2d2d;
  --color-background: #121212;
  --color-border: #404040;
  --color-border-light: #333333;
  --color-border-strong: #606060;
  --color-text-primary: #ffffff;
  --color-text-secondary: #b3b3b3;
  --color-text-muted: #808080;
  --color-text-disabled: #666666;
  --shadow-surface: 0 16px 32px rgba(0, 0, 0, 0.4);
  --shadow-hover: 0 2px 8px rgba(0, 0, 0, 0.3);
}

@media only screen and (max-width: map.get($breakpoints, "md")) {
  //.mainBox {
  //  padding: 2.5rem 1.5rem;
  //}
}
