import { checkRes } from "../../config";

/**
 * 檢查表頭內容是否正確
 * @param {Array} headerArr - Excel 第一列的表頭陣列
 * @param {Array} fbHeader - Firebase 的標準表頭配置
 * @returns {Object} { status, errors } - 驗證狀態和詳細錯誤列表
 */
const checkHeaderCell = (headerArr, fbHeader) => {
  // Excel 的 headerRow.values 第一個元素是 undefined，需要從索引 1 開始
  const tmpHeaderArr = headerArr
    .slice(1) // 移除第一個空值
    .filter((el) => el)
    .map((value, index) => ({ id: value || "", columnIndex: index + 1 }));

  const errors = [];
  const validHeaderIds = fbHeader.map(h => h.id);

  // 檢查每個表頭欄位
  tmpHeaderArr.forEach(({ id, columnIndex }) => {
    if (!id) {
      errors.push({
        type: 'header_content',
        columnIndex,
        message: `第 ${columnIndex} 欄的欄位 ID 為空`,
        severity: 'error'
      });
    } else if (!validHeaderIds.includes(id)) {
      // 嘗試找到最相似的欄位（簡單的模糊比對）
      const similar = validHeaderIds.find(validId => 
        validId.toLowerCase().includes(id.toLowerCase()) || 
        id.toLowerCase().includes(validId.toLowerCase())
      );
      
      errors.push({
        type: 'header_content',
        columnIndex,
        actual: id,
        message: `第 ${columnIndex} 欄的欄位 ID「${id}」不在標準欄位列表中`,
        suggestion: similar ? `您是否要輸入「${similar}」？` : '請參考範本檔案',
        severity: 'error'
      });
    }
  });

  // 檢查是否有缺少的必要欄位
  const missingHeaders = fbHeader.filter(h => 
    !tmpHeaderArr.some(th => th.id === h.id)
  );

  if (missingHeaders.length > 0) {
    errors.push({
      type: 'header_content',
      message: `缺少 ${missingHeaders.length} 個必要欄位`,
      missing: missingHeaders.map(h => ({
        id: h.id,
        label: h.label,
        order: h.order
      })),
      severity: 'error'
    });
  }

  return errors.length > 0
    ? { status: checkRes.failed, errors }
    : { status: checkRes.success, errors: [] };
};

export default checkHeaderCell;
