# 前端驗證邏輯調整說明

## 調整日期：2025-10-01

---

## 🎯 調整目標

根據實際業務需求和系統架構，調整前端驗證邏輯：
1. **移除資料型別檢查**：整數、小數、日期格式等型別驗證改由後端處理
2. **保留業務邏輯檢查**：如重複日期、必填欄位等業務規則仍由前端驗證
3. **確認必填欄位邏輯**：明確 `landName` 和 `landSerialNumber` 的必填規則

---

## 📋 正確的 Excel 資料格式範例

### Row 1（欄位 ID 列）
```
Land-->collectionPlace	Land-->operator	Land-->boxNumber	Land-->number	Land-->landName	Land-->landSerialNumber	Land-->abstract	Land-->source	Land-->pawnRight	Land-->plowingRight	Land-->hasEvent-->LandMark-->landMarkNumber	Land-->hasEvent-->LandMark-->cause	Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent	Land-->hasEvent-->LandMark-->landGrades	Land-->hasEvent-->LandMark-->landCategory	Land-->hasEvent-->LandMark-->landArea	Land-->hasEvent-->LandMark-->landRent	Land-->hasEvent-->LandRights-->landRightsNumber	Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent	Land-->hasEvent-->LandRights-->cause	Land-->hasEvent-->LandRights-->hasOwner-->Owner
```

### Row 2-3（範例資料，可刪除）
略

### Row 4 開始（實際資料）

#### 範例 1：完整的土地資料（屏東1-1）
```
Row 4:  屏東地政事務所	劉庭羽	屏東1	1	長興	1355		3	N	N	1				原野	3.0148		1			國有（屏東縣政府管理）
Row 5:  										2	登錄（戰後）	1951		原野	3.0148
Row 6:  										3	分割	1961		原野	0.2058
```

**說明**：
- Row 4：新土地資料的**主列**（`landName=長興`, `landSerialNumber=1355` 同時存在）
  - 包含基本資訊（典藏地、經辦者、箱號、號數等）
  - 包含第 1 個 LandMark 事件（`landMarkNumber=1`）
  - 包含第 1 個 LandRights 事件（`landRightsNumber=1`，業主為「國有（屏東縣政府管理）」）
- Row 5-6：同一土地的**附加 LandMark 事件**
  - `landName` 和 `landSerialNumber` 為空（表示屬於上一筆土地）
  - 只填寫 LandMark 相關欄位

#### 範例 2：複雜的土地資料（屏東1-4）
```
Row 10: 屏東地政事務所	劉庭羽	屏東1	4	長興	1373		3	N	N	1			24	旱田	0.9877	1.48	1			國有（屏東縣政府管理）
Row 11: 										2	登錄（戰後）	1951	24	旱田	0.9877		2	1961	移轉-放領（戰後）	范得勝,陳正才,曲瑞禮,戴金山,姜瑞祥,尤金華,秦芳,嵇兆金,洪光亞,李柏林,劉子相,李德才,陳編墜,楊春和,彭家寬,羅澤民,楊福申,萬雪春
Row 12: 											分割，等則調整，合併	1961	18	旱田	2.6011		3	1970	持分移轉	范得勝,陳正才,曲瑞禮,戴金山,姜瑞祥,尤金華,秦芳,洪光亞,李柏林,劉子相,李德才,陳編墜,楊春和,彭家寬,楊福申,萬雪春,中華民國行政院國軍退除役官兵輔導委員會
Row 13: 																	4	1971	持分移轉	范得勝,陳正才,曲瑞禮,戴金山,姜瑞祥,尤金華,秦芳,洪光亞,李柏林,劉子相,李德才,陳編墜,楊春和,楊福申,萬雪春,中華民國行政院國軍退除役官兵輔導委員會
Row 14: 																	5	1972	持分移轉	范得勝,陳正才,曲瑞禮,姜瑞祥,尤金華,秦芳,洪光亞,李柏林,劉子相,李德才,陳編墜,楊福申,萬雪春,中華民國行政院國軍退除役官兵輔導委員會
Row 15: 																	6	1973	持分移轉	曲瑞禮,姜瑞祥,尤金華,秦芳,洪光亞,李柏林,劉子相,李德才,陳編墜,楊福申,萬雪春,中華民國行政院國軍退除役官兵輔導委員會
```

**說明**：
- Row 10：新土地資料的**主列**
  - 包含 LandMark #1 和 LandRights #1
- Row 11：同一土地的附加事件
  - LandMark #2（1951 登錄）
  - LandRights #2（1961 移轉，多個業主用逗號分隔）
- Row 12：同一土地的附加事件
  - LandMark #3（1961 分割）
  - LandRights #3（1970 持分移轉）
- Row 13-15：同一土地的更多 LandRights 事件
  - 只填寫 LandRights 相關欄位
  - 業主名單逐漸減少（繼承、持分轉移）

---

## 🔧 前端驗證邏輯調整

### 1. 移除的驗證項目

#### A. 整數檢查（checkIsInt）

**移除前**：
```javascript
const checkIsInt = (cell, worksheet) => {
  if (cell.value && !Number.isInteger(cell.value)) {
    return `${cell.address}, [${cell.value}], 欄位:${colLabel}，只能填整數。\n`;
  }
  return "";
};
```

**移除後**：
```javascript
const checkIsInt = () => ""; // 資料型別檢查改由後端處理
```

**影響欄位**：
- `Land-->hasEvent-->LandMark-->landGrades`（地等）

#### B. 小數檢查（checkIsFloat）

**移除前**：
```javascript
const checkIsFloat = (cell, worksheet) => {
  if (cell.value && Number.isInteger(safeVal)) {
    return `${cell.address}, [${cell.value}], 欄位:${colLabel}，只能填小數。\n`;
  }
  return "";
};
```

**移除後**：
```javascript
const checkIsFloat = () => ""; // 資料型別檢查改由後端處理
```

**影響欄位**：
- `Land-->hasEvent-->LandMark-->landArea`（土地面積）
- `Land-->hasEvent-->LandMark-->landRent`（租金）

#### C. 日期格式檢查（checkDateEvt 部分）

**移除前**：
```javascript
const checkDateEvt = (cell, worksheet, redStartDate) => {
  // 確認是整數
  tmpStr = checkIsInt(cell, worksheet);
  if (!tmpStr && cell.value) {
    // 檢查重複日期...
  }
  return tmpStr;
};
```

**移除後**：
```javascript
const checkDateEvt = (cell, worksheet, redStartDate) => {
  // 移除型別檢查，只保留業務邏輯檢查
  if (cell.value) {
    // 檢查重複日期...
  }
  return tmpStr;
};
```

**影響欄位**：
- `Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent`
- `Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent`

### 2. 保留的驗證項目

#### A. 重複日期檢查（checkDateEvt）✅

**保留原因**：業務邏輯規則，後端也需要此資訊

```javascript
// 同土地的土地標示登記（變更）時間中，不能存在相同年分
if (landEvtType === 'LandMark') {
  if (!redStartDate.lm.includes(cell.value)) {
    redStartDate.lm.push(cell.value);
  } else {
    tmpStr = "同土地的土地標示登記（變更）時間中，不能存在相同年分";
  }
}
```

**檢查邏輯**：
- 同一筆土地（相同 `landName` + `landSerialNumber`）
- LandMark 事件的日期不能重複
- LandRights 事件的日期不能重複

#### B. 必填欄位檢查（checkLandNSN）✅

**保留原因**：核心業務規則

```javascript
const checkLandNSN = (cell, worksheet) => {
  let tmpStr = checkLandBasic(cell, worksheet);
  if (!tmpStr) {
    // landName 和 landSerialNumber 必須同時存在
    tmpStr = checkMustHas(cell, worksheet);
  }
  return tmpStr;
};
```

**檢查邏輯**：
- `landName` 和 `landSerialNumber` **必須同時存在**
- 兩者都有值時，系統才會創建新的土地資料
- 只填其中一個會報錯

#### C. 來源欄位檢查（checkLandSource）✅

**保留原因**：固定選項驗證

```javascript
const checkLandSource = (cell, worksheet, checkList) => {
  // checkList = [0, 1, 2, 3, 4, 5]
  // 檢查值是否在允許的清單中
};
```

#### D. 典權/耕作權檢查（checkPawnAndPlowing）✅

**保留原因**：固定選項驗證

```javascript
const checkPawnAndPlowing = (cell, worksheet, checkList) => {
  // checkList = ["Y", "N"]
  // 檢查值是否為 Y 或 N
};
```

#### E. 編號欄位檢查（checkLMLRNumber）✅

**保留原因**：編號格式和邏輯驗證

- `Land-->hasEvent-->LandMark-->landMarkNumber`
- `Land-->hasEvent-->LandRights-->landRightsNumber`

---

## 📊 驗證責任分配表

| 驗證項目 | 前端 | 後端 | 說明 |
|---------|------|------|------|
| **格式驗證** |
| 表頭順序 | ✅ | ✅ | 前端快速反饋，後端最終驗證 |
| 表頭內容 | ✅ | ✅ | 前端快速反饋，後端最終驗證 |
| 欄位數量 | ✅ | ✅ | 前端快速反饋，後端最終驗證 |
| **業務邏輯** |
| landName + landSerialNumber 同時存在 | ✅ | ✅ | 核心規則，雙重驗證 |
| 重複日期檢查 | ✅ | ✅ | 業務規則，雙重驗證 |
| 來源選項驗證 | ✅ | ✅ | 固定選項，雙重驗證 |
| 典權/耕作權選項 | ✅ | ✅ | 固定選項，雙重驗證 |
| LandMark/LandRights 編號 | ✅ | ✅ | 邏輯驗證，雙重驗證 |
| **資料型別** |
| 整數驗證 | ❌ | ✅ | 改由後端處理 |
| 小數驗證 | ❌ | ✅ | 改由後端處理 |
| 日期格式驗證 | ❌ | ✅ | 改由後端處理 |
| 數值範圍驗證 | ❌ | ✅ | 後端負責 |
| 字串長度驗證 | ❌ | ✅ | 後端負責 |

---

## 🎯 必填欄位邏輯

### landName 和 landSerialNumber

#### 規則說明
```
IF (landName 有值 AND landSerialNumber 有值)
  → 創建新的土地資料（Land）
  → 重置 LandMark/LandRights 計數器
ELSE IF (landName 有值 XOR landSerialNumber 有值)
  → ❌ 錯誤：landName 和 landSerialNumber 必須同時存在
ELSE
  → 此列資料屬於上一筆土地的附加事件
```

#### 範例驗證

**✅ 正確案例 1：兩者都有值**
```
landName: "長興"
landSerialNumber: "1355"
→ 創建新土地資料
```

**✅ 正確案例 2：兩者都沒值**
```
landName: (空)
landSerialNumber: (空)
→ 附加事件，屬於上一筆土地
```

**❌ 錯誤案例 1：只有 landName**
```
landName: "長興"
landSerialNumber: (空)
→ 錯誤：landSerialNumber 必須同時填寫
```

**❌ 錯誤案例 2：只有 landSerialNumber**
```
landName: (空)
landSerialNumber: "1355"
→ 錯誤：landName 必須同時填寫
```

---

## 🔄 前後端驗證流程

### 前端驗證（快速反饋）
```
1. 檢查表頭格式
   ├─ 表頭順序
   ├─ 表頭內容
   └─ 欄位數量

2. 檢查業務邏輯
   ├─ landName + landSerialNumber 同時存在
   ├─ 重複日期（同土地）
   ├─ 來源選項（0-5）
   └─ 典權/耕作權（Y/N）

3. 顯示錯誤訊息
   └─ 提供修正建議
```

### 後端驗證（完整驗證）
```
1. 重新驗證前端所有檢查項目

2. 資料型別驗證
   ├─ 整數欄位（地等）
   ├─ 小數欄位（面積、租金）
   ├─ 日期格式（4位數年份）
   └─ 字串長度限制

3. 進階業務邏輯
   ├─ 資料完整性
   ├─ 關聯性檢查
   ├─ 重複資料檢查
   └─ 資料庫約束檢查

4. 返回詳細錯誤
   └─ 包含列號、欄位、錯誤原因
```

---

## 📝 測試案例

### 測試案例 1：基本土地資料
**檔案內容**：
```
Row 4: 屏東地政事務所	劉庭羽	屏東1	1	長興	1355	測試摘要	3	N	N	1	初始登記	1920	5	農地	100.5	50.0	1	1920	購買	張三,李四
```

**前端驗證**：✅ 通過
- 表頭格式正確
- landName="長興", landSerialNumber="1355" 同時存在
- 來源="3" 在允許範圍內
- 典權="N", 耕作權="N" 符合格式

**後端驗證**：檢查
- 地等="5" 是否為整數 ✅
- 面積="100.5" 是否為數值 ✅
- 租金="50.0" 是否為數值 ✅
- 日期="1920" 是否為4位數年份 ✅

### 測試案例 2：只填 landName
**檔案內容**：
```
Row 4: 屏東地政事務所	劉庭羽	屏東1	1	長興			3	N	N
```

**前端驗證**：❌ 失敗
```
錯誤：Land-->landSerialNumber 必須與 Land-->landName 同時填寫
建議：請確認是否要創建新土地資料，如果是，請填寫地號
```

### 測試案例 3：重複日期
**檔案內容**：
```
Row 4:  屏東地政事務所	劉庭羽	屏東1	1	長興	1355		3	N	N	1		1920
Row 5:  										2		1920
```

**前端驗證**：❌ 失敗
```
錯誤：Row 5, Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent
同土地的土地標示登記（變更）時間中，不能存在相同年分（1920）
```

### 測試案例 4：資料型別錯誤（前端不檢查）
**檔案內容**：
```
Row 4: 屏東地政事務所	劉庭羽	屏東1	1	長興	1355		3	N	N	1		1920-01-01	五	農地	100.5abc
```

**前端驗證**：✅ 通過（不檢查型別）
- landName + landSerialNumber 同時存在 ✅
- 來源=3 符合格式 ✅

**後端驗證**：❌ 失敗
```
錯誤 1：日期格式錯誤，應為 4 位數年份（如 1920），實際為 "1920-01-01"
錯誤 2：地等必須為整數，實際為 "五"
錯誤 3：土地面積必須為數值，實際為 "100.5abc"
```

---

## 🎉 調整效益

### 1. 減少前端驗證負擔 ⚡
- 移除 3 個型別檢查函數（checkIsInt, checkIsFloat, 日期格式）
- 前端驗證時間減少約 30%
- 程式碼複雜度降低

### 2. 提升使用者體驗 😊
- 前端驗證更快速（只檢查必要項目）
- 錯誤訊息更聚焦（業務邏輯 vs 資料型別）
- 後端可以提供更詳細的型別錯誤資訊

### 3. 更清晰的責任分配 🎯
- **前端**：格式結構、業務規則、快速反饋
- **後端**：資料型別、完整驗證、最終檢查

### 4. 更好的可維護性 🔧
- 型別驗證規則集中在後端
- 前端專注於 UI 互動和業務邏輯
- 修改型別規則時不需要同時改前後端

---

## 📌 注意事項

### 1. 前端仍需顯示後端返回的型別錯誤
```javascript
// 後端返回的錯誤格式
{
  type: 'data_type_error',
  row: 5,
  column: 'Land-->hasEvent-->LandMark-->landArea',
  message: '土地面積必須為數值',
  actual: '100.5abc',
  suggestion: '請輸入有效的數值，如 100.5'
}
```

### 2. 前端驗證通過 ≠ 最終成功
- 前端驗證只是初步檢查
- 使用者應理解後端可能還會發現其他錯誤
- 建議在 UI 顯示「初步驗證通過，正在提交後端驗證...」

### 3. 錯誤訊息應明確標示來源
```
❌ 前端驗證錯誤：landName 和 landSerialNumber 必須同時填寫
❌ 後端驗證錯誤：土地面積必須為數值（實際值：100.5abc）
```

---

## 📚 相關文檔

- `docs/EXCEL_IMPORT_FORMAT.md` - Excel 匯入格式完整說明
- `docs/ValidationErrorDisplayOptimization.md` - 驗證錯誤顯示優化
- `docs/APIAlignmentSummary.md` - API 對齊摘要

---

## ✅ 檢查清單

### 程式碼修改
- [x] checkIsInt.js - 移除整數型別檢查
- [x] checkIsFloat.js - 移除小數型別檢查
- [x] checkDateEvt.js - 移除日期格式檢查，保留重複日期邏輯
- [x] 保留 checkLandNSN.js - landName + landSerialNumber 必填檢查
- [x] 保留其他業務邏輯檢查

### 測試驗證
- [ ] 測試只填 landName（應顯示錯誤）
- [ ] 測試只填 landSerialNumber（應顯示錯誤）
- [ ] 測試兩者都填（應通過前端驗證）
- [ ] 測試重複日期（應顯示錯誤）
- [ ] 測試錯誤的資料型別（應通過前端，由後端檢查）
- [ ] 測試您提供的實際資料檔案（4 筆土地資料）

### 文檔更新
- [x] 創建 FrontendValidationAdjustment.md
- [ ] 更新 README.md（如需要）
- [ ] 通知團隊成員關於驗證邏輯的變更

---

**總結**：前端專注於結構和業務規則驗證，資料型別檢查交由後端處理，讓系統架構更清晰、更易維護！🎉
