Warning: NLS unused message: QTEST in: com.kms.katalon.constants.globalMessages
Warning: NLS missing message: QTEST_NAME in: com.kms.katalon.constants.globalMessages
2025-08-19 12:44:14.524 INFO  c.k.katalon.core.main.TestSuiteExecutor  - START Test Suites/Mandarin/v1/Gis
2025-08-19 12:44:14.604 INFO  c.k.katalon.core.main.TestSuiteExecutor  - userFullName = Lin Kai Yee
2025-08-19 12:44:14.605 INFO  c.k.katalon.core.main.TestSuiteExecutor  - projectName = katalon
2025-08-19 12:44:14.606 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostName = saw63 - ************
2025-08-19 12:44:14.606 INFO  c.k.katalon.core.main.TestSuiteExecutor  - os = Windows 11 64bit
2025-08-19 12:44:14.607 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostAddress = ************
2025-08-19 12:44:14.607 INFO  c.k.katalon.core.main.TestSuiteExecutor  - katalonVersion = ********
2025-08-19 12:44:14.960 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-19 12:44:15.070 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 19, 2025 12:44:16 下午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-19 12:44:16.713 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 1a787dca262d6c70829aa2bc6f210b84
2025-08-19 12:44:16.714 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-19 12:44:16.715 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-19 12:44:16.715 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-19 12:44:16.726 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-19 12:44:17.167 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 12:44:17.167 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-19 12:44:17.234 DEBUG testcase.GoToGis                         - 1: openBrowser("")
2025-08-19 12:44:17.239 WARN  c.k.k.core.webui.driver.DriverFactory    - A browser is already opened. Closing browser and opening a new one
2025-08-19 12:44:17.424 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-19 12:44:17.427 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 19, 2025 12:44:18 下午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-19 12:44:18.264 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 51f75141b54369cc80422e8f4548a289
2025-08-19 12:44:18.266 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-19 12:44:18.267 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-19 12:44:18.267 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-19 12:44:18.269 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-19 12:44:18.288 DEBUG testcase.GoToGis                         - 2: navigateToUrl("http://localhost:3000/Gis")
2025-08-19 12:44:19.462 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-19 12:44:19.474 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 12:44:19.474 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-19 12:44:19.550 DEBUG testcase.LoginWithEmail                  - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_"))
2025-08-19 12:44:20.461 DEBUG testcase.LoginWithEmail                  - 2: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail"))
2025-08-19 12:44:20.975 DEBUG testcase.LoginWithEmail                  - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Email_email"), "<EMAIL>")
2025-08-19 12:44:21.816 DEBUG testcase.LoginWithEmail                  - 4: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Next"))
2025-08-19 12:44:22.119 DEBUG testcase.LoginWithEmail                  - 5: setEncryptedText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Password_password"), "0D41p6Ct1VJ35FCiyaw3Gw==")
2025-08-19 12:44:23.062 DEBUG testcase.LoginWithEmail                  - 6: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign In"))
2025-08-19 12:44:23.371 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-19 12:44:23.374 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 12:44:23.374 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-19 12:44:23.454 DEBUG testcase.SearchTextInGis                 - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button__1"))
2025-08-19 12:44:25.827 DEBUG testcase.SearchTextInGis                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input__mui-3"), "新北勢")
2025-08-19 12:44:26.962 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-19 12:44:26.968 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 12:44:26.968 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-19 12:44:27.031 DEBUG testcase.ChoosePerPage                   - 1: dropdownButton = new com.kms.katalon.core.testobject.TestObject()
2025-08-19 12:44:27.038 DEBUG testcase.ChoosePerPage                   - 2: dropdownButton.addProperty("css", EQUALS, "div[role="combobox"]")
2025-08-19 12:44:27.051 DEBUG testcase.ChoosePerPage                   - 3: click(dropdownButton)
2025-08-19 12:44:27.466 DEBUG testcase.ChoosePerPage                   - 4: option50 = new com.kms.katalon.core.testobject.TestObject()
2025-08-19 12:44:27.469 DEBUG testcase.ChoosePerPage                   - 5: option50.addProperty("css", EQUALS, "li.MuiMenuItem-root[data-value="50"]")
2025-08-19 12:44:27.470 DEBUG testcase.ChoosePerPage                   - 6: waitForElementVisible(option50, 5)
2025-08-19 12:44:27.912 DEBUG testcase.ChoosePerPage                   - 7: click(option50)
2025-08-19 12:44:28.524 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-19 12:44:28.527 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 12:44:28.527 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-19 12:44:28.578 DEBUG testcase.EditCoordinates                 - 1: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "148485")
2025-08-19 12:44:29.608 DEBUG testcase.EditCoordinates                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "2406567 ")
2025-08-19 12:44:30.736 DEBUG testcase.EditCoordinates                 - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "148485")
2025-08-19 12:44:31.670 DEBUG testcase.EditCoordinates                 - 4: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "2406567 ")
2025-08-19 12:44:32.773 DEBUG testcase.EditCoordinates                 - 5: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination"))
2025-08-19 12:44:33.602 DEBUG testcase.EditCoordinates                 - 6: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "148485")
2025-08-19 12:44:34.579 DEBUG testcase.EditCoordinates                 - 7: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "2406567 ")
2025-08-19 12:44:35.641 DEBUG testcase.EditCoordinates                 - 8: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination_1"))
2025-08-19 12:44:36.476 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-19 12:44:36.479 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 12:44:36.479 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SaveAndVerify
2025-08-19 12:44:36.526 DEBUG testcase.SaveAndVerify                   - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_save"))
2025-08-19 12:44:36.956 DEBUG testcase.SaveAndVerify                   - 2: waitForElementVisible(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button"), 5)
2025-08-19 12:44:37.430 DEBUG testcase.SaveAndVerify                   - 3: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button"))
2025-08-19 12:44:37.859 DEBUG testcase.SaveAndVerify                   - 4: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "value"), "148485")
2025-08-19 12:44:43.390 DEBUG testcase.SaveAndVerify                   - 5: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "value"), "2406567")
2025-08-19 12:44:43.420 DEBUG testcase.SaveAndVerify                   - 6: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "value"), "148485")
2025-08-19 12:44:43.446 DEBUG testcase.SaveAndVerify                   - 7: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "value"), "2406567")
2025-08-19 12:44:43.471 DEBUG testcase.SaveAndVerify                   - 8: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination"))
2025-08-19 12:44:44.069 DEBUG testcase.SaveAndVerify                   - 9: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "value"), "148485")
2025-08-19 12:44:44.125 DEBUG testcase.SaveAndVerify                   - 10: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "value"), "2406567")
2025-08-19 12:44:44.162 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SaveAndVerify
2025-08-19 12:44:44.406 INFO  c.k.katalon.core.main.TestSuiteExecutor  - --------------------
2025-08-19 12:44:44.407 INFO  c.k.katalon.core.main.TestSuiteExecutor  - END Test Suites/Mandarin/v1/Gis
2025-08-19 12:44:44.407 INFO  c.k.katalon.core.main.TestSuiteExecutor  - ====================
