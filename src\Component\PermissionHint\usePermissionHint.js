import { useMemo } from 'react';
import { getCurrentEnvironment } from '../../utils/environmentUtils';
import { canEdit, canImport, canManageUsers, getUserRoleDisplayText } from '../../utils/permissionUtils';

/**
 * usePermissionHint - 自動生成頁面權限提示的 Hook
 * 
 * @param {Object} user - 使用者物件
 * @param {string} pageType - 頁面類型 ('edit' | 'import' | 'manage' | 'view')
 * @returns {Object} 包含權限提示資訊的物件
 * @returns {boolean} shouldShow - 是否應該顯示提示
 * @returns {string} title - 提示標題
 * @returns {string} message - 提示訊息
 * @returns {string} severity - 嚴重程度 ('info' | 'warning' | 'error')
 */
const usePermissionHint = (user, pageType = 'view') => {
  const environment = getCurrentEnvironment();
  const roleText = getUserRoleDisplayText(user);

  const hint = useMemo(() => {
    if (!user || !user.roleDev) {
      return {
        shouldShow: false,
        title: '',
        message: '',
        severity: 'info',
      };
    }

    // 檢查是否為正式站
    const isProduction = environment === 'production';

    // 根據頁面類型檢查權限
    let hasPermission = true;
    let actionText = '操作';

    switch (pageType) {
      case 'edit':
        hasPermission = canEdit(user);
        actionText = '編輯';
        break;
      case 'import':
        hasPermission = canImport(user);
        actionText = '匯入資料';
        break;
      case 'manage':
        hasPermission = canManageUsers(user);
        actionText = '管理使用者';
        break;
      case 'view':
      default:
        hasPermission = true;
        actionText = '查看';
        break;
    }

    // 如果有完整權限，不顯示提示（除非是正式站的非 admin）
    if (hasPermission) {
      // 在正式站，即使有權限也提示謹慎操作
      if (isProduction && pageType !== 'view') {
        return {
          shouldShow: true,
          title: '正式站環境',
          message: `您目前在正式站環境中，擁有${actionText}權限。請謹慎操作，所有變更將影響實際資料。`,
          severity: 'warning',
        };
      }
      return {
        shouldShow: false,
        title: '',
        message: '',
        severity: 'info',
      };
    }

    // 沒有權限的情況
    let severity = 'info';
    let title = '權限限制';
    let message = '';

    if (isProduction) {
      // 正式站沒有權限 - 警告
      severity = 'warning';
      title = '正式站僅限查看';
      message = `您的角色（${roleText}）在正式站僅能查看資料，無法進行${actionText}操作。如需編輯權限，請聯絡管理員。`;
    } else {
      // 測試站沒有權限 - 提示
      severity = 'info';
      title = '權限提示';
      message = `您的角色（${roleText}）目前無法進行${actionText}操作。如需相關權限，請聯絡管理員。`;
    }

    return {
      shouldShow: true,
      title,
      message,
      severity,
    };
  }, [user, pageType, environment, roleText]);

  return hint;
};

export default usePermissionHint;
