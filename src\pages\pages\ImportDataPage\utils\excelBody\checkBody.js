import checkLandBasic from "./basicRule/checkLandBasic";
import checkLand<PERSON><PERSON> from "./complexRule/checkLandNSN";
import checkLandSource from "./complexRule/checkLandSource";
import checkPawnAndPlowing from "./complexRule/checkPawnAndPlowing";
import checkLMLRNumber from "./basicRule/checkLMLRNumber";
import checkIsInt from "./basicRule/checkIsInt";
import checkIsFloat from "./basicRule/checkIsFloat";
import checkDateEvt from "./complexRule/checkDateEvt";

const checkBody = {
  "Land-->collectionPlace": { method: checkLandBasic },
  "Land-->operator": { method: checkLandBasic },
  "Land-->boxNumber": { method: checkLandBasic },
  "Land-->number": { method: checkLandBasic },
  "Land-->landName": { method: checkLandNSN },
  "Land-->landSerialNumber": { method: checkLandNSN },
  "Land-->abstract": { method: checkLandBasic },
  "Land-->source": { method: checkLandSource },
  "Land-->pawnRight": { method: checkPawnAndPlowing },
  "Land-->plowingRight": { method: checkPawnAndPlowing },
  "Land-->hasEvent-->LandMark-->landMarkNumber": { method: checkLMLRNumber },
  "Land-->hasEvent-->LandMark-->cause": { method: () => "" },
  "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent": {
    method: checkDateEvt,
  },
  "Land-->hasEvent-->LandMark-->landGrades": { method: checkIsInt },
  "Land-->hasEvent-->LandMark-->landCategory": { method: () => "" },
  "Land-->hasEvent-->LandMark-->landArea": { method: checkIsFloat },
  "Land-->hasEvent-->LandMark-->landRent": { method: checkIsFloat },
  "Land-->hasEvent-->LandRights-->landRightsNumber": {
    method: checkLMLRNumber,
  },
  "Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent": {
    method: checkDateEvt,
  },
  "Land-->hasEvent-->LandRights-->cause": { method: () => "" },
  "Land-->hasEvent-->LandRights-->hasOwner-->Owner": { method: () => "" },
};

export default checkBody;
