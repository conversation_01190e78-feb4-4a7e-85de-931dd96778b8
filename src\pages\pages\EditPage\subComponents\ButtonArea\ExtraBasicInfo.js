import React, { useContext, useEffect, useState } from "react";
import { Autocomplete, Grid, TextField } from "@mui/material";
import { StoreContext } from "../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../utils";
import Act from "../../../../../store/actions";
import { checkRole, disableEditFunction, disableFontColor } from "../../common";
import { basicPlaceHolder } from "../../common/phDes";
import { Api, readOntoData } from "../../../../../api/land/Api";

function ExtraBasicInfo() {
  const [state, dispatch] = useContext(StoreContext);
  const { extraBasic } = state.edit;
  const { user } = state;
  const [usersList, setUsersList] = useState([]);

  const handleChange = (value, type) => {
    const tmpBoxNumber = JSON.parse(JSON.stringify(extraBasic));
    const findPlace = tmpBoxNumber.find(
      (element) => element.predicate === type
    );
    if (findPlace) {
      findPlace.value = value;
    }
    dispatch({
      type: Act.SET_EXTRABASIC,
      payload: tmpBoxNumber,
    });
  };

  useEffect(() => {
    const apiStr = Api.getLandOperator();
    readOntoData(apiStr).then((result) => {
      const tmpOperators = result.data
        .filter((el) => el.operator !== "")
        .map((el) => el.operator);
      setUsersList(tmpOperators);
    });
  }, []);

  return (
    <Grid
      item
      container
      xs={12}
      direction="row"
      alignItems="center"
      style={{ columnGap: "1rem" }}
    >
      {!isEmpty(extraBasic) &&
        extraBasic.map((el, index) => (
          <Grid item container xs={2} key={`extraBasic-${index}`}>
            <Grid item style={{ width: "100%" }}>
              {el.predicate !== "operator" && (
                <TextField
                  label={el.object}
                  variant="standard"
                  value={el.value}
                  disabled={disableEditFunction(state)}
                  placeholder={basicPlaceHolder[el.predicate]}
                  onChange={(e) => handleChange(e.target.value, el.predicate)}
                  inputProps={{
                    min: 0,
                    style: { ...disableFontColor.style, textAlign: "center" },
                  }}
                  InputLabelProps={{
                    style: { color: "#1976d2" },
                  }}
                />
              )}
              {el.predicate === "operator" && (
                <Autocomplete
                  options={usersList}
                  fullWidth
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label={el.object}
                      variant="standard"
                      placeholder={
                        checkRole(user, ["admin", "developer"])
                          ? "麻煩管理者更改"
                          : basicPlaceHolder[el.predicate]
                      }
                      autoFocus
                      fullWidth
                      InputLabelProps={{
                        style: { color: "#1976d2" },
                      }}
                      inputProps={{
                        ...params.inputProps,
                        min: 0,
                        style: {
                          ...disableFontColor.style,
                          textAlign: "center",
                        },
                      }}
                      onChange={(evt) =>
                        handleChange(evt.target.value, el.predicate)
                      }
                    />
                  )}
                  value={el.value}
                  disabled={
                    disableEditFunction(state) ||
                    checkRole(user, ["admin", "developer"])
                  }
                  onChange={(e, value) => handleChange(value, el.predicate)}
                />
              )}
            </Grid>
          </Grid>
        ))}
    </Grid>
  );
}

export default ExtraBasicInfo;
