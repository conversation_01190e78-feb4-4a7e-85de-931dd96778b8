import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useHistory } from "react-router-dom";

// material-ui
import Autocomplete from "@mui/material/Autocomplete";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import TextField from "@mui/material/TextField";

// utils
import { Api, readOntoData } from "../../../../../api/land/Api";
import createLandID from "../../../../../utils/createLandID";
import hisAddSearch from "../../../../../utils/hisAddSearch";

function AddLandDialog({ openAddLandDialog, setOpenAddLandDialog }) {
  const history = useHistory();
  const [newLandName, setNewLandName] = useState(""); // 土名輸入
  const [newLandSN, setNewLandSN] = useState(""); // 地號輸入
  const [LNList, setLNList] = useState([]);
  const [tmpLSNList, setTmpLSNList] = useState([]);

  const alertArr = [
    { alert: false, msg: "注意: 土名與地番須同時有值才可以增加。" },
    {
      alert: false,
      msg: "注意: 此地番已存在於此土名，相同土名，不能再新增已存在的地番。",
    },
  ];
  const [alert, setAlert] = useState(alertArr);

  useEffect(() => {
    let tmpAlert = JSON.parse(JSON.stringify(alertArr));
    tmpAlert = tmpAlert.map((el) => ({ ...el, alert: false }));
    setAlert(tmpAlert);
  }, [newLandName, newLandSN]);

  useEffect(() => {
    if (newLandName === "") {
      setNewLandSN("");
      return;
    }
    const apiStr = Api.getLSList().replace("{landName}", newLandName);
    readOntoData(apiStr).then((result) => {
      setTmpLSNList(result.data);
    });
  }, [newLandName]);

  const handleClick = () => {
    const apiStr = Api.getLNList();
    readOntoData(apiStr).then((result) => {
      setLNList(result.data);
    });
  };

  const handleChange = (event, value) => {
    setNewLandName(value || "");
  };

  const handleClose = () => {
    setOpenAddLandDialog(false);
  };

  const handleAdd = async () => {
    const tmpAlert = JSON.parse(JSON.stringify(alertArr));
    // newLandName 與 newLandSN 不能留一個空字串
    if (newLandName !== "" && newLandSN === "") {
      tmpAlert[0].alert = true;
    }

    // 相同newLandName不能再新增newLandSN
    if (tmpLSNList.find((el) => el.landSerialNumber === newLandSN)) {
      tmpAlert[1].alert = true;
    }

    if (tmpAlert.filter((el) => el.alert).length > 0) {
      setAlert(tmpAlert);
      return;
    }

    if (newLandName !== "" && newLandSN !== "") {
      const newID = await createLandID(newLandName, newLandSN);
      hisAddSearch(history, { landId: newID });
    }
    setOpenAddLandDialog(false);
  };

  return (
    <Dialog
      open={openAddLandDialog}
      onClose={handleClose}
      fullWidth
      maxWidth="md"
    >
      <DialogTitle>新增土地</DialogTitle>
      <DialogContent>
        <Autocomplete
          options={LNList.map((element) => element.landName)}
          renderInput={(params) => (
            <TextField
              {...params}
              autoFocus
              fullWidth
              label="土名（舊地段名）"
              variant="standard"
              placeholder="土名（舊地段名）"
              onClick={handleClick}
              onBlur={(evt) => {
                setNewLandName(evt.target.value || "");
              }}
            />
          )}
          value={newLandName}
          onChange={handleChange}
        />
      </DialogContent>
      <DialogContent>
        <TextField
          autoFocus
          label="地番（地號）"
          fullWidth
          variant="standard"
          onChange={(event) => {
            setNewLandSN(event.target.value || "");
          }}
          placeholder="地番（地號）"
          value={newLandSN}
          disabled={newLandName === ""}
        />
      </DialogContent>
      {alert
        .filter((el) => el.alert)
        .map((el, index) => (
          <DialogContent key={index} style={{ color: "red" }}>
            {el.msg}
          </DialogContent>
        ))}
      <DialogActions>
        <Button onClick={handleClose}>取消</Button>
        <Button onClick={handleAdd}>確定</Button>
      </DialogActions>
    </Dialog>
  );
}

AddLandDialog.propTypes = {
  openAddLandDialog: PropTypes.bool,
  setOpenAddLandDialog: PropTypes.func,
};

AddLandDialog.defaultProps = {
  openAddLandDialog: false,
  setOpenAddLandDialog: () => {},
};

export default AddLandDialog;
