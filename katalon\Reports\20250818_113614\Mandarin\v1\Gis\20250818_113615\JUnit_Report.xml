<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Gis" time="27.089" tests="6" failures="0" errors="0">
   <testsuite name="Gis" tests="6" failures="0" errors="0" time="27.089" skipped="0" timestamp="2025-08-18T03:36:18.350Z" hostname="saw63 - ************" id="Test Suites/Mandarin/v1/Gis">
      <properties>
         <property name="deviceName" value=""/>
         <property name="devicePlatform"/>
         <property name="logFolder" value="C:\\project\\land-web\\katalon\\Reports\\20250818_113614\\Mandarin\\v1\\Gis\\20250818_113615"/>
         <property name="logFiles" value="C:\\project\\land-web\\katalon\\Reports\\20250818_113614\\Mandarin\\v1\\Gis\\20250818_113615\\console0.log, C:\\project\\land-web\\katalon\\Reports\\20250818_113614\\Mandarin\\v1\\Gis\\20250818_113615\\execution0.log"/>
         <property name="attachments" value=""/>
         <property name="hostName" value="saw63 - ************"/>
         <property name="os" value="Windows 11 64bit"/>
         <property name="katalonVersion" value="********"/>
         <property name="browser" value="Chrome 138.0.7204.185"/>
         <property name="userFullName" value="Lin Kai Yee"/>
         <property name="hostAddress" value="************"/>
         <property name="sessionId" value="56640e1245e8aac5141f38e3534b59eb"/>
         <property name="projectName" value="katalon"/>
         <property name="seleniumVersion" value="4.28.1"/>
         <property name="proxyInformation" value="ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }"/>
         <property name="platform" value="Windows 11"/>
      </properties>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/GoToGis" time="3.546" classname="Test Cases/Mandarin/v1_web/Gis/GoToGis" status="PASSED">
         <system-out><![CDATA[18-08-2025T11:36:18 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/GoToGis: Test Cases/Mandarin/v1_web/Gis/GoToGis

18-08-2025T11:36:19 - [TEST_STEP][PASSED] - openBrowser(""): Browser is opened with url: ''

18-08-2025T11:36:19 - [MESSAGE][INFO] - Starting 'Chrome' driver

18-08-2025T11:36:19 - [MESSAGE][INFO] - Action delay is set to 0 milliseconds

18-08-2025T11:36:21 - [MESSAGE][PASSED] - Browser is opened with url: ''

18-08-2025T11:36:21 - [TEST_STEP][PASSED] - navigateToUrl("http://localhost:3000/Gis"): Navigate to 'http://localhost:3000/Gis' successfully

18-08-2025T11:36:22 - [MESSAGE][PASSED] - Navigate to 'http://localhost:3000/Gis' successfully]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/LoginWithEmail" time="3.865" classname="Test Cases/Mandarin/v1_web/Gis/LoginWithEmail" status="PASSED">
         <system-out><![CDATA[18-08-2025T11:36:22 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/LoginWithEmail: Test Cases/Mandarin/v1_web/Gis/LoginWithEmail

18-08-2025T11:36:22 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_' is clicked on

18-08-2025T11:36:23 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_' is clicked on

18-08-2025T11:36:23 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail' is clicked on

18-08-2025T11:36:23 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail' is clicked on

18-08-2025T11:36:23 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Email_email"), "<EMAIL>"): Text '<EMAIL>' is set on object 'Object Repository/Mandarin/v1_web/Gis/input_Email_email'

18-08-2025T11:36:24 - [MESSAGE][PASSED] - Text '<EMAIL>' is set on object 'Object Repository/Mandarin/v1_web/Gis/input_Email_email'

18-08-2025T11:36:24 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Next")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_Next' is clicked on

18-08-2025T11:36:25 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_Next' is clicked on

18-08-2025T11:36:25 - [TEST_STEP][PASSED] - setEncryptedText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Password_password"), "0D41p6Ct1VJ35FCiyaw3Gw=="): Text ****** has been set on object 'Object Repository/Mandarin/v1_web/Gis/input_Password_password'

18-08-2025T11:36:26 - [MESSAGE][PASSED] - Text ****** has been set on object 'Object Repository/Mandarin/v1_web/Gis/input_Password_password'

18-08-2025T11:36:26 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign In")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign In' is clicked on

18-08-2025T11:36:26 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign In' is clicked on]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/SearchTextInGis" time="3.171" classname="Test Cases/Mandarin/v1_web/Gis/SearchTextInGis" status="PASSED">
         <system-out><![CDATA[18-08-2025T11:36:26 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/SearchTextInGis: Test Cases/Mandarin/v1_web/Gis/SearchTextInGis

18-08-2025T11:36:26 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button__1")): Object: 'Object Repository/Mandarin/v1_web/Gis/button__1' is clicked on

18-08-2025T11:36:28 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button__1' is clicked on

18-08-2025T11:36:28 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input__mui-3"), "新北勢"): Text '新北勢' is set on object 'Object Repository/Mandarin/v1_web/Gis/input__mui-3'

18-08-2025T11:36:29 - [MESSAGE][PASSED] - Text '新北勢' is set on object 'Object Repository/Mandarin/v1_web/Gis/input__mui-3']]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/ChoosePerPage" time="1.626" classname="Test Cases/Mandarin/v1_web/Gis/ChoosePerPage" status="PASSED">
         <system-out><![CDATA[18-08-2025T11:36:29 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/ChoosePerPage: Test Cases/Mandarin/v1_web/Gis/ChoosePerPage

18-08-2025T11:36:29 - [TEST_STEP][PASSED] - dropdownButton = new com.kms.katalon.core.testobject.TestObject(): null

18-08-2025T11:36:29 - [TEST_STEP][PASSED] - dropdownButton.addProperty("css", EQUALS, "div[role="combobox"]"): null

18-08-2025T11:36:29 - [TEST_STEP][PASSED] - click(dropdownButton): Object: '' is clicked on

18-08-2025T11:36:30 - [MESSAGE][PASSED] - Object: '' is clicked on

18-08-2025T11:36:30 - [TEST_STEP][PASSED] - option50 = new com.kms.katalon.core.testobject.TestObject(): null

18-08-2025T11:36:30 - [TEST_STEP][PASSED] - option50.addProperty("css", EQUALS, "li.MuiMenuItem-root[data-value="50"]"): null

18-08-2025T11:36:30 - [TEST_STEP][PASSED] - waitForElementVisible(option50, 5): Object '' is visible

18-08-2025T11:36:30 - [MESSAGE][PASSED] - Object '' is visible

18-08-2025T11:36:30 - [TEST_STEP][PASSED] - click(option50): Object: '' is clicked on

18-08-2025T11:36:31 - [MESSAGE][PASSED] - Object: '' is clicked on]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/EditCoordinates" time="7.962" classname="Test Cases/Mandarin/v1_web/Gis/EditCoordinates" status="PASSED">
         <system-out><![CDATA[18-08-2025T11:36:31 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/EditCoordinates: Test Cases/Mandarin/v1_web/Gis/EditCoordinates

18-08-2025T11:36:31 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "148485"): Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'

18-08-2025T11:36:32 - [MESSAGE][PASSED] - Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'

18-08-2025T11:36:32 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "2406567 "): Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y'

18-08-2025T11:36:33 - [MESSAGE][PASSED] - Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y'

18-08-2025T11:36:33 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "148485"): Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x'

18-08-2025T11:36:34 - [MESSAGE][PASSED] - Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x'

18-08-2025T11:36:34 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "2406567 "): Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y'

18-08-2025T11:36:35 - [MESSAGE][PASSED] - Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y'

18-08-2025T11:36:35 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination")): Object: 'Object Repository/Mandarin/v1_web/Gis/pagination' is clicked on

18-08-2025T11:36:36 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/pagination' is clicked on

18-08-2025T11:36:36 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "148485"): Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x'

18-08-2025T11:36:37 - [MESSAGE][PASSED] - Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x'

18-08-2025T11:36:37 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "2406567 "): Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y'

18-08-2025T11:36:38 - [MESSAGE][PASSED] - Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y'

18-08-2025T11:36:38 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination_1")): Object: 'Object Repository/Mandarin/v1_web/Gis/pagination_1' is clicked on

18-08-2025T11:36:39 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/pagination_1' is clicked on]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/SaveAndVerify" time="6.351" classname="Test Cases/Mandarin/v1_web/Gis/SaveAndVerify" status="PASSED">
         <system-out><![CDATA[18-08-2025T11:36:39 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/SaveAndVerify: Test Cases/Mandarin/v1_web/Gis/SaveAndVerify

18-08-2025T11:36:39 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_save")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_save' is clicked on

18-08-2025T11:36:39 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_save' is clicked on

18-08-2025T11:36:39 - [TEST_STEP][PASSED] - waitForElementVisible(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button"), 5): Object 'Object Repository/Mandarin/v1_web/Gis/save_modal_save_button' is visible

18-08-2025T11:36:40 - [MESSAGE][PASSED] - Object 'Object Repository/Mandarin/v1_web/Gis/save_modal_save_button' is visible

18-08-2025T11:36:40 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button")): Object: 'Object Repository/Mandarin/v1_web/Gis/save_modal_save_button' is clicked on

18-08-2025T11:36:40 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/save_modal_save_button' is clicked on

18-08-2025T11:36:40 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "value"), "148485"): Actual object '148485' and expected object '148485' are equal

18-08-2025T11:36:44 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x' is: '148485'

18-08-2025T11:36:44 - [MESSAGE][PASSED] - Actual object '148485' and expected object '148485' are equal

18-08-2025T11:36:44 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "value"), "2406567"): Actual object '2406567' and expected object '2406567' are equal

18-08-2025T11:36:44 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y' is: '2406567'

18-08-2025T11:36:44 - [MESSAGE][PASSED] - Actual object '2406567' and expected object '2406567' are equal

18-08-2025T11:36:44 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "value"), "148485"): Actual object '148485' and expected object '148485' are equal

18-08-2025T11:36:44 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x' is: '148485'

18-08-2025T11:36:44 - [MESSAGE][PASSED] - Actual object '148485' and expected object '148485' are equal

18-08-2025T11:36:44 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "value"), "2406567"): Actual object '2406567' and expected object '2406567' are equal

18-08-2025T11:36:44 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y' is: '2406567'

18-08-2025T11:36:44 - [MESSAGE][PASSED] - Actual object '2406567' and expected object '2406567' are equal

18-08-2025T11:36:44 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination")): Object: 'Object Repository/Mandarin/v1_web/Gis/pagination' is clicked on

18-08-2025T11:36:45 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/pagination' is clicked on

18-08-2025T11:36:45 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "value"), "148485"): Actual object '148485' and expected object '148485' are equal

18-08-2025T11:36:45 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x' is: '148485'

18-08-2025T11:36:45 - [MESSAGE][PASSED] - Actual object '148485' and expected object '148485' are equal

18-08-2025T11:36:45 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "value"), "2406567"): Actual object '2406567' and expected object '2406567' are equal

18-08-2025T11:36:45 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y' is: '2406567'

18-08-2025T11:36:45 - [MESSAGE][PASSED] - Actual object '2406567' and expected object '2406567' are equal]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <system-out><![CDATA[18-08-2025T11:36:18 - [TEST_SUITE][PASSED] - Gis: null]]></system-out>
      <system-err><![CDATA[]]></system-err>
   </testsuite>
</testsuites>
