Warning: NLS unused message: QTEST in: com.kms.katalon.constants.globalMessages
Warning: NLS missing message: QTEST_NAME in: com.kms.katalon.constants.globalMessages
2025-08-18 13:17:48.175 INFO  c.k.katalon.core.main.TestSuiteExecutor  - START Test Suites/Mandarin/v1/Gis
2025-08-18 13:17:48.272 INFO  c.k.katalon.core.main.TestSuiteExecutor  - userFullName = Lin Kai Yee
2025-08-18 13:17:48.273 INFO  c.k.katalon.core.main.TestSuiteExecutor  - projectName = katalon
2025-08-18 13:17:48.274 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostName = saw63 - ************
2025-08-18 13:17:48.274 INFO  c.k.katalon.core.main.TestSuiteExecutor  - os = Windows 11 64bit
2025-08-18 13:17:48.274 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostAddress = ************
2025-08-18 13:17:48.276 INFO  c.k.katalon.core.main.TestSuiteExecutor  - katalonVersion = ********
2025-08-18 13:17:48.599 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-18 13:17:48.686 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 18, 2025 1:17:49 下午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-18 13:17:50.360 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 10b5f45ef0c405c98c0769138e2b91fe
2025-08-18 13:17:50.362 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-18 13:17:50.363 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-18 13:17:50.363 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-18 13:17:50.373 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-18 13:17:52.540 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-18 13:17:52.541 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-18 13:17:52.616 DEBUG testcase.LoginWithEmail                  - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_"))
2025-08-18 13:17:53.033 DEBUG testcase.LoginWithEmail                  - 2: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail"))
2025-08-18 13:17:53.552 DEBUG testcase.LoginWithEmail                  - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Email_email"), "<EMAIL>")
2025-08-18 13:17:54.356 DEBUG testcase.LoginWithEmail                  - 4: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Next"))
2025-08-18 13:17:54.658 DEBUG testcase.LoginWithEmail                  - 5: setEncryptedText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Password_password"), "0D41p6Ct1VJ35FCiyaw3Gw==")
2025-08-18 13:17:55.630 DEBUG testcase.LoginWithEmail                  - 6: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign In"))
2025-08-18 13:17:55.925 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-18 13:17:55.934 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-18 13:17:55.934 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-18 13:17:56.046 DEBUG testcase.SearchTextInGis                 - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button__1"))
2025-08-18 13:17:58.979 DEBUG testcase.SearchTextInGis                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input__mui-3"), "新北勢")
2025-08-18 13:18:00.058 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-18 13:18:00.065 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-18 13:18:00.065 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-18 13:18:00.208 DEBUG testcase.ChoosePerPage                   - 1: dropdownButton = new com.kms.katalon.core.testobject.TestObject()
2025-08-18 13:18:00.221 DEBUG testcase.ChoosePerPage                   - 2: dropdownButton.addProperty("css", EQUALS, "div[role="combobox"]")
2025-08-18 13:18:00.235 DEBUG testcase.ChoosePerPage                   - 3: click(dropdownButton)
2025-08-18 13:18:00.590 DEBUG testcase.ChoosePerPage                   - 4: option50 = new com.kms.katalon.core.testobject.TestObject()
2025-08-18 13:18:00.592 DEBUG testcase.ChoosePerPage                   - 5: option50.addProperty("css", EQUALS, "li.MuiMenuItem-root[data-value="50"]")
2025-08-18 13:18:00.596 DEBUG testcase.ChoosePerPage                   - 6: waitForElementVisible(option50, 5)
2025-08-18 13:18:01.101 DEBUG testcase.ChoosePerPage                   - 7: click(option50)
2025-08-18 13:18:01.715 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-18 13:18:01.723 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-18 13:18:01.724 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-18 13:18:01.811 DEBUG testcase.EditCoordinates                 - 1: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "148485")
2025-08-18 13:18:02.923 DEBUG testcase.EditCoordinates                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "2406567 ")
8月 18, 2025 1:18:02 下午 org.openqa.selenium.remote.http.WebSocket$Listener onError
警告: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at java.net.http/jdk.internal.net.http.SocketTube.readAvailable(SocketTube.java:1170)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.read(SocketTube.java:833)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowTask.run(SocketTube.java:181)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.signalReadable(SocketTube.java:774)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$ReadEvent.signalEvent(SocketTube.java:957)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowEvent.handle(SocketTube.java:253)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.handleEvent(HttpClientImpl.java:984)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.lambda$run$3(HttpClientImpl.java:939)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:939)

