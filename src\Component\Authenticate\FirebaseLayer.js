// firebase
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth } from "firebase/auth";
// import "firebase/database";
// import "firebase/firestore";
// import "firebase/analytics";

import AuthListener from "./firebase/AuthListener";
// import RealTimeListener from "./firebase/RealTimeListener";
// import StorageListener from "./firebase/StorageListener";
import firebaseConfig from "../../api/config/config-firebase";

// Initialize Firebase
const fbApp = initializeApp(firebaseConfig);
getAnalytics(fbApp);

const firebaseAuth = getAuth();
// const realTimeDb = firebase.database();
// const firestoreDb = firebase.firestore();

const FirebaseLayer = (props) => {
  AuthListener({ firebaseAuth });
  // RealTimeListener({ firebaseAuth, realTimeDb });
  // StorageListener();
  const { children } = props;

  return children || null;
};

export { FirebaseLayer, fbApp };
