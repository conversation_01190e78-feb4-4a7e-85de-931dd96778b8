# 資訊安全分析：權限管控環境區分

## 執行摘要

**分析結論**：❌ **此專案目前未達到「權限管控區分正式站/測試站」的資訊安全需求**

### 主要發現

1. ✅ **環境配置已區分**：有 `.env.development` 和 `.env.production`
2. ✅ **API 端點已區分**：開發環境與正式環境使用不同的 API
3. ✅ **多選角色已實作**：系統支援多選角色格式（如 `role: "admin,editor,developer"`）
4. ❌ **權限角色未區分環境**：所有環境共用同一套角色定義（`role` 欄位）
5. ❌ **Firebase 配置未區分**：正式站與測試站共用同一個 Firebase 專案
6. ❌ **使用者資料庫未隔離**：正式站與測試站的使用者資料存在同一個資料庫
7. ⚠️ **多選角色邏輯未考慮環境**：現有的多選角色檢查（如 `user.role.split(',').includes('developer')`）未根據環境區分權限

---

## 詳細分析

### 1. 環境配置分析

#### 1.1 環境變數檔案

**現況**：
```bash
# .env.development (開發/測試站)
REACT_APP_IMPORT=true
REACT_APP_API_NODE=https://api2.daoyidh.com/land2

# .env.production (正式站)
REACT_APP_IMPORT=false
REACT_APP_API_NODE=https://api.daoyidh.com/land
```

**評估**：
- ✅ API 端點已區分
- ✅ 功能開關已區分 (REACT_APP_IMPORT)
- ⚠️ 缺少環境識別標記 (如 `REACT_APP_ENV=production`)

#### 1.2 API 配置

**檔案位置**：`src/api/land/Api.js`

```javascript
if (process.env.NODE_ENV === 'production') {
  console.log = () => {}; // 僅關閉 console.log
}

const baseUrl = process.env.REACT_APP_API_NODE;
```

**問題**：
- ⚠️ 環境判斷邏輯簡單，僅用於關閉 console
- ⚠️ 沒有根據環境載入不同的安全配置

---

### 2. 權限角色系統分析

#### 2.1 角色定義

**檔案位置**：`src/config/App-role.js`

```javascript
const role = {
  admin: "admin",
  editor: "editor",
  reader: "reader",
  developer: "developer",
  anonymous: "anonymous",
};
```

**問題**：
- ❌ **所有環境共用同一套角色定義**
- ❌ 沒有區分「正式站管理員」vs「測試站管理員」
- ❌ developer 角色在正式站仍然存在

#### 2.2 權限配置

**檔案位置**：`src/config/App-authority.js`

```javascript
const authority = {
  Search: [role.admin, role.editor, role.developer],
  Edit: [role.admin, role.editor, role.developer],
  Download: [role.admin, role.developer],
  ImportData: [role.admin, role.developer],
  History: [role.admin, role.developer],
  // ... 更多頁面
};
```

**問題**：
- ❌ **權限配置不分環境**，測試站與正式站完全相同
- ❌ developer 角色擁有過多權限（Edit, Download, ImportData）
- ❌ 正式站的 developer 角色可能被誤用

---

### 3. Firebase 配置分析

#### 3.1 Firebase 專案配置

**檔案位置**：`src/api/config/config-firebase.js`

```javascript
const firebaseConfig = {
    apiKey: "AIzaSyBr38oWoJESqQVz_bK_87xaAeeok6YJSGc",
    authDomain: "land-web-4f1ad.firebaseapp.com",
    projectId: "land-web-4f1ad",
    storageBucket: "land-web-4f1ad.appspot.com",
    messagingSenderId: "558111585509",
    appId: "1:558111585509:web:d4f959633b519cc7d8900c",
    measurementId: "G-EFDK0LKB23",
    databaseURL: "land-web-4f1ad-default-rtdb.asia-southeast1.firebasedatabase.app"
};
```

**嚴重問題**：
- 🔴 **所有環境共用同一個 Firebase 專案**
- 🔴 Firebase 配置硬編碼在程式碼中，未使用環境變數
- 🔴 API Key 直接暴露在前端程式碼
- 🔴 測試站與正式站的使用者認證資料混在一起

#### 3.2 Firebase Realtime Database

**檔案位置**：`src/api/firebase/realtimeDatabase.js`

```javascript
// 使用者資料路徑
const getUser = (uid) => {
  return firebase
    .database()
    .ref(`/users/${safeUid}`)
    .once("value")
};

// 設定資料有區分環境
const getFieldSetting = () => {
  const env = process.env.REACT_APP_MODE || "development";
  return firebase
    .database()
    .ref(`settings/fieldSetting/${env}`)
    .once("value")
};
```

**部分問題**：
- ✅ 設定資料 (settings) 有根據 `REACT_APP_MODE` 區分路徑
- ❌ **使用者資料 (/users) 沒有區分環境**
- ❌ 測試站的使用者可能污染正式站使用者清單
- ❌ 正式站的使用者資料可能被測試站誤操作

---

### 4. 認證流程分析

#### 4.1 AuthListener

**檔案位置**：`src/Component/Authenticate/firebase/AuthListener.js`

```javascript
const AuthListener = ({ firebaseAuth }) => {
  useEffect(() => {
    onAuthStateChanged(firebaseAuth, async (userData) => {
      if (userData) {
        // 新增 role key
        const _userInfo = { ...userInfo, role: role.anonymous };
        await setUser(uid, newUserInfo);
      }
    });
  }, [production, refreshAnonymousToken]);
};
```

**問題**：
- ⚠️ 新使用者預設為 `anonymous` 角色
- ❌ 沒有根據環境設定不同的預設權限
- ❌ 測試站與正式站的使用者認證混在一起

#### 4.2 權限檢查

**檔案位置**：`src/route/RouteProtectedHoc.js`

```javascript
const RouteProtectedHoc = ({ component: Component, ...rest }) => {
  const isLogin = JSON.parse(localStorage.getItem("isLogin"));
  const [state, _] = useContext(StoreContext);
  const { user } = state;

  if (isLogin || !isEmpty(user) || rest.public) {
    return <Route {...rest} render={(props) => <Component {...props} />} />;
  }
  return <Route to={{ pathname: "SignIn" }} />;
};
```

**問題**：
- ❌ **僅檢查登入狀態，未檢查環境與角色對應關係**
- ❌ 沒有防止測試站的使用者登入正式站
- ❌ 沒有防止正式站的使用者登入測試站

---

## 風險評估

### 🔴 高風險

1. **使用者資料混用**
   - 正式站與測試站共用 Firebase 使用者資料庫
   - 測試站的假資料可能污染正式站
   - 正式站的真實使用者可能被測試站誤操作

2. **權限誤用**
   - 測試站的 developer 角色可能在正式站擁有過高權限
   - 正式站的 admin 可能誤入測試站進行危險操作

3. **配置洩漏**
   - Firebase API Key 硬編碼在前端程式碼
   - 任何人都可以查看並使用相同的 Firebase 配置

### ⚠️ 中風險

4. **角色混淆**
   - developer 角色在正式站與測試站意義不同
   - 沒有明確區分「正式站管理員」vs「測試站管理員」

5. **資料隔離不足**
   - 設定資料 (settings) 雖有區分，但使用者資料沒有
   - 缺少完整的環境隔離策略

---

## 安全需求對照

| 需求項目 | 現況 | 是否達標 |
|---------|------|---------|
| 正式站/測試站環境分離 | API 端點分離，但 Firebase 未分離 | ❌ |
| 使用者資料隔離 | 共用同一個 Firebase 專案 | ❌ |
| 權限角色分離 | 所有環境共用角色定義 | ❌ |
| 環境識別機制 | 僅有 NODE_ENV，缺少明確的環境標記 | ⚠️ |
| 跨環境存取防護 | 沒有檢查機制 | ❌ |

---

## 改進建議（根據團隊決策調整）

### 團隊決策摘要

經過團隊討論，確認以下技術方案：

1. ✅ **共用 Firebase 專案**：正式站與測試站使用同一個 Firebase 專案
2. ✅ **雙角色機制**：使用者資料包含 `role`（正式站）和 `roleDev`（測試站）
3. ✅ **Firebase 配置硬編碼**：維持現有方式，不改為環境變數
4. ✅ **Developer 角色保留**：正式站保留 developer 角色，但限制編輯權限

### 改進方案：雙角色 + 環境感知權限控制

#### 架構設計

```
Firebase Project (共用)
├─ Authentication (共用)
├─ Realtime Database
│  └─ users/{uid}
│     ├─ role: "admin" | "editor" | "reader" | "developer" | "anonymous"  (正式站)
│     ├─ roleDev: "admin" | "editor" | "reader" | "developer" | "anonymous"  (測試站)
│     ├─ email: "<EMAIL>"
│     └─ displayName: "使用者名稱"
│
├─ API 端點
│  ├─ 正式站: api.daoyidh.com
│  └─ 測試站: api2.daoyidh.com
│
└─ 權限策略
   ├─ 正式站 developer: 可查看，不可編輯
   └─ 測試站 developer: 可查看 + 可編輯
```

#### 實作步驟

**步驟 1：新增環境識別標記**

更新環境變數檔案：

`.env.development`：
```bash
# 開發/測試站配置
REACT_APP_ENV=development
REACT_APP_IMPORT=true
REACT_APP_API_NODE=https://api2.daoyidh.com/land2
```

`.env.production`：
```bash
# 正式站配置
REACT_APP_ENV=production
REACT_APP_IMPORT=false
REACT_APP_API_NODE=https://api.daoyidh.com/land
```

**步驟 2：更新使用者資料結構（雙角色機制 + 多選角色支援）**

⚠️ **重要**：現有系統使用**多選角色格式**（逗號分隔字串），如 `role: "admin,editor,developer"`

在 Firebase Realtime Database 中，使用者資料結構應包含兩個角色欄位（皆支援多選）：

```json
{
  "users": {
    "user123": {
      "uid": "user123",
      "email": "<EMAIL>",
      "displayName": "管理員",
      "role": "admin",           // 正式站角色（單選或多選）
      "roleDev": "admin",        // 測試站角色（單選或多選）
      "createdAt": "2025-01-01T00:00:00Z"
    },
    "user456": {
      "uid": "user456",
      "email": "<EMAIL>",
      "displayName": "開發者",
      "role": "developer",       // 正式站: developer (限制編輯)
      "roleDev": "developer",    // 測試站: developer (完整權限)
      "createdAt": "2025-01-01T00:00:00Z"
    },
    "user789": {
      "uid": "user789",
      "email": "<EMAIL>",
      "displayName": "編輯者",
      "role": "editor",          // 正式站: editor
      "roleDev": "admin,developer",  // 測試站: 多選（提升權限）
      "createdAt": "2025-01-01T00:00:00Z"
    },
    "user999": {
      "uid": "user999",
      "email": "<EMAIL>",
      "displayName": "多重角色使用者",
      "role": "reader,editor",   // 正式站: 多選角色（reader + editor）
      "roleDev": "admin,editor,developer",  // 測試站: 多選角色（更高權限）
      "createdAt": "2025-01-01T00:00:00Z"
    }
  }
}
```

**多選角色格式說明**：
- 單選：`"admin"`, `"editor"`, `"developer"`, `"reader"`, `"anonymous"`
- 多選：`"admin,editor"`, `"reader,editor,developer"` （逗號分隔，無空格）
- 檢查方法：`user.role.split(',').includes('admin')`

**步驟 2.5：多選角色處理策略**

⚠️ **重要設計決策**：系統支援多選角色（如 `role: "admin,editor,developer"`）

**多選角色的權限判斷原則**：
1. **「最高權限優先」**：使用者擁有的多個角色中，取最高權限
   - 例如：`role: "reader,admin"` → 視為 admin 權限
   - 順序：admin > developer > editor > reader > anonymous

2. **環境限制仍然適用**：即使有多個角色，developer 在正式站的限制依然有效
   - 例如：`role: "developer,editor"` 在正式站 → 仍不可編輯（因為有 developer）
   - 例如：`roleDev: "admin,developer"` 在測試站 → admin 權限（最高優先）

3. **權限檢查邏輯**：
   ```javascript
   // 檢查是否包含特定角色
   if (roles.includes('admin')) {
     // 有 admin 權限，允許所有操作
   }
   
   // 檢查是否包含 developer（需要環境限制）
   if (roles.includes('developer') && !roles.includes('admin')) {
     // 有 developer 但沒有 admin，套用環境限制
   }
   ```

**範例**：
- `role: "reader,editor"` 在正式站 → 可查看 + 可編輯（editor 權限）
- `role: "developer,editor"` 在正式站 → 可查看，不可編輯（developer 限制優先於 editor）
- `role: "admin,developer"` 在正式站 → 完整權限（admin 權限最高）
- `roleDev: "admin,editor,developer"` 在測試站 → 完整權限（admin 權限最高）

---

**步驟 3：建立環境感知工具（支援多選角色）**

新增 `src/utils/environmentUtils.js`：

```javascript
/**
 * 環境相關工具函數
 */

/**
 * 取得當前環境
 * @returns {'production' | 'development'}
 */
export const getCurrentEnvironment = () => {
  return process.env.REACT_APP_ENV || 'development';
};

/**
 * 檢查是否為正式站
 * @returns {boolean}
 */
export const isProduction = () => {
  return getCurrentEnvironment() === 'production';
};

/**
 * 檢查是否為測試站
 * @returns {boolean}
 */
export const isDevelopment = () => {
  return getCurrentEnvironment() === 'development';
};

/**
 * 取得環境顯示名稱
 * @returns {string}
 */
export const getEnvironmentLabel = () => {
  const labels = {
    production: '正式站',
    development: '測試站',
  };
  return labels[getCurrentEnvironment()] || '未知環境';
};

/**
 * 根據環境取得使用者的角色（支援多選角色）
 * @param {Object} user - 使用者物件
 * @returns {string} 角色字串（可能為多選，如 "admin,editor"）
 */
export const getUserRoleByEnvironment = (user) => {
  if (!user) return 'anonymous';
  
  if (isProduction()) {
    return user.role || 'anonymous';
  } else {
    return user.roleDev || user.role || 'anonymous';
  }
};

/**
 * 根據環境取得使用者的角色陣列（支援多選角色）
 * @param {Object} user - 使用者物件
 * @returns {string[]} 角色陣列，如 ["admin", "editor"]
 */
export const getUserRolesArrayByEnvironment = (user) => {
  const roleString = getUserRoleByEnvironment(user);
  return roleString.split(',').map(r => r.trim()).filter(r => r);
};

/**
 * 檢查使用者是否擁有特定角色（支援多選角色）
 * @param {Object} user - 使用者物件
 * @param {string} targetRole - 要檢查的角色名稱
 * @returns {boolean}
 */
export const hasRole = (user, targetRole) => {
  const roles = getUserRolesArrayByEnvironment(user);
  return roles.includes(targetRole);
};
```

**步驟 4：更新 AuthListener 以支援雙角色**

修改 `src/Component/Authenticate/firebase/AuthListener.js`：

```javascript
import { getUserRoleByEnvironment } from "../../../utils/environmentUtils";

const AuthListener = ({ firebaseAuth }) => {
  // ... 現有程式碼

  useEffect(() => {
    onAuthStateChanged(firebaseAuth, async (userData) => {
      if (userData) {
        const userInfo = getFormatUser(userData);
        const { uid } = userInfo;
        
        // 從 Realtime DB 取得使用者資料
        const dbUserData = await getUser(uid);
        
        if (isEmpty(dbUserData)) {
          // 新使用者：初始化雙角色
          const newUserInfo = { 
            ...userInfo, 
            role: 'anonymous',      // 正式站預設角色
            roleDev: 'anonymous'    // 測試站預設角色
          };
          await setUser(uid, JSON.parse(JSON.stringify(newUserInfo)));
          
          dispatch({
            type: act.FIREBASE_LOGIN_USER,
            payload: { 
              ...newUserInfo,
              currentRole: getUserRoleByEnvironment(newUserInfo) 
            },
          });
        } else {
          // 現有使用者：根據環境載入對應角色
          const currentRole = getUserRoleByEnvironment(dbUserData);
          
          dispatch({
            type: act.FIREBASE_LOGIN_USER,
            payload: { 
              ...dbUserData,
              currentRole  // 當前環境的有效角色
            },
          });
        }
      }
    });
  }, [production, refreshAnonymousToken]);
};
```

**步驟 5：環境感知的角色定義（保持現有角色）**

保持 `src/config/App-role.js` 不變（所有環境都有相同角色定義）：

```javascript
const role = {
  admin: "admin",
  editor: "editor",
  reader: "reader",
  developer: "developer",  // 正式站與測試站都有，但權限不同
  anonymous: "anonymous",
};

export default role;
```

**步驟 6：環境感知的權限配置（頁面存取層級）**

修改 `src/config/App-authority.js`：

```javascript
import role from "./App-role";

// 頁面存取權限（所有環境相同）
const authority = {
  Home: [role.admin, role.editor, role.reader, role.developer, role.anonymous],
  SignIn: [role.admin, role.editor, role.reader, role.developer, role.anonymous],
  SignOut: [role.admin, role.editor, role.reader, role.developer, role.anonymous],
  
  // 查詢功能
  Search: [role.admin, role.editor, role.developer],
  
  // 編輯功能頁面（developer 可進入，但編輯功能受環境限制）
  Edit: [role.admin, role.editor, role.developer],
  Gis: [role.admin, role.editor, role.developer],
  ImportData: [role.admin, role.developer],
  
  // 管理功能
  Download: [role.admin, role.developer],
  Authority: [role.admin, role.developer],  // developer 可查看，但不可編輯
  Admin: [role.admin, role.developer],
  History: [role.admin, role.developer],
  Statistics: [role.admin, role.developer],
  Monitor: [role.admin, role.developer],
};

export default authority;
```

**步驟 7：建立操作權限控制**

新增 `src/utils/permissionUtils.js`：

```javascript
import { isProduction, getUserRoleByEnvironment } from './environmentUtils';
import role from '../config/App-role';

/**
 * 操作權限類型
 */
export const OPERATION = {
  VIEW: 'view',           // 查看
  EDIT: 'edit',           // 編輯
  DELETE: 'delete',       // 刪除
  IMPORT: 'import',       // 匯入
  EXPORT: 'export',       // 匯出
  MANAGE_USERS: 'manage_users',  // 管理使用者
};

/**
 * 檢查使用者是否可以執行特定操作（支援多選角色）
 * @param {Object} user - 使用者物件
 * @param {string} operation - 操作類型
 * @param {string} page - 頁面名稱（選填）
 * @returns {boolean}
 */
export const canPerformOperation = (user, operation, page = null) => {
  if (!user) return false;
  
  // 取得使用者的所有角色（陣列）
  const roles = getUserRolesArrayByEnvironment(user);
  const isProd = isProduction();
  
  // Admin 在所有環境都有完整權限
  if (roles.includes(role.admin)) {
    return true;
  }
  
  // Developer 權限根據環境區分
  if (roles.includes(role.developer)) {
    switch (operation) {
      case OPERATION.VIEW:
        return true;  // 所有環境都可查看
        
      case OPERATION.EDIT:
        // 正式站：不可編輯資料庫
        // 測試站：可編輯
        return !isProd;
        
      case OPERATION.DELETE:
        // 正式站：不可刪除
        // 測試站：可刪除
        return !isProd;
        
      case OPERATION.IMPORT:
        // 正式站：禁止匯入
        // 測試站：允許匯入
        return !isProd;
        
      case OPERATION.EXPORT:
        // 所有環境都可匯出
        return true;
        
      case OPERATION.MANAGE_USERS:
        // 所有環境都不可管理使用者（僅 admin）
        return false;
        
      default:
        return false;
    }
  }
  
  // Editor 權限
  if (roles.includes(role.editor)) {
    switch (operation) {
      case OPERATION.VIEW:
      case OPERATION.EDIT:
        return true;
      case OPERATION.DELETE:
      case OPERATION.IMPORT:
      case OPERATION.EXPORT:
      case OPERATION.MANAGE_USERS:
        return false;
      default:
        return false;
    }
  }
  
  // Reader 權限
  if (roles.includes(role.reader)) {
    return operation === OPERATION.VIEW;
  }
  
  return false;
};

/**
 * 檢查是否可以編輯（快捷方法）
 * @param {Object} user - 使用者物件
 * @returns {boolean}
 */
export const canEdit = (user) => {
  return canPerformOperation(user, OPERATION.EDIT);
};

/**
 * 檢查是否可以匯入資料（快捷方法）
 * @param {Object} user - 使用者物件
 * @returns {boolean}
 */
export const canImport = (user) => {
  return canPerformOperation(user, OPERATION.IMPORT);
};

/**
 * 檢查是否可以管理使用者（快捷方法）
 * @param {Object} user - 使用者物件
 * @returns {boolean}
 */
export const canManageUsers = (user) => {
  return canPerformOperation(user, OPERATION.MANAGE_USERS);
};

/**
 * 取得操作權限說明文字
 * @param {Object} user - 使用者物件
 * @param {string} operation - 操作類型
 * @returns {string}
 */
export const getPermissionMessage = (user, operation) => {
  const currentRole = getUserRoleByEnvironment(user);
  const isProd = isProduction();
  const envLabel = isProd ? '正式站' : '測試站';
  
  if (currentRole === role.developer && isProd) {
    const messages = {
      [OPERATION.EDIT]: `Developer 角色在${envLabel}僅能查看資料，不可編輯`,
      [OPERATION.DELETE]: `Developer 角色在${envLabel}僅能查看資料，不可刪除`,
      [OPERATION.IMPORT]: `Developer 角色在${envLabel}禁止匯入資料`,
      [OPERATION.MANAGE_USERS]: `僅 Admin 可管理使用者權限`,
    };
    return messages[operation] || '您沒有執行此操作的權限';
  }
  
  return '您沒有執行此操作的權限';
};
```

**步驟 8：更新路由保護（使用雙角色 + 多選角色支援）**

修改 `src/route/RouteProtectedHoc.js`（使用雙角色機制，支援多選角色）：

```javascript
import React, { useContext } from "react";
import { Route } from "react-router-dom";
import { bool, any, object } from "prop-types";

import { StoreContext } from "../store/StoreProvider";
import { isEmpty } from "../utils";
import { getUserRoleByEnvironment, getUserRolesArrayByEnvironment } from "../utils/environmentUtils";

const RouteProtectedHoc = ({ component: Component, ...rest }) => {
  const isLogin = JSON.parse(localStorage.getItem("isLogin"));
  const [state, _] = useContext(StoreContext);
  const { user } = state;

  // 使用雙角色機制：根據環境取得有效角色（支援多選）
  if (!isEmpty(user)) {
    const currentRole = getUserRoleByEnvironment(user);  // 角色字串（可能多選）
    const currentRoles = getUserRolesArrayByEnvironment(user);  // 角色陣列
    
    // 將當前角色資訊注入到 user 物件中，供子組件使用
    user.currentRole = currentRole;  // 保持向後相容：字串格式
    user.currentRoles = currentRoles;  // 新增：陣列格式（建議使用）
  }

  if (isLogin || !isEmpty(user) || rest.public) {
    return <Route {...rest} render={(props) => <Component {...props} />} />;
  }

  return <Route to={{ pathname: "SignIn" }} />;
};

RouteProtectedHoc.propTypes = {
  component: any,
  isLoggedIn: bool,
  rest: object,
  props: object,
};

export default RouteProtectedHoc;
```

**步驟 9：在編輯頁面中應用操作權限控制**

範例：修改 `src/pages/pages/EditPage/index.js`：

```javascript
import { canEdit, getPermissionMessage, OPERATION } from '../../../utils/permissionUtils';
import { getUserRoleByEnvironment, getEnvironmentLabel } from '../../../utils/environmentUtils';

function EditPage() {
  const [state] = useContext(StoreContext);
  const { user } = state;
  
  const canUserEdit = canEdit(user);
  const currentRole = getUserRoleByEnvironment(user);
  
  // 在保存按鈕中檢查權限
  const handleSave = () => {
    if (!canUserEdit) {
      alert(getPermissionMessage(user, OPERATION.EDIT));
      return;
    }
    
    // 執行保存邏輯
    // ...
  };
  
  return (
    <div>
      {/* 顯示環境與角色資訊 */}
      <Box sx={{ mb: 2, p: 1, bgcolor: 'info.light', borderRadius: 1 }}>
        <Typography variant="caption">
          當前環境: {getEnvironmentLabel()} | 角色: {currentRole}
        </Typography>
      </Box>
      
      {/* 編輯表單 */}
      <form>
        {/* ... 表單欄位 ... */}
        
        <Button 
          onClick={handleSave}
          disabled={!canUserEdit}
          variant="contained"
        >
          儲存
        </Button>
        
        {!canUserEdit && (
          <Typography variant="caption" color="error" sx={{ mt: 1 }}>
            {getPermissionMessage(user, OPERATION.EDIT)}
          </Typography>
        )}
      </form>
    </div>
  );
}
```

**步驟 10：在匯入資料頁面中應用權限控制**

範例：修改 `src/pages/pages/ImportDataPage/index.js`：

```javascript
import { canImport, getPermissionMessage, OPERATION } from '../../../utils/permissionUtils';
import { getUserRoleByEnvironment, getEnvironmentLabel, isProduction } from '../../../utils/environmentUtils';

function ImportDataPage() {
  const [state] = useContext(StoreContext);
  const { user } = state;
  
  const canUserImport = canImport(user);
  const currentRole = getUserRoleByEnvironment(user);
  
  // 如果沒有匯入權限，顯示警告
  if (!canUserImport) {
    return (
      <Container>
        <Alert severity="warning" sx={{ mt: 2 }}>
          <AlertTitle>權限不足</AlertTitle>
          {getPermissionMessage(user, OPERATION.IMPORT)}
          <br />
          當前環境: {getEnvironmentLabel()} | 您的角色: {currentRole}
        </Alert>
      </Container>
    );
  }
  
  // 原有的匯入邏輯
  return (
    <div>
      {/* 匯入介面 */}
    </div>
  );
}
```

**步驟 11：在權限管理頁面中應用權限控制**

範例：修改 `src/pages/pages/AuthorityPage/index.js`：

```javascript
import { canManageUsers, getPermissionMessage, OPERATION } from '../../../utils/permissionUtils';
import { getUserRoleByEnvironment } from '../../../utils/environmentUtils';

function AuthorityPage() {
  const [state] = useContext(StoreContext);
  const { user } = state;
  
  const canManage = canManageUsers(user);
  const currentRole = getUserRoleByEnvironment(user);
  
  // Developer 可查看，但不可編輯
  return (
    <Container>
      <Typography variant="h4">權限管理</Typography>
      
      {/* 使用者清單（所有角色都可查看） */}
      <UserList users={users} readOnly={!canManage} />
      
      {/* 編輯按鈕（僅 admin 可用） */}
      {!canManage && (
        <Alert severity="info" sx={{ mt: 2 }}>
          您的角色 ({currentRole}) 僅能查看權限設定，不可編輯。
          {getPermissionMessage(user, OPERATION.MANAGE_USERS)}
        </Alert>
      )}
    </Container>
  );
}
```

**步驟 12：Firebase Realtime Database 使用者資料遷移（支援多選角色）**

建立資料遷移腳本，為現有使用者新增 `roleDev` 欄位（保持多選格式）：

```javascript
// scripts/migrateUserRoles.js
// 執行此腳本為所有現有使用者新增 roleDev 欄位
// ⚠️ 支援多選角色格式（如 "admin,editor,developer"）

import firebase from "firebase/compat/app";
import "firebase/compat/database";
import firebaseConfig from "../src/api/config/config-firebase";

// 初始化 Firebase
firebase.initializeApp(firebaseConfig);

const migrateUsers = async () => {
  try {
    const usersRef = firebase.database().ref('/users');
    const snapshot = await usersRef.once('value');
    const users = snapshot.val();
    
    if (!users) {
      console.log('沒有找到使用者資料');
      return;
    }
    
    const updates = {};
    let updatedCount = 0;
    let skippedCount = 0;
    
    Object.entries(users).forEach(([uid, userData]) => {
      // 如果沒有 roleDev 欄位，則新增
      if (!userData.roleDev) {
        // 預設：roleDev 與 role 相同（保持多選格式）
        const roleValue = userData.role || 'anonymous';
        updates[`/users/${uid}/roleDev`] = roleValue;
        updatedCount++;
        
        console.log(`✓ 更新使用者 ${uid}:`);
        console.log(`  - role: ${roleValue}`);
        console.log(`  - roleDev: ${roleValue} (新增)`);
        
        // 如果是多選角色，顯示拆解後的角色
        if (roleValue.includes(',')) {
          const roles = roleValue.split(',').map(r => r.trim());
          console.log(`  - 角色清單: [${roles.join(', ')}]`);
        }
      } else {
        skippedCount++;
        console.log(`- 跳過使用者 ${uid}: 已有 roleDev 欄位 (role=${userData.role}, roleDev=${userData.roleDev})`);
      }
    });
    
    if (Object.keys(updates).length > 0) {
      console.log('\n開始更新資料庫...');
      await firebase.database().ref().update(updates);
      console.log(`\n✅ 遷移完成！`);
      console.log(`   - 更新: ${updatedCount} 個使用者`);
      console.log(`   - 跳過: ${skippedCount} 個使用者（已有 roleDev）`);
    } else {
      console.log('\n✅ 所有使用者已有 roleDev 欄位，無需更新');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('\n❌ 遷移失敗:', error);
    process.exit(1);
  }
};

migrateUsers();
```

執行方式：
```bash
node scripts/migrateUserRoles.js
```

**遷移腳本特點**：
- ✅ 自動保留多選角色格式（如 `"admin,editor"` → `roleDev: "admin,editor"`）
- ✅ 顯示每個使用者的角色拆解資訊
- ✅ 跳過已有 `roleDev` 欄位的使用者
- ✅ 批次更新，減少資料庫寫入次數

---

### 優勢與風險分析

#### 優勢

1. **無需建立新 Firebase 專案**
   - 降低管理複雜度
   - 無需處理專案遷移

2. **資料集中管理**
   - 使用者資料在同一處
   - 便於跨環境角色管理

3. **向後相容**
   - 既有使用者無需重新註冊
   - 平滑遷移至雙角色機制

4. **靈活的權限配置**
   - 可為每個使用者獨立設定測試站與正式站權限
   - 支援「測試站 admin + 正式站 reader」等組合

#### 風險

1. **資料混用風險（中等）**
   - 正式站與測試站使用者資料在同一資料庫
   - 需依賴程式邏輯正確區分環境

2. **程式複雜度增加（低）**
   - 需要在多處檢查 `role` vs `roleDev`
   - 增加維護成本

3. **錯誤配置風險（中等）**
   - 若忘記檢查環境，可能誤用角色
   - 需完善的測試覆蓋

#### 風險緩解措施

1. **統一使用工具函數**
   - 所有地方都使用 `getUserRoleByEnvironment()` 取得角色
   - 避免直接存取 `user.role`

2. **完整的測試覆蓋**
   - 測試正式站 developer 的權限限制
   - 測試測試站 developer 的完整權限

3. **UI 提示**
   - 在介面上明確顯示當前環境
   - 權限不足時提供清楚的錯誤訊息

4. **程式碼審查規範**
   - 禁止直接使用 `user.role`
   - 必須透過 `getUserRoleByEnvironment()` 取得角色

---

## 實作優先順序（根據團隊決策調整）

### Phase 1：基礎架構（1 週）🔴

**目標**：建立雙角色機制與環境識別系統

1. **新增環境識別標記**
   - 在 `.env.development` 和 `.env.production` 新增 `REACT_APP_ENV`
   - 確保 build 時正確載入環境變數
   - 優先級：🔴 最高
   - 預計工時：0.5 天

2. **建立環境工具函數（支援多選角色）**
   - 實作 `src/utils/environmentUtils.js`
   - 包含 `getCurrentEnvironment()`, `isProduction()`, `getUserRoleByEnvironment()` 等
   - **新增多選角色支援**：`getUserRolesArrayByEnvironment()`, `hasRole()` 等
   - ⚠️ **重要**：處理多選角色格式（如 `"admin,editor,developer"`）
   - 優先級：🔴 最高
   - 預計工時：1 天（增加 0.5 天以處理多選角色邏輯）

3. **建立操作權限工具函數（支援多選角色）**
   - 實作 `src/utils/permissionUtils.js`
   - 包含 `canPerformOperation()`, `canEdit()`, `canImport()`, `canManageUsers()` 等
   - 定義權限規則：正式站 developer 限制編輯
   - **權限判斷邏輯**：
     * Admin 最高優先（忽略其他角色）
     * Developer 環境限制（正式站不可編輯，測試站可編輯）
     * 其他角色取最高權限（editor > reader > anonymous）
   - 優先級：🔴 最高
   - 預計工時：1.5 天（增加 0.5 天以處理多選角色權限判斷）

4. **Firebase 使用者資料結構更新（保留多選格式）**
   - 執行資料遷移腳本，為所有使用者新增 `roleDev` 欄位
   - 預設 `roleDev` = `role`（保持向後相容 + 保留多選格式）
   - ⚠️ **重要**：多選角色格式自動保留（如 `"admin,editor"` → `roleDev: "admin,editor"`）
   - 優先級：🔴 最高
   - 預計工時：0.5 天

5. **更新 AuthListener**
   - 支援雙角色載入機制
   - 根據環境設定 `currentRole`
   - 優先級：🔴 最高
   - 預計工時：1 天

**Phase 1 驗收標準**：
- ✅ 環境變數正確設定並可讀取
- ✅ 工具函數可正確判斷環境與角色
- ✅ 所有現有使用者都有 `role` 和 `roleDev` 欄位
- ✅ 登入時可正確載入對應環境的角色

---

### Phase 2：頁面權限控制（1.5 週）🟠

**目標**：在關鍵頁面實作操作權限檢查

6. **編輯頁面 (EditPage) 權限控制**
   - 檢查 `canEdit()` 權限
   - 正式站 developer: 禁用儲存/刪除按鈕
   - 測試站 developer: 完整編輯權限
   - 顯示權限提示訊息
   - 優先級：🟠 高
   - 預計工時：1 天

7. **經緯度頁面 (GisPage) 權限控制**
   - 檢查 `canEdit()` 權限
   - 正式站 developer: 僅可查看，不可修改座標
   - 測試站 developer: 可修改座標
   - 優先級：🟠 高
   - 預計工時：1 天

8. **匯入資料頁面 (ImportDataPage) 權限控制**
   - 檢查 `canImport()` 權限
   - 正式站 developer: 完全禁止存取頁面（顯示警告訊息）
   - 測試站 developer: 可匯入資料
   - 優先級：🟠 高
   - 預計工時：0.5 天

9. **權限管理頁面 (AuthorityPage) 權限控制**
   - 檢查 `canManageUsers()` 權限
   - Developer (所有環境): 僅可查看，不可編輯使用者權限
   - Admin (所有環境): 可查看 + 編輯
   - 顯示「僅 Admin 可編輯使用者權限」提示
   - 優先級：🟠 高
   - 預計工時：1 天

10. **其他編輯功能頁面檢查**
    - 檢查所有涉及資料修改的頁面
    - 統一套用 `canEdit()` 權限檢查
    - 優先級：🟠 高
    - 預計工時：1 天

**Phase 2 驗收標準**：
- ✅ 正式站 developer 無法編輯資料
- ✅ 正式站 developer 無法匯入資料
- ✅ 正式站 developer 可查看但無法編輯使用者權限
- ✅ 測試站 developer 有完整編輯權限
- ✅ 所有限制都有清楚的 UI 提示

---

### Phase 3：UI/UX 改善（1 週）🟡

**目標**：提升使用者體驗，清楚標示環境與權限

11. **全域環境標示**
    - 在 Header 或固定位置顯示當前環境（正式站/測試站）
    - 正式站：紅色標籤
    - 測試站：藍色標籤
    - 優先級：🟡 中
    - 預計工時：0.5 天

12. **角色資訊顯示**
    - 在使用者資訊區顯示當前角色
    - 格式：「角色: Developer (測試站)」
    - 優先級：🟡 中
    - 預計工時：0.5 天

13. **權限提示優化**
    - 統一權限不足的錯誤訊息樣式
    - 提供更友善的說明文字
    - 加入「為什麼我不能操作」的說明
    - 優先級：🟡 中
    - 預計工時：1 天

14. **按鈕狀態優化**
    - 無權限的按鈕顯示為 disabled
    - 加入 Tooltip 說明原因
    - 優先級：🟡 中
    - 預計工時：1 天

**Phase 3 驗收標準**：
- ✅ 使用者可清楚知道當前在哪個環境
- ✅ 使用者可清楚知道自己的角色
- ✅ 無權限操作時，UI 有明確提示
- ✅ 錯誤訊息清楚說明原因

---

### Phase 4：測試與驗證（1 週）🟢

**目標**：確保系統正確運作，無安全漏洞

15. **單元測試**
    - 測試 `environmentUtils.js` 所有函數
    - 測試 `permissionUtils.js` 所有權限規則
    - 優先級：� 中
    - 預計工時：1 天

16. **整合測試**
    - 測試正式站 developer 的所有限制
    - 測試測試站 developer 的完整權限
    - 測試 admin 在兩個環境的完整權限
    - 優先級：� 中
    - 預計工時：1 天

17. **跨環境測試**
    - 在正式站與測試站分別部署
    - 驗證環境變數正確載入
    - 驗證權限規則正確套用
    - 優先級：🟢 中
    - 預計工時：1 天

18. **使用者驗收測試 (UAT)**
    - 邀請真實使用者測試
    - 收集回饋並調整
    - 優先級：🟢 中
    - 預計工時：2 天

**Phase 4 驗收標準**：
- ✅ 所有單元測試通過
- ✅ 所有整合測試通過
- ✅ 跨環境部署測試通過
- ✅ 使用者回饋正面

---

### Phase 5：文檔與維護（持續）🟢

**目標**：完善文檔，建立維護機制

19. **開發文檔**
    - 撰寫權限系統設計文檔
    - 撰寫 API 使用說明
    - 更新程式碼註解
    - 優先級：🟢 低
    - 預計工時：1 天

20. **使用者手冊**
    - 撰寫角色權限說明
    - 說明正式站 vs 測試站的差異
    - 提供常見問題解答 (FAQ)
    - 優先級：🟢 低
    - 預計工時：1 天

21. **監控機制**
    - 記錄權限檢查失敗的事件
    - 建立異常告警
    - 優先級：🟢 低
    - 預計工時：1 天

22. **定期審查機制**
    - 每季度檢查使用者角色設定
    - 檢查是否有異常權限配置
    - 優先級：🟢 低
    - 持續進行

**Phase 5 驗收標準**：
- ✅ 開發文檔完整且易懂
- ✅ 使用者手冊清楚說明權限規則
- ✅ 監控機制運作正常
- ✅ 定期審查機制建立

---

## 總時程估算

| Phase | 工作內容 | 預計工時 | 優先級 | 備註 |
|-------|---------|---------|--------|------|
| Phase 1 | 基礎架構（雙角色 + 多選角色） | 4 天 | 🔴 最高 | +0.5 天處理多選角色 |
| Phase 2 | 頁面權限控制 | 5.5 天 | 🟠 高 | 多選角色邏輯已含 |
| Phase 3 | UI/UX 改善 | 3 天 | 🟡 中 | |
| Phase 4 | 測試與驗證（含多選角色測試） | 6 天 | 🟢 中 | +1 天多選角色測試 |
| Phase 5 | 文檔與維護 | 3 天 + 持續 | 🟢 低 | |
| **總計** | **21.5 天（約 4.5 週）** | | | |

**建議執行順序**：
1. **第 1 週**：Phase 1 完成（3.5 天）+ Phase 2 開始（1.5 天）
2. **第 2 週**：Phase 2 完成（4 天）+ Phase 3 開始（1 天）
3. **第 3 週**：Phase 3 完成（2 天）+ Phase 4 完成（3 天）
4. **第 4 週**：Phase 4 完成（2 天）+ Phase 5 開始（3 天）

---

## 測試計畫（根據雙角色機制調整）

### 測試案例 1：雙角色機制驗證（含多選角色）

**測試目標**：驗證使用者在不同環境使用不同角色（包含多選角色）

**測試步驟 1-1：單選角色**
1. 在 Firebase Realtime Database 建立測試使用者：
   ```json
   {
     "uid": "test-user-001",
     "email": "<EMAIL>",
     "displayName": "測試使用者",
     "role": "reader",        // 正式站: reader
     "roleDev": "developer"   // 測試站: developer
   }
   ```
2. 在測試站登入此使用者
3. 驗證取得的角色為 `developer`
4. 切換到正式站（重新 build + deploy）
5. 登入同一使用者
6. 驗證取得的角色為 `reader`

**測試步驟 1-2：多選角色**
1. 建立多選角色測試使用者：
   ```json
   {
     "uid": "test-user-002",
     "email": "<EMAIL>",
     "displayName": "多選角色使用者",
     "role": "reader,editor",             // 正式站: reader + editor
     "roleDev": "admin,editor,developer"  // 測試站: admin + editor + developer
   }
   ```
2. 在測試站登入
3. 驗證 `getUserRolesArrayByEnvironment(user)` 回傳 `["admin", "editor", "developer"]`
4. 驗證 `hasRole(user, 'admin')` 回傳 `true`
5. 切換到正式站
6. 驗證 `getUserRolesArrayByEnvironment(user)` 回傳 `["reader", "editor"]`
7. 驗證 `hasRole(user, 'admin')` 回傳 `false`

**預期結果**：
- ✅ 測試站（單選）：`getUserRoleByEnvironment(user)` 回傳 `"developer"`
- ✅ 正式站（單選）：`getUserRoleByEnvironment(user)` 回傳 `"reader"`
- ✅ 測試站（多選）：`getUserRolesArrayByEnvironment(user)` 回傳 `["admin", "editor", "developer"]`
- ✅ 正式站（多選）：`getUserRolesArrayByEnvironment(user)` 回傳 `["reader", "editor"]`
- ✅ 相同使用者在不同環境有不同權限

---

### 測試案例 2：正式站 Developer 編輯限制

**測試目標**：驗證 developer 在正式站不可編輯資料

**測試步驟**：
1. 建立使用者：`role: "developer"`, `roleDev: "developer"`
2. 在正式站登入
3. 進入編輯頁面 (EditPage)
4. 嘗試修改資料並點擊「儲存」
5. 嘗試刪除資料
6. 進入經緯度頁面 (GisPage)
7. 嘗試修改座標

**預期結果**：
- ✅ `canEdit(user)` 回傳 `false`
- ✅ 儲存按鈕被禁用或顯示錯誤訊息
- ✅ 刪除按鈕被禁用
- ✅ 經緯度編輯功能被禁用
- ✅ 顯示提示：「Developer 角色在正式站僅能查看資料，不可編輯」

---

### 測試案例 3：測試站 Developer 完整權限

**測試目標**：驗證 developer 在測試站有完整編輯權限

**測試步驟**：
1. 使用相同使用者（`role: "developer"`, `roleDev: "developer"`）
2. 在測試站登入
3. 進入編輯頁面 (EditPage)
4. 修改資料並點擊「儲存」
5. 刪除測試資料
6. 進入經緯度頁面 (GisPage)
7. 修改座標
8. 進入匯入資料頁面 (ImportDataPage)
9. 上傳並匯入測試檔案

**預期結果**：
- ✅ `canEdit(user)` 回傳 `true`
- ✅ 可成功儲存資料
- ✅ 可成功刪除資料
- ✅ 可成功修改座標
- ✅ `canImport(user)` 回傳 `true`
- ✅ 可成功匯入資料

---

### 測試案例 4：正式站 Developer 匯入限制

**測試目標**：驗證 developer 在正式站完全禁止匯入資料

**測試步驟**：
1. 以 developer 身份在正式站登入
2. 嘗試進入匯入資料頁面 (ImportDataPage)

**預期結果**：
- ✅ `canImport(user)` 回傳 `false`
- ✅ 顯示警告訊息：「Developer 角色在正式站禁止匯入資料」
- ✅ 無法存取匯入功能

---

### 測試案例 5：權限管理頁面限制

**測試目標**：驗證 developer 可查看但不可編輯使用者權限

**測試步驟**：
1. 以 developer 身份登入（測試站與正式站都要測）
2. 進入權限管理頁面 (AuthorityPage)
3. 查看使用者清單
4. 嘗試修改其他使用者的 `role` 或 `roleDev`

**預期結果**：
- ✅ `canManageUsers(user)` 回傳 `false`
- ✅ 可查看使用者清單
- ✅ 編輯按鈕被禁用
- ✅ 顯示提示：「僅 Admin 可編輯使用者權限」
- ✅ 測試站與正式站行為一致

---

### 測試案例 6：Admin 完整權限

**測試目標**：驗證 admin 在所有環境都有完整權限

**測試步驟**：
1. 建立 admin 使用者：`role: "admin"`, `roleDev: "admin"`
2. 在正式站測試所有功能
3. 在測試站測試所有功能

**預期結果**：
- ✅ 正式站：可編輯、刪除、匯入、管理使用者
- ✅ 測試站：可編輯、刪除、匯入、管理使用者
- ✅ 所有權限檢查都回傳 `true`

---

### 測試案例 7：環境顯示與角色顯示

**測試目標**：驗證 UI 正確顯示環境與角色資訊

**測試步驟**：
1. 在測試站登入
2. 檢查 Header 或固定位置的環境標示
3. 檢查使用者資訊區的角色顯示
4. 切換到正式站
5. 再次檢查環境與角色顯示

**預期結果**：
- ✅ 測試站：顯示「測試站」標籤（藍色）
- ✅ 正式站：顯示「正式站」標籤（紅色）
- ✅ 角色顯示格式：「角色: Developer (測試站)」
- ✅ 環境與角色資訊清楚易懂

---

### 測試案例 8：資料遷移驗證

**測試目標**：驗證現有使用者成功新增 `roleDev` 欄位

**測試步驟**：
1. 執行資料遷移腳本
2. 檢查 Firebase Realtime Database
3. 驗證所有使用者都有 `role` 和 `roleDev` 欄位
4. 驗證預設值正確（`roleDev` = `role`）

**預期結果**：
- ✅ 所有使用者都有 `roleDev` 欄位
- ✅ `roleDev` 預設值與 `role` 相同
- ✅ 現有使用者可正常登入
- ✅ 無資料遺失或錯誤

---

### 測試案例 9：多選角色權限優先順序

**測試目標**：驗證多選角色的權限判斷邏輯（最高權限優先）

**測試步驟**：
1. 建立多選角色測試使用者：
   ```json
   {
     "uid": "test-multi-001",
     "role": "developer,editor",  // 正式站: developer + editor
     "roleDev": "developer,editor" // 測試站: developer + editor
   }
   ```
2. 在正式站登入
3. 檢查 `canEdit(user)` → 應為 `false`（developer 限制優先）
4. 切換到測試站
5. 檢查 `canEdit(user)` → 應為 `true`（developer 在測試站可編輯）

**測試步驟 2：Admin 最高權限測試**
1. 建立測試使用者：
   ```json
   {
     "uid": "test-multi-002",
     "role": "admin,developer,editor",  // 正式站: admin + developer + editor
     "roleDev": "admin,developer"
   }
   ```
2. 在正式站登入
3. 檢查 `canEdit(user)` → 應為 `true`（admin 權限最高，忽略 developer 限制）
4. 檢查 `canImport(user)` → 應為 `true`
5. 檢查 `canManageUsers(user)` → 應為 `true`

**測試步驟 3：Reader + Editor 組合**
1. 建立測試使用者：
   ```json
   {
     "uid": "test-multi-003",
     "role": "reader,editor",  // reader + editor
     "roleDev": "reader,editor"
   }
   ```
2. 檢查權限：
   - `canEdit(user)` → `true`（有 editor）
   - `canImport(user)` → `false`（reader + editor 都不可匯入）

**預期結果**：
- ✅ Developer + Editor 在正式站：不可編輯（developer 限制優先）
- ✅ Developer + Editor 在測試站：可編輯
- ✅ Admin + Developer：admin 權限優先，忽略 developer 限制
- ✅ Reader + Editor：取 editor 權限（較高）
- ✅ 權限判斷邏輯正確：admin > developer（環境相關）> editor > reader > anonymous

---

### 測試案例 10：跨環境部署驗證

**測試目標**：驗證環境變數在部署時正確載入

**測試步驟**：
1. 使用 `npm run build` 建置正式站版本
2. 檢查 build 輸出，確認 `REACT_APP_ENV=production`
3. 部署到正式站伺服器
4. 在瀏覽器開啟正式站
5. 開啟開發者工具 Console
6. 執行 `console.log(process.env.REACT_APP_ENV)`
7. 重複步驟 1-6 測試測試站

**預期結果**：
- ✅ 正式站：`process.env.REACT_APP_ENV` = `"production"`
- ✅ 測試站：`process.env.REACT_APP_ENV` = `"development"`
- ✅ 環境變數正確載入且不可在前端修改

---

## 測試矩陣

### 單選角色權限矩陣

| 測試項目 | Admin (正式) | Admin (測試) | Developer (正式) | Developer (測試) | Editor (正式) | Editor (測試) |
|---------|-------------|-------------|-----------------|-----------------|--------------|--------------|
| 查看資料 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| 編輯資料 | ✅ | ✅ | ❌ | ✅ | ✅ | ✅ |
| 刪除資料 | ✅ | ✅ | ❌ | ✅ | ❌ | ❌ |
| 匯入資料 | ✅ | ✅ | ❌ | ✅ | ❌ | ❌ |
| 匯出資料 | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ |
| 管理使用者 | ✅ | ✅ | ❌ (僅查看) | ❌ (僅查看) | ❌ | ❌ |

### 多選角色權限矩陣

| 測試項目 | Admin+Dev (正式) | Admin+Dev (測試) | Dev+Editor (正式) | Dev+Editor (測試) | Reader+Editor (正式) | Reader+Editor (測試) |
|---------|-----------------|-----------------|------------------|------------------|---------------------|---------------------|
| 查看資料 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| 編輯資料 | ✅ (admin優先) | ✅ (admin優先) | ❌ (dev限制優先) | ✅ | ✅ (editor權限) | ✅ (editor權限) |
| 刪除資料 | ✅ (admin優先) | ✅ (admin優先) | ❌ (dev限制優先) | ✅ | ❌ | ❌ |
| 匯入資料 | ✅ (admin優先) | ✅ (admin優先) | ❌ (dev限制優先) | ✅ | ❌ | ❌ |
| 匯出資料 | ✅ | ✅ | ✅ (dev有匯出權) | ✅ | ❌ | ❌ |
| 管理使用者 | ✅ (admin優先) | ✅ (admin優先) | ❌ (僅查看) | ❌ (僅查看) | ❌ | ❌ |

**權限判斷邏輯**：
- 🔴 **Admin 最高優先**：只要包含 admin，就有完整權限（忽略其他角色）
- 🟠 **Developer 環境限制**：在正式站有限制，在測試站無限制
- 🟡 **其他角色累加**：取最高權限（editor > reader > anonymous）

**圖例**：
- ✅ 允許操作
- ❌ 禁止操作
- ❌ (僅查看) 可查看但不可編輯
- (角色優先) 說明由哪個角色決定權限

---

## 結論與建議（根據團隊決策更新）

### 現況評估

此專案**尚未達到「權限管控區分正式站/測試站」的資訊安全需求**。主要問題在於：

1. **❌ 無環境識別機制**：缺少 `REACT_APP_ENV` 環境變數
2. **❌ 單一角色系統**：使用者只有 `role` 欄位，無法區分正式站/測試站權限
3. **❌ 無操作權限控制**：頁面僅檢查存取權限，未檢查操作權限（編輯、刪除、匯入）
4. **❌ Developer 權限過高**：正式站的 developer 可以編輯與匯入資料

### 改善方案

根據團隊決策，採用**雙角色機制 + 多選角色支援 + 環境感知權限控制**：

#### 核心策略

1. **✅ 共用 Firebase 專案**
   - 正式站與測試站使用同一個 Firebase 專案
   - 降低管理複雜度

2. **✅ 雙角色機制（保留多選格式）**
   - `role`：正式站使用的角色（支援多選，如 `"admin,editor"`）
   - `roleDev`：測試站使用的角色（支援多選，如 `"admin,developer"`）
   - 系統根據 `REACT_APP_ENV` 自動選擇對應角色

3. **✅ 多選角色權限判斷**
   - **Admin 最高優先**：只要包含 admin，就有完整權限
   - **Developer 環境限制**：在正式站有限制，在測試站無限制
   - **其他角色累加**：取最高權限（editor > reader > anonymous）
   - 範例：`role: "developer,editor"` 在正式站 → 不可編輯（developer 限制優先）

4. **✅ 環境感知權限控制**
   - 正式站 developer：僅能查看，不可編輯/刪除/匯入
   - 測試站 developer：完整權限（編輯/刪除/匯入）
   - 所有環境的 developer：可查看權限管理頁面，但不可編輯使用者

5. **✅ Firebase 配置維持硬編碼**
   - 無需改為環境變數
   - 保持現有架構

6. **✅ 向後相容**
   - 現有的多選角色邏輯保持運作
   - 新增環境感知層，不影響現有功能

### 實作路徑

建議按照以下順序執行：

1. **🔴 Phase 1：基礎架構（1 週）**
   - 新增環境識別標記
   - 建立工具函數
   - 更新使用者資料結構（新增 `roleDev`）
   - 執行資料遷移

2. **🟠 Phase 2：頁面權限控制（1.5 週）**
   - 編輯頁面權限控制
   - 經緯度頁面權限控制
   - 匯入資料頁面權限控制
   - 權限管理頁面權限控制

3. **🟡 Phase 3：UI/UX 改善（1 週）**
   - 環境標示
   - 角色顯示
   - 權限提示優化

4. **🟢 Phase 4-5：測試、文檔、維護（1.5 週）**
   - 完整測試
   - 撰寫文檔
   - 建立監控機制

### 預期成果

實作完成後，系統將達到以下目標：

1. **✅ 環境隔離**
   - 正式站與測試站有明確區分
   - 使用者可清楚知道當前環境

2. **✅ 權限分離**
   - Developer 在正式站權限受限
   - Developer 在測試站有完整權限
   - Admin 在所有環境都有完整權限

3. **✅ 資料安全**
   - 正式站資料不會被誤修改
   - 測試站可自由測試
   - 操作都有明確的權限檢查

4. **✅ 使用者體驗**
   - 清楚的環境與角色標示
   - 友善的權限提示訊息
   - 無權限操作時提供明確說明

### 關鍵風險與緩解

| 風險 | 影響 | 緩解措施 |
|-----|-----|---------|
| 資料混用 | 中 | 統一使用 `getUserRoleByEnvironment()`，避免直接存取 `user.role` |
| 程式複雜度 | 低 | 建立清楚的工具函數，統一權限檢查邏輯 |
| 測試覆蓋不足 | 中 | 建立完整的測試矩陣，涵蓋所有角色與環境組合 |
| 誤配置 | 中 | 程式碼審查時檢查是否正確使用工具函數 |

### 下一步行動

**立即開始**：
1. 建立 Phase 1 工作分支
2. 實作環境工具函數
3. 執行使用者資料遷移腳本
4. 測試雙角色機制

**預計時程**：4 週完成所有 Phase

**成功指標**：
- ✅ 正式站 developer 無法編輯資料
- ✅ 測試站 developer 有完整權限
- ✅ 所有測試案例通過
- ✅ 使用者回饋正面

---

## 參考資源

- [Firebase 多環境配置最佳實踐](https://firebase.google.com/docs/projects/multiprojects)
- [React 環境變數管理](https://create-react-app.dev/docs/adding-custom-environment-variables/)
- [資訊安全：環境隔離原則](https://owasp.org/www-project-web-security-testing-guide/)

---

**文件建立日期**：2025-10-02  
**分析者**：GitHub Copilot  
**專案版本**：當前版本  
**下次審查日期**：實作完成後
