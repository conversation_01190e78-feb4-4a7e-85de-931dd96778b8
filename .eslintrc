{
	"parser": "@babel/eslint-parser",
	"env": {
		"browser": true,
		"es6": true,
		"commonjs": true
	},
	"extends": ["airbnb", "prettier"],
	"parserOptions": {
		"ecmaVersion": 6,
		"ecmaFeatures": {
			"experimentalObjectRestSpread": true,
			"jsx": true
		},
		"requireConfigFile": false,
		"sourceType": "module"
	},
	"plugins": ["prettier", "react"],
	"globals": {
		"process": true, // for DefinePlugin provide env
		"tinymce": true,
		"jest": true,
		"describe": true,
		"it": true,
		"expect": true
	},
	"rules": {
		"react/jsx-filename-extension": [1, { "extensions": [".js", ".jsx"] }],
		"prettier/prettier": "off",
		"linebreak-style": "off",
		"import/no-named-as-default": "off",
		"jsx-a11y/no-static-element-interactions": "off",
		"jsx-a11y/click-events-have-key-events": "off",
		"jsx-a11y/no-noninteractive-element-interactions": "off",
		"react/jsx-props-no-spreading": "off",
		"react/jsx-fragments": "off",
		"react/no-array-index-key": "off",
		"react/function-component-definition": "off",
		"react/prop-types": "off",
		"default-param-last": "off",
		"no-restricted-syntax": "off",
		"radix": "off",
		"no-restricted-globals": "off",
		"consistent-return": "off",
		"no-shadow": "off",
		"no-param-reassign": "off"
	}
}
