/**
 * 環境相關工具函數
 * 
 * 用途：
 * 1. 判斷當前執行環境（正式站 vs 測試站）
 * 2. 根據環境取得使用者的有效角色
 * 3. 支援多選角色格式（如 "admin,editor,developer"）
 */

/**
 * 取得當前環境
 * @returns {'production' | 'development'} 當前環境名稱
 */
export const getCurrentEnvironment = () => process.env.REACT_APP_ENV || 'development';

/**
 * 檢查是否為正式站
 * @returns {boolean}
 */
export const isProduction = () => getCurrentEnvironment() === 'production';

/**
 * 檢查是否為測試站/開發站
 * @returns {boolean}
 */
export const isDevelopment = () => getCurrentEnvironment() === 'development';

/**
 * 取得環境顯示名稱
 * @returns {string} 環境名稱（正式站 | 測試站）
 */
export const getEnvironmentLabel = () => {
  const labels = {
    production: '正式站',
    development: '測試站',
  };
  return labels[getCurrentEnvironment()] || '未知環境';
};

/**
 * 根據環境取得使用者的角色字串
 * 
 * 邏輯：
 * - 正式站：使用 user.role
 * - 測試站：優先使用 user.roleDev，若無則使用 user.role
 * 
 * 支援多選角色格式，如：
 * - 單選: "admin", "editor", "developer"
 * - 多選: "admin,editor", "reader,editor,developer"
 * 
 * @param {Object} user - 使用者物件
 * @param {string} user.role - 正式站角色（支援多選）
 * @param {string} [user.roleDev] - 測試站角色（支援多選）
 * @returns {string} 角色字串（可能為多選，如 "admin,editor"）
 */
export const getUserRoleByEnvironment = (user) => {
  if (!user) return 'anonymous';
  
  if (isProduction()) {
    // 正式站：使用 role
    return user.role || 'anonymous';
  }
  
  // 測試站：優先使用 roleDev，若無則使用 role
  return user.roleDev || user.role || 'anonymous';
};

/**
 * 根據環境取得使用者的角色陣列（支援多選角色）
 * 
 * 將多選角色字串拆分為陣列，方便後續權限檢查
 * 
 * 範例：
 * - "admin" → ["admin"]
 * - "admin,editor" → ["admin", "editor"]
 * - "reader,editor,developer" → ["reader", "editor", "developer"]
 * 
 * @param {Object} user - 使用者物件
 * @returns {string[]} 角色陣列，如 ["admin", "editor"]
 */
export const getUserRolesArrayByEnvironment = (user) => {
  const roleString = getUserRoleByEnvironment(user);
  
  // 拆分多選角色，移除空白，過濾空字串
  return roleString
    .split(',')
    .map(r => r.trim())
    .filter(r => r);
};

/**
 * 檢查使用者是否擁有特定角色（支援多選角色）
 * 
 * @param {Object} user - 使用者物件
 * @param {string} targetRole - 要檢查的角色名稱
 * @returns {boolean}
 * 
 * @example
 * // 使用者: { role: "admin,editor" }
 * hasRole(user, 'admin')   // true
 * hasRole(user, 'editor')  // true
 * hasRole(user, 'reader')  // false
 */
export const hasRole = (user, targetRole) => {
  const roles = getUserRolesArrayByEnvironment(user);
  return roles.includes(targetRole);
};

/**
 * 取得環境顏色標記（用於 UI 顯示）
 * @returns {Object} { color, bgColor, label }
 */
export const getEnvironmentBadge = () => {
  if (isProduction()) {
    return {
      color: '#ffffff',
      bgColor: '#d32f2f',  // 紅色（警告）
      label: '正式站',
    };
  }
  
  return {
    color: '#ffffff',
    bgColor: '#1976d2',  // 藍色（安全）
    label: '測試站',
  };
};

/**
 * 檢查是否需要顯示環境警告
 * 
 * 正式站顯示警告，測試站不顯示
 * 
 * @returns {boolean}
 */
export const shouldShowEnvironmentWarning = () => isProduction();

/**
 * 取得環境相關的設定路徑（用於 Firebase Realtime Database）
 * 
 * 現有系統已使用此邏輯，如：
 * settings/fieldSetting/${env}
 * 
 * @returns {string} 'production' | 'development'
 */
export const getEnvironmentPathKey = () => getCurrentEnvironment();

// ===== 除錯工具 =====

/**
 * 印出當前環境資訊（僅開發環境）
 * 
 * @param {Object} user - 使用者物件
 */
export const logEnvironmentInfo = (user) => {
  if (isDevelopment()) {
    /* eslint-disable no-console */
    console.group('🌍 Environment Info');
    console.log('Environment:', getCurrentEnvironment());
    console.log('Label:', getEnvironmentLabel());
    console.log('Is Production:', isProduction());
    
    if (user) {
      console.log('User Role (raw):', user.role);
      console.log('User RoleDev (raw):', user.roleDev);
      console.log('Current Role String:', getUserRoleByEnvironment(user));
      console.log('Current Roles Array:', getUserRolesArrayByEnvironment(user));
    }
    
    console.groupEnd();
    /* eslint-enable no-console */
  }
};
