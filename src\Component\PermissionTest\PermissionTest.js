import React, { useContext } from "react";
import { StoreContext } from "../../../store/StoreProvider";
import { allowGetIn, menus } from "../../../config/App-layout";
import role from "../../../config/App-role";

/**
 * 權限測試組件 - 用於開發階段測試不同角色的菜單可見性
 * 在生產環境中應該移除此組件
 */
const PermissionTest = () => {
  const [state] = useContext(StoreContext);
  const { user } = state;
  const currentRole = user?.role;

  const testRoles = [
    role.admin,
    role.editor,
    role.reader,
    role.developer,
    role.anonymous,
  ];

  const getVisibleMenus = (testRole) =>
    menus.menuLeft.filter((menu) => allowGetIn(menu, testRole));

  return (
    <div
      style={{
        padding: "20px",
        border: "1px solid var(--color-border)",
        margin: "10px",
        borderRadius: "12px",
        background: "var(--color-surface)",
        boxShadow: "var(--shadow-surface)",
      }}
    >
      <h3>權限測試 - 菜單可見性</h3>
      <p>
        <strong>當前使用者角色:</strong> {currentRole || "未登入"}
      </p>

      <div style={{ marginTop: "20px" }}>
        {testRoles.map((testRole) => {
          const visibleMenus = getVisibleMenus(testRole);
          return (
            <div
              key={testRole}
              style={{
                marginBottom: "15px",
                padding: "10px",
                backgroundColor:
                  testRole === currentRole
                    ? "var(--color-primary-tint)"
                    : "var(--color-surface-alt)",
                borderRadius: "8px",
              }}
            >
              <h4
                style={{
                  margin: "0 0 10px 0",
                  color:
                    testRole === currentRole
                      ? "var(--color-primary)"
                      : "var(--color-text-secondary)",
                }}
              >
                {testRole} {testRole === currentRole ? "(當前角色)" : ""}
              </h4>
              <div>
                <strong>可見菜單項目:</strong>
                {visibleMenus.length > 0 ? (
                  <ul style={{ margin: "5px 0", paddingLeft: "20px" }}>
                    {visibleMenus.map((menu) => (
                      <li key={menu.id}>
                        {menu.name} ({menu.path})
                      </li>
                    ))}
                  </ul>
                ) : (
                  <span style={{ color: "var(--color-text-muted)" }}>
                    {" "}
                    無可見菜單項目
                  </span>
                )}
              </div>
            </div>
          );
        })}
      </div>

      <div
        style={{
          marginTop: "20px",
          fontSize: "12px",
          color: "var(--color-text-muted)",
        }}
      >
        <p>
          <strong>注意:</strong> 此組件僅用於開發測試，生產環境中應該移除。
        </p>
      </div>
    </div>
  );
};

export default PermissionTest;
