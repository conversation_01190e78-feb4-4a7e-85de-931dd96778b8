import { specRow } from "../../../config";
import getLMLRNumber from "./getLMLRNumber";
/**
 * 檢查說明: 同一列的landMarkNumber & landRightsNumber欄位至少1個1才可以填
 * */
const checkLandBasic = (cell, worksheet) => {
  const colLabel = worksheet
    .getRow(specRow.labelIdx)
    .getCell(cell.fullAddress.col).value;

  const specialColVal = getLMLRNumber(cell, worksheet);
  let tmpResStr = "";

  if (cell.value && !specialColVal.includes("1")) {
    const reason = "登記次序、權利登記次序，至少1個1，此欄位才可以填";
    tmpResStr += `${cell.address}, [${cell.value}], 欄位:${colLabel}，${reason}。\n`;
  }

  return tmpResStr;
};

export default checkLandBasic;
