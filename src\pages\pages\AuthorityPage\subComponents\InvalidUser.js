import React, { useContext } from 'react';
import {
  Checkbox,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
} from '@mui/material';
import { StoreContext } from '../../../../store/StoreProvider';
import Act from '../../../../store/actions';
import { updateUser } from '../../../../Component/Authenticate/firebase/realtimeDatabase';
import { canManageUsers, getPermissionMessage, OPERATION } from '../../../../utils/permissionUtils';
import { isProduction } from '../../../../utils/environmentUtils';

function InvalidUser() {
  const header = { userName: '使用者名稱', email: '信箱', checkBox: '確認' };

  const [state, dispatch] = useContext(StoreContext);
  const { userInfo, roles } = state.authority;
  const currentUser = state.user;
  const hasManagePermission = canManageUsers(currentUser);

  const handleClick = (user) => {
    // 權限檢查
    if (!hasManagePermission) {
      // eslint-disable-next-line no-alert
      alert(getPermissionMessage(currentUser, OPERATION.MANAGE_USERS));
      return;
    }
    const tmpUserInfo = JSON.parse(JSON.stringify(userInfo));
    const findUser = tmpUserInfo.find((element) => element.uid === user.uid);
    if (findUser) {
      // 根據環境決定要讀寫哪個欄位
      const isProd = isProduction();
      const roleField = isProd ? 'role' : 'roleDev';
      
      // 讀取當前角色，與 UI 顯示邏輯保持一致
      // 測試站：如果 roleDev 為空，fallback 到 role
      const currentRoleString = isProd 
        ? (findUser.role || '') 
        : (findUser.roleDev || findUser.role || '');
      
      const tmpRole = currentRoleString.split(',');
      tmpRole.push('reader');
      const updatedRoleString = tmpRole.join();
      
      // 根據環境只更新對應的欄位
      // 正式站: 只更新 role
      // 測試站: 只更新 roleDev
      findUser[roleField] = updatedRoleString;
      
      // 如果是測試站且 roleDev 原本是空的，需要確保更新 roleDev
      if (!isProd && !findUser.roleDev) {
        findUser.roleDev = updatedRoleString;
      }
      
      dispatch({
        type: Act.SET_USERINFO,
        payload: tmpUserInfo,
      });

      // update firebase
      updateUser(findUser.uid, findUser);
    }
  };

  return (
    <>
      {/* 權限提示 */}
      {!hasManagePermission && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          <strong>唯讀模式：</strong>僅 Admin 可編輯使用者權限。您目前只能檢視權限設定。
        </Alert>
      )}
      <TableContainer component={Paper} className="ValidUser">
        <Table>
          <TableHead className="tableHead">
            <TableRow>
              {Object.keys(header).map((col, index) => (
                <TableCell key={index} align="center">
                  {header[col]}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {userInfo
              .filter((info) => {
                let exist = true;
                roles.forEach((role) => {
                  if (
                    info.role.indexOf(role.toLowerCase()) >= 0 ||
                    info.role.indexOf('developer') >= 0
                  ) {
                    exist = false;
                  }
                });
                return exist;
              })
              .map((user, index) => (
                <TableRow key={index}>
                  <TableCell align="center">{user.displayName}</TableCell>
                  <TableCell align="center">{user.email}</TableCell>
                  <TableCell align="center">
                    <Checkbox
                      onClick={() => handleClick(user)}
                      disabled={!hasManagePermission}
                      sx={{
                        '&.Mui-disabled': {
                          color: 'action.disabled',
                          opacity: 0.5,
                        },
                        '&.Mui-disabled.Mui-checked': {
                          color: 'action.disabled',
                        },
                      }}
                    />
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
}

export default InvalidUser;
