import React, { useState, useEffect, useContext } from "react";
import { Button, CircularProgress, Stack } from "@mui/material";
import { isEmpty } from "../../../../../utils";
import ExcelMultiSheet from "../../../../../Component/ExcelMultiSheet/ExcelMultiSheet";
import { getAllUpdatedLandID, getAllLandData } from "../../common";
import { landHeaders } from "../../common/landEditorConfig";
import { StoreContext } from "../../../../../store/StoreProvider";
import evtType from "../../../../../utils/evtType";

function DownLoadBtn({ startDate, endDate }) {
  const [state] = useContext(StoreContext);
  const { selectDateType } = state.download;
  const [loading, setLoading] = useState(false);
  const [excelData, setExcelData] = useState({});
  const [fileName, setFileName] = useState("");

  useEffect(() => {
    const tmpStartDate = startDate.toISOString().split("T")[0];
    const tmpEndDate = endDate.toISOString().split("T")[0];
    let tmpFileName = "";
    if (startDate === endDate) {
      tmpFileName = `Land Data Edited on ${tmpStartDate}`;
    } else {
      tmpFileName = `Land Data Edited from ${tmpStartDate} to ${tmpEndDate}`;
    }
    setFileName(tmpFileName);
    setExcelData({});
  }, [startDate, endDate, selectDateType]);

  const handleClick = async () => {
    setLoading(true);
    const allUpdateLandID = await getAllUpdatedLandID(startDate, endDate);
    let allLandData = [];
    const tmpExcelData = {};
    // headers
    const tmpHeaders = [];
    landHeaders.forEach((obj, index) => {
      tmpHeaders.push({ ...obj, seq: index });
    });
    let sheetNames = ["工作表"];
    if (allUpdateLandID.length > 0) {
      allLandData = await getAllLandData(
        allUpdateLandID.map((el) => el.landId)
      );

      // 找出所有user，當作sheetName
      sheetNames = allUpdateLandID
        .filter(
          (element, pos) =>
            allUpdateLandID.findIndex((item) => item.user === element.user) ===
            pos
        )
        .map((el) => el.user);
    }

    sheetNames.forEach((sheetName, index) => {
      const filterDataByUser = allUpdateLandID.filter(
        (obj) => obj.user === sheetName
      );
      const filterData = allLandData.filter((el) =>
        filterDataByUser.find(
          (obj) =>
            obj.landId ===
            el.Land.find((obj) => obj.predicate === "landName").landId
        )
      );
      let rowIndex = 0;

      // data
      const tmpData = [];
      filterData.forEach((data) => {
        const maxLandMarkNumber = Math.max(
          ...data.LandMark.filter(
            (el) => el.predicate === "landMarkNumber"
          ).map((el) => parseInt(el.value)),
          1
        );
        const maxLandRightsNumber = Math.max(
          ...data.LandRights.filter(
            (el) => el.predicate === "landRightsNumber"
          ).map((el) => parseInt(el.value)),
          1
        );
        let tmpLMNumber = 1;
        let tmpLRNumber = 1;
        for (
          let dataRowIndex = 0;
          dataRowIndex < Math.max(maxLandMarkNumber, maxLandRightsNumber);
          dataRowIndex++
        ) {
          const tmpObj = {};
          tmpHeaders.forEach((header) => {
            const landType = header.id.split("--")[0];
            const property = header.id.split("--")[1];
            if (dataRowIndex === 0) {
              // 找Land、LandMark、LandRight
              if (landType === "Land") {
                const findValue = data[landType].find(
                  (el) => el.predicate === property
                );
                tmpObj[header.id] = findValue ? findValue.value : "";
              }
            }
            if (landType === evtType.LandMark) {
              const findLMID = data[landType].find(
                (el) =>
                  el.predicate === "landMarkNumber" &&
                  parseInt(el.value) === tmpLMNumber
              );
              if (findLMID) {
                const LMID = findLMID.landMarkId;
                const findValue = data[landType].find(
                  (el) => el.predicate === property && el.landMarkId === LMID
                );
                if (findValue) {
                  tmpObj[header.id] = findValue ? findValue.value : "";
                }
              }
            } else if (landType === evtType.LandRights) {
              const findLRID = data[landType].find(
                (el) =>
                  el.predicate === "landRightsNumber" &&
                  parseInt(el.value) === tmpLRNumber
              );
              if (findLRID) {
                const LRID = findLRID.landRightsId;
                const findValue = data[landType].find(
                  (el) => el.predicate === property && el.landRightsId === LRID
                );
                if (findValue) {
                  tmpObj[header.id] = findValue ? findValue.value : "";
                }
              }
            }
          });
          tmpLMNumber++;
          tmpLRNumber++;
          rowIndex++;
          tmpData.push(tmpObj);
        }
      });
      tmpExcelData[sheetName] = {
        order: index,
        headers: tmpHeaders,
        data: tmpData,
      };
    });
    // console.log("tmpExcelData ", tmpExcelData);
    setExcelData(tmpExcelData);
    setLoading(false);
  };

  return (
    <Stack 
      className="DownLoadBtnBox"
      direction={{ xs: 'column', sm: 'row' }}
      spacing={2}
      alignItems={{ xs: 'stretch', sm: 'center' }}
      sx={{
        mt: 3,
        p: 2,
        backgroundColor: 'var(--color-surface-alt)',
        borderRadius: 2,
        border: '1px solid var(--color-border-light)',
      }}
    >
      <Button
        variant="contained"
        size="large"
        onClick={handleClick}
        color="primary"
        disabled={!isEmpty(excelData) || loading}
        className="DWButton"
        sx={{
          minWidth: 160,
          px: 4,
          py: 1.5,
          fontSize: '1rem',
          fontWeight: 600,
          borderRadius: 2,
          boxShadow: 2,
          textTransform: 'none',
          '&:hover': {
            boxShadow: 4,
            transform: 'translateY(-1px)',
          },
          '&:disabled': {
            backgroundColor: 'action.disabledBackground',
            color: 'action.disabled',
          },
          transition: 'all 0.2s ease',
        }}
      >
        {loading ? (
          <>
            <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
            產生中...
          </>
        ) : (
          "產生資料"
        )}
      </Button>
      {!isEmpty(excelData) && (
        <ExcelMultiSheet filename={fileName} excelData={excelData} />
      )}
    </Stack>
  );
}

export default DownLoadBtn;
