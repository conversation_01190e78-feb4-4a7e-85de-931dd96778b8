import React, { useContext, useEffect } from "react";
import queryString from "query-string";
import { useHistory, useLocation } from "react-router-dom";

// scss
import "./EditPage.scss";

// component
import ButtonArea from "./subComponents/ButtonArea";
import InfoArea from "./subComponents/infoArea";
import CustomLoading from "../../../Component/CustomLoading/CustomLoading";
import PermissionSnackbar from "../../../Component/PermissionSnackbar";
import usePermissionSnackbar from "../../../Component/PermissionSnackbar/usePermissionSnackbar";

// utils
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";
import { isEmpty } from "../../../utils";
import { Api, readOntoData, fetchPOSTData } from "../../../api/land/Api";
import { setLNAndLSN } from "./common";
import getLandID from "../../../utils/getLandID";
import hisAddSearch from "../../../utils/hisAddSearch";

function EditPage() {
  const history = useHistory();
  const location = useLocation();
  const [state, dispatch] = useContext(StoreContext);
  const { editLoading, landId, landName, landSerialNumber } = state.edit;
  const { displayName } = state.user;
  
  // 初始化 Snackbar Hook
  const { snackbarState, showError, hideSnackbar } = usePermissionSnackbar();

  useEffect(() => {
    // 進到編輯頁，先確認有無編輯資料
    let apiStr = Api.getEditedLandByUser(displayName);
    readOntoData(apiStr).then((result) => {
      if (!isEmpty(result.data)) {
        const tmpLandId = result.data[0]?.landId || "";
        // 有編輯中資料，URL紀錄landId
        hisAddSearch(history, { landId: tmpLandId });

        dispatch({
          type: Act.SET_LANDNAME,
          payload: result.data[0]?.landName || "",
        });

        dispatch({
          type: Act.SET_LANDSERIALNUMBER,
          payload: result.data[0]?.landSerialNumber || "",
        });
      } else {
        // 沒有編輯中資料，確認URL landId
        const { landId: urlLandId } = queryString.parse(location.search);
        if (urlLandId) {
          apiStr = Api.getPostLandData();
          fetchPOSTData({
            apiStr,
            entry: {
              ids: urlLandId,
            },
          }).then((res) => {
            // update landName and landSerialNumber
            setLNAndLSN(res.data, dispatch);
          });
        }
      }
    });
  }, []);

  useEffect(() => {
    const { landId: urlLandId } = queryString.parse(location.search);
    dispatch({
      type: Act.SET_LANDID,
      payload: urlLandId || "",
    });
  }, [location]);

  useEffect(() => {
    if (!isEmpty(landId) || !landName || !landSerialNumber) return;

    // url沒有Id，只能從landName、landSerialNumber取得
    (async () => {
      const tmpId = await getLandID(landName, landSerialNumber);
      if (!isEmpty(tmpId)) {
        // URL紀錄landId
        hisAddSearch(history, { landId: tmpId });
      }
    })();
  }, [landId, landName, landSerialNumber]);

  useEffect(
    () =>
      // 離開編輯頁要清除地號、地段、地號清單、編輯狀態
      () => {
        // clear cache
        dispatch({
          type: Act.SET_LANDID,
          payload: "",
        });
        dispatch({
          type: Act.SET_LANDNAME,
          payload: "",
        });
        dispatch({
          type: Act.SET_LANDSERIALNUMBER,
          payload: "",
        });
        dispatch({
          type: Act.SET_ISLOCK,
          payload: false,
        });
        dispatch({
          type: Act.SET_LSNLIST,
          payload: [],
        });
      },
    []
  );

  return (
    <div style={{ height: "100%" }}>
      {editLoading ? (
        <CustomLoading />
      ) : (
        <div className="EditPage">
          <ButtonArea showPermissionError={showError} />
          <InfoArea />
        </div>
      )}
      
      {/* 權限通知 Snackbar */}
      <PermissionSnackbar
        open={snackbarState.open}
        onClose={hideSnackbar}
        message={snackbarState.message}
        severity={snackbarState.severity}
      />
    </div>
  );
}

export default EditPage;
