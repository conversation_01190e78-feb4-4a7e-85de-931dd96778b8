<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE log SYSTEM "logger.dtd">
<log>
<record>
  <date>2025-08-19T04:42:36.123408500Z</date>
  <millis>1755578556123</millis>
  <nanos>408500</nanos>
  <sequence>0</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startSuite</method>
  <thread>1</thread>
  <message>Start Test Suite : Test Suites/Mandarin/v1/Gis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="rerunTestFailImmediately">false</property>
  <property name="retryCount">0</property>
  <property name="name">Gis</property>
  <property name="description"></property>
  <property name="id">Test Suites/Mandarin/v1/Gis</property>
</record>
<record>
  <date>2025-08-19T04:42:36.147938300Z</date>
  <millis>1755578556147</millis>
  <nanos>938300</nanos>
  <sequence>1</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>userFullName = Lin Kai Yee</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="userFullName">Lin Kai Yee</property>
</record>
<record>
  <date>2025-08-19T04:42:36.148936900Z</date>
  <millis>1755578556148</millis>
  <nanos>936900</nanos>
  <sequence>2</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>projectName = katalon</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="projectName">katalon</property>
</record>
<record>
  <date>2025-08-19T04:42:36.149936600Z</date>
  <millis>1755578556149</millis>
  <nanos>936600</nanos>
  <sequence>3</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>hostName = saw63 - ************</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="hostName">saw63 - ************</property>
</record>
<record>
  <date>2025-08-19T04:42:36.149936600Z</date>
  <millis>1755578556149</millis>
  <nanos>936600</nanos>
  <sequence>4</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>os = Windows 11 64bit</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="os">Windows 11 64bit</property>
</record>
<record>
  <date>2025-08-19T04:42:36.149936600Z</date>
  <millis>1755578556149</millis>
  <nanos>936600</nanos>
  <sequence>5</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>hostAddress = ************</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="hostAddress">************</property>
</record>
<record>
  <date>2025-08-19T04:42:36.150936700Z</date>
  <millis>1755578556150</millis>
  <nanos>936700</nanos>
  <sequence>6</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>katalonVersion = ********</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="katalonVersion">********</property>
</record>
<record>
  <date>2025-08-19T04:42:36.164934600Z</date>
  <millis>1755578556164</millis>
  <nanos>934600</nanos>
  <sequence>7</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start listener action : beforeSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:36.185936900Z</date>
  <millis>1755578556185</millis>
  <nanos>936900</nanos>
  <sequence>9</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T04:42:36.464084200Z</date>
  <millis>1755578556464</millis>
  <nanos>84200</nanos>
  <sequence>11</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Starting &amp;apos;Chrome&amp;apos; driver</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:36.556575500Z</date>
  <millis>1755578556556</millis>
  <nanos>575500</nanos>
  <sequence>12</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Action delay is set to 0 milliseconds</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:38.394178800Z</date>
  <millis>1755578558394</millis>
  <nanos>178800</nanos>
  <sequence>14</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>sessionId = 58b9db6878e23d023d7d9eac8e711437</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="sessionId">58b9db6878e23d023d7d9eac8e711437</property>
</record>
<record>
  <date>2025-08-19T04:42:38.395180100Z</date>
  <millis>1755578558395</millis>
  <nanos>180100</nanos>
  <sequence>15</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>browser = Chrome 138.0.7204.185</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="browser">Chrome 138.0.7204.185</property>
</record>
<record>
  <date>2025-08-19T04:42:38.396180500Z</date>
  <millis>1755578558396</millis>
  <nanos>180500</nanos>
  <sequence>16</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>platform = Windows 11</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="platform">Windows 11</property>
</record>
<record>
  <date>2025-08-19T04:42:38.396180500Z</date>
  <millis>1755578558396</millis>
  <nanos>180500</nanos>
  <sequence>17</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>seleniumVersion = 4.28.1</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="seleniumVersion">4.28.1</property>
</record>
<record>
  <date>2025-08-19T04:42:38.407178400Z</date>
  <millis>1755578558407</millis>
  <nanos>178400</nanos>
  <sequence>18</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&amp;quot;&amp;quot;, isApplyToDesiredCapabilities=true }</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="proxyInformation">ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }</property>
</record>
<record>
  <date>2025-08-19T04:42:38.451520400Z</date>
  <millis>1755578558451</millis>
  <nanos>520400</nanos>
  <sequence>19</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is opened with url: &amp;apos;&amp;apos;</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.OpenBrowserKeyword.openBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T04:42:38.453549300Z</date>
  <millis>1755578558453</millis>
  <nanos>549300</nanos>
  <sequence>20</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:38.454657700Z</date>
  <millis>1755578558454</millis>
  <nanos>657700</nanos>
  <sequence>21</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : maximizeWindow()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T04:42:38.520012900Z</date>
  <millis>1755578558520</millis>
  <nanos>12900</nanos>
  <sequence>23</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Current window maximized</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.MaximizeWindowKeyword.maximizeWindow</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T04:42:38.521027300Z</date>
  <millis>1755578558521</millis>
  <nanos>27300</nanos>
  <sequence>25</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : maximizeWindow()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:38.522023300Z</date>
  <millis>1755578558522</millis>
  <nanos>23300</nanos>
  <sequence>26</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End listener action : beforeSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:38.910485100Z</date>
  <millis>1755578558910</millis>
  <nanos>485100</nanos>
  <sequence>27</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/GoToGis</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/GoToGis</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\GoToGis.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-19T04:42:38.967121900Z</date>
  <millis>1755578558967</millis>
  <nanos>121900</nanos>
  <sequence>29</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">20</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T04:42:38.972087700Z</date>
  <millis>1755578558972</millis>
  <nanos>87700</nanos>
  <sequence>31</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>A browser is already opened. Closing browser and opening a new one</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:39.141746600Z</date>
  <millis>1755578559141</millis>
  <nanos>746600</nanos>
  <sequence>32</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Starting &amp;apos;Chrome&amp;apos; driver</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:39.144294Z</date>
  <millis>1755578559144</millis>
  <nanos>294000</nanos>
  <sequence>33</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Action delay is set to 0 milliseconds</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:40.087611800Z</date>
  <millis>1755578560087</millis>
  <nanos>611800</nanos>
  <sequence>35</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>sessionId = 499d3100755f74aa5cfb63b89bdb2b7b</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="sessionId">499d3100755f74aa5cfb63b89bdb2b7b</property>
</record>
<record>
  <date>2025-08-19T04:42:40.087611800Z</date>
  <millis>1755578560087</millis>
  <nanos>611800</nanos>
  <sequence>36</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>browser = Chrome 138.0.7204.185</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="browser">Chrome 138.0.7204.185</property>
</record>
<record>
  <date>2025-08-19T04:42:40.088617Z</date>
  <millis>1755578560088</millis>
  <nanos>617000</nanos>
  <sequence>37</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>platform = Windows 11</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="platform">Windows 11</property>
</record>
<record>
  <date>2025-08-19T04:42:40.088617Z</date>
  <millis>1755578560088</millis>
  <nanos>617000</nanos>
  <sequence>38</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>seleniumVersion = 4.28.1</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="seleniumVersion">4.28.1</property>
</record>
<record>
  <date>2025-08-19T04:42:40.089613100Z</date>
  <millis>1755578560089</millis>
  <nanos>613100</nanos>
  <sequence>39</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&amp;quot;&amp;quot;, isApplyToDesiredCapabilities=true }</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="proxyInformation">ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }</property>
</record>
<record>
  <date>2025-08-19T04:42:40.109215500Z</date>
  <millis>1755578560109</millis>
  <nanos>215500</nanos>
  <sequence>40</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is opened with url: &amp;apos;&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.OpenBrowserKeyword.openBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T04:42:40.111300200Z</date>
  <millis>1755578560111</millis>
  <nanos>300200</nanos>
  <sequence>41</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:40.111300200Z</date>
  <millis>1755578560111</millis>
  <nanos>300200</nanos>
  <sequence>42</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : navigateToUrl(&amp;quot;https://land2.daoyidh.com/Gis&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T04:42:40.412648500Z</date>
  <millis>1755578560412</millis>
  <nanos>648500</nanos>
  <sequence>45</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Navigate to &amp;apos;https://land2.daoyidh.com/Gis&amp;apos; successfully</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.NavigateToUrlKeyword.navigateToUrl</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T04:42:40.412648500Z</date>
  <millis>1755578560412</millis>
  <nanos>648500</nanos>
  <sequence>46</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : navigateToUrl(&amp;quot;https://land2.daoyidh.com/Gis&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:40.412648500Z</date>
  <millis>1755578560412</millis>
  <nanos>648500</nanos>
  <sequence>47</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:40.414160200Z</date>
  <millis>1755578560414</millis>
  <nanos>160200</nanos>
  <sequence>48</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-19T04:42:40.420682500Z</date>
  <millis>1755578560420</millis>
  <nanos>682500</nanos>
  <sequence>49</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</property>
  <property name="description">Login</property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\LoginWithEmail.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-19T04:42:40.497649100Z</date>
  <millis>1755578560497</millis>
  <nanos>649100</nanos>
  <sequence>51</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">20</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T04:42:41.870015600Z</date>
  <millis>1755578561870</millis>
  <nanos>15600</nanos>
  <sequence>61</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T04:42:41.872012700Z</date>
  <millis>1755578561872</millis>
  <nanos>12700</nanos>
  <sequence>62</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:41.873013400Z</date>
  <millis>1755578561873</millis>
  <nanos>13400</nanos>
  <sequence>63</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T04:42:42.377174800Z</date>
  <millis>1755578562377</millis>
  <nanos>174800</nanos>
  <sequence>73</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T04:42:42.380681700Z</date>
  <millis>1755578562380</millis>
  <nanos>681700</nanos>
  <sequence>74</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:42.387819900Z</date>
  <millis>1755578562387</millis>
  <nanos>819900</nanos>
  <sequence>75</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;quot;), &amp;quot;<EMAIL>&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">24</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-19T04:42:43.174442Z</date>
  <millis>1755578563174</millis>
  <nanos>442000</nanos>
  <sequence>91</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;<EMAIL>&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T04:42:43.177445200Z</date>
  <millis>1755578563177</millis>
  <nanos>445200</nanos>
  <sequence>92</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;quot;), &amp;quot;<EMAIL>&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:43.178442600Z</date>
  <millis>1755578563178</millis>
  <nanos>442600</nanos>
  <sequence>93</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-19T04:42:43.480977500Z</date>
  <millis>1755578563480</millis>
  <nanos>977500</nanos>
  <sequence>103</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T04:42:43.487979Z</date>
  <millis>1755578563487</millis>
  <nanos>979000</nanos>
  <sequence>104</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:43.487979Z</date>
  <millis>1755578563487</millis>
  <nanos>979000</nanos>
  <sequence>105</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setEncryptedText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;quot;), &amp;quot;0D41p6Ct1VJ35FCiyaw3Gw==&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">28</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-19T04:42:44.441506400Z</date>
  <millis>1755578564441</millis>
  <nanos>506400</nanos>
  <sequence>116</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text ****** has been set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetEncryptedTextKeyword.setEncryptedText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T04:42:44.443505700Z</date>
  <millis>1755578564443</millis>
  <nanos>505700</nanos>
  <sequence>117</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setEncryptedText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;quot;), &amp;quot;0D41p6Ct1VJ35FCiyaw3Gw==&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:44.444507700Z</date>
  <millis>1755578564444</millis>
  <nanos>507700</nanos>
  <sequence>118</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-19T04:42:44.734595200Z</date>
  <millis>1755578564734</millis>
  <nanos>595200</nanos>
  <sequence>128</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T04:42:44.735604300Z</date>
  <millis>1755578564735</millis>
  <nanos>604300</nanos>
  <sequence>129</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:44.736594900Z</date>
  <millis>1755578564736</millis>
  <nanos>594900</nanos>
  <sequence>130</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:44.737592600Z</date>
  <millis>1755578564737</millis>
  <nanos>592600</nanos>
  <sequence>131</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-19T04:42:44.742040Z</date>
  <millis>1755578564742</millis>
  <nanos>40000</nanos>
  <sequence>132</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\SearchTextInGis.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-19T04:42:44.788070600Z</date>
  <millis>1755578564788</millis>
  <nanos>70600</nanos>
  <sequence>134</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button__1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T04:42:47.291459500Z</date>
  <millis>1755578567291</millis>
  <nanos>459500</nanos>
  <sequence>145</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button__1&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T04:42:47.293463200Z</date>
  <millis>1755578567293</millis>
  <nanos>463200</nanos>
  <sequence>146</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button__1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:47.293463200Z</date>
  <millis>1755578567293</millis>
  <nanos>463200</nanos>
  <sequence>147</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;quot;), &amp;quot;新北勢&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">25</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T04:42:48.167603Z</date>
  <millis>1755578568167</millis>
  <nanos>603000</nanos>
  <sequence>159</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;新北勢&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T04:42:48.168602200Z</date>
  <millis>1755578568168</millis>
  <nanos>602200</nanos>
  <sequence>160</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;quot;), &amp;quot;新北勢&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:48.168602200Z</date>
  <millis>1755578568168</millis>
  <nanos>602200</nanos>
  <sequence>161</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:48.169603900Z</date>
  <millis>1755578568169</millis>
  <nanos>603900</nanos>
  <sequence>162</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-19T04:42:48.174603300Z</date>
  <millis>1755578568174</millis>
  <nanos>603300</nanos>
  <sequence>163</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\ChoosePerPage.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-19T04:42:48.248603700Z</date>
  <millis>1755578568248</millis>
  <nanos>603700</nanos>
  <sequence>165</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">24</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T04:42:48.256601500Z</date>
  <millis>1755578568256</millis>
  <nanos>601500</nanos>
  <sequence>166</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:48.256601500Z</date>
  <millis>1755578568256</millis>
  <nanos>601500</nanos>
  <sequence>167</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;div[role=&amp;quot;combobox&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">25</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T04:42:48.262604400Z</date>
  <millis>1755578568262</millis>
  <nanos>604400</nanos>
  <sequence>168</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;div[role=&amp;quot;combobox&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:42:48.264104300Z</date>
  <millis>1755578568264</millis>
  <nanos>104300</nanos>
  <sequence>169</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(dropdownButton)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-19T04:43:19.280268700Z</date>
  <millis>1755578599280</millis>
  <nanos>268700</nanos>
  <sequence>304</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Unable to find the element located by &amp;apos;By.cssSelector: div[role=&amp;quot;combobox&amp;quot;]&amp;apos;. Please recheck the objects properties to make sure the desired element is located. </message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:43:19.281268400Z</date>
  <millis>1755578599281</millis>
  <nanos>268400</nanos>
  <sequence>305</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Failed to find element with id &amp;apos;&amp;apos;. Try using Self-healing.</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:43:19.296529900Z</date>
  <millis>1755578599296</millis>
  <nanos>529900</nanos>
  <sequence>307</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Index 0 out of bounds for length 0</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IndexOutOfBoundsException</property>
  <property name="failed.exception.stacktrace">java.lang.IndexOutOfBoundsException: Index 0 out of bounds for length 0
	at java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:64)
	at java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:70)
	at java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:266)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:531)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:955)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:843)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:66)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at ChoosePerPage.run(ChoosePerPage:26)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755578552784.run(TempTestSuite1755578552784.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Index 0 out of bounds for length 0</property>
</record>
<record>
  <date>2025-08-19T04:43:19.297530600Z</date>
  <millis>1755578599297</millis>
  <nanos>530600</nanos>
  <sequence>309</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Cannot find elements when the selector is null</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IllegalArgumentException</property>
  <property name="failed.exception.stacktrace">java.lang.IllegalArgumentException: Cannot find elements when the selector is null
	at org.openqa.selenium.internal.Require$ArgumentChecker.nonNull(Require.java:211)
	at org.openqa.selenium.By$ByCssSelector.&lt;init&gt;(By.java:328)
	at org.openqa.selenium.By.cssSelector(By.java:112)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:527)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:951)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:846)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:66)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at ChoosePerPage.run(ChoosePerPage:26)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755578552784.run(TempTestSuite1755578552784.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Cannot find elements when the selector is null</property>
</record>
<record>
  <date>2025-08-19T04:43:19.402065700Z</date>
  <millis>1755578599402</millis>
  <nanos>65700</nanos>
  <sequence>313</sequence>
  <level>FAILED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Unable to click on object &amp;apos;&amp;apos; (Root cause: com.kms.katalon.core.exception.StepFailedException: Unable to click on object &amp;apos;&amp;apos;
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at ChoosePerPage.run(ChoosePerPage:26)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755578552784.run(TempTestSuite1755578552784.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &amp;apos;&amp;apos; located by &amp;apos;div[role=&amp;quot;combobox&amp;quot;]&amp;apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:66)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at Script1755487429981.run(Script1755487429981.groovy:26)
	... 15 more
)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">com.kms.katalon.core.webui.exception.WebElementNotFoundException</property>
  <property name="attachment">1755578599304.png</property>
  <property name="failed.exception.stacktrace">com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:66)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at ChoosePerPage.run(ChoosePerPage:26)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755578552784.run(TempTestSuite1755578552784.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found</property>
</record>
<record>
  <date>2025-08-19T04:43:19.403065500Z</date>
  <millis>1755578599403</millis>
  <nanos>65500</nanos>
  <sequence>316</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(dropdownButton)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T04:43:19.406064700Z</date>
  <millis>1755578599406</millis>
  <nanos>64700</nanos>
  <sequence>319</sequence>
  <level>FAILED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/ChoosePerPage FAILED.
Reason:
com.kms.katalon.core.exception.StepFailedException: Unable to click on object &amp;apos;&amp;apos;
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at ChoosePerPage.run(ChoosePerPage:26)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755578552784.run(TempTestSuite1755578552784.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &amp;apos;&amp;apos; located by &amp;apos;div[role=&amp;quot;combobox&amp;quot;]&amp;apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:66)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	... 20 more
</message>
  <exception>
    <message>com.kms.katalon.core.exception.StepFailedException: Unable to click on object ''</message>
    <frame>
      <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
      <method>logMessage</method>
      <line>500</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.logging.KeywordLogger</class>
      <method>logMessage</method>
      <line>439</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>logError</method>
      <line>593</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>onExecutionError</method>
      <line>187</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>processExecutionPhase</method>
      <line>465</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>accessMainPhase</method>
      <line>447</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>execute</method>
      <line>321</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.common.CommonExecutor</class>
      <method>accessTestCaseMainPhase</method>
      <line>71</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestSuiteExecutor</class>
      <method>accessTestSuiteMainPhase</method>
      <line>160</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestSuiteExecutor</class>
      <method>execute</method>
      <line>107</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseMain</class>
      <method>startTestSuite</method>
      <line>195</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.vmplugin.v8.IndyInterface</class>
      <method>fromCache</method>
      <line>318</line>
    </frame>
    <frame>
      <class>TempTestSuite1755578552784</class>
      <method>run</method>
      <line>35</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>runScriptOrMainOrTestOrRunnable</method>
      <line>254</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>run</method>
      <line>360</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>run</method>
      <line>349</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>processOnce</method>
      <line>652</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>run</method>
      <line>398</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>access$1400</method>
      <line>68</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain$GroovyCommand</class>
      <method>process</method>
      <line>322</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>processArgs</method>
      <line>142</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>main</method>
      <line>115</line>
    </frame>
    <frame>
      <class>jdk.internal.reflect.NativeMethodAccessorImpl</class>
      <method>invoke0</method>
    </frame>
    <frame>
      <class>jdk.internal.reflect.NativeMethodAccessorImpl</class>
      <method>invoke</method>
      <line>77</line>
    </frame>
    <frame>
      <class>jdk.internal.reflect.DelegatingMethodAccessorImpl</class>
      <method>invoke</method>
      <line>43</line>
    </frame>
    <frame>
      <class>java.lang.reflect.Method</class>
      <method>invoke</method>
      <line>569</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.tools.GroovyStarter</class>
      <method>rootLoader</method>
      <line>117</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.tools.GroovyStarter</class>
      <method>main</method>
      <line>39</line>
    </frame>
  </exception>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.stacktrace">com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:66)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at ChoosePerPage.run(ChoosePerPage:26)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755578552784.run(TempTestSuite1755578552784.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.class">com.kms.katalon.core.webui.exception.WebElementNotFoundException</property>
  <property name="failed.exception.message">Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found</property>
</record>
<record>
  <date>2025-08-19T04:43:19.408063800Z</date>
  <millis>1755578599408</millis>
  <nanos>63800</nanos>
  <sequence>321</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-19T04:43:19.412064900Z</date>
  <millis>1755578599412</millis>
  <nanos>64900</nanos>
  <sequence>322</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/EditCoordinates</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/EditCoordinates</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/EditCoordinates</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\EditCoordinates.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-19T04:43:19.465824100Z</date>
  <millis>1755578599465</millis>
  <nanos>824100</nanos>
  <sequence>324</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">21</property>
  <property name="stepIndex">1</property>
</record>
