import { isEmpty } from "../../../../../utils";
import { checkRes } from "../../config";

/**
 * 檢查表頭順序是否正確
 * @param {Array} headerArr - Excel 第一列的表頭陣列
 * @param {Array} fbHeader - Firebase 的標準表頭配置
 * @returns {Object} { status, errors } - 驗證狀態和詳細錯誤列表
 */
const checkHeaderOrder = (headerArr, fbHeader) => {
  if (isEmpty(headerArr)) {
    return { 
      status: checkRes.failed, 
      errors: [{ 
        type: 'header_order',
        message: '表頭為空，請確認第一列有填寫欄位 ID',
        expected: fbHeader.map(h => h.id).join(', '),
        actual: '空白'
      }]
    };
  }

  const errors = [];
  // Excel 的 headerRow.values 第一個元素是 undefined，需要從索引 1 開始
  const filteredHeaderArr = headerArr.slice(1).filter(el => el); // 移除第一個空值和其他空值

  // 檢查每個欄位的順序
  filteredHeaderArr.forEach((elStr, idx) => {
    const expectedHeader = fbHeader[idx]?.id;
    if (elStr !== expectedHeader) {
      errors.push({
        type: 'header_order',
        columnIndex: idx + 1,
        expected: expectedHeader || '(無)',
        actual: elStr,
        message: `第 ${idx + 1} 欄：預期為「${expectedHeader || '無此欄位'}」，實際為「${elStr}」`
      });
    }
  });

  // 檢查欄位數量
  if (filteredHeaderArr.length < fbHeader.length) {
    errors.push({
      type: 'header_order',
      message: `欄位數量不足：預期 ${fbHeader.length} 個欄位，實際只有 ${filteredHeaderArr.length} 個`,
      missing: fbHeader.slice(filteredHeaderArr.length).map(h => h.label || h.id)
    });
  } else if (filteredHeaderArr.length > fbHeader.length) {
    errors.push({
      type: 'header_order',
      message: `欄位數量過多：預期 ${fbHeader.length} 個欄位，實際有 ${filteredHeaderArr.length} 個`,
      extra: filteredHeaderArr.slice(fbHeader.length)
    });
  }

  return errors.length > 0 
    ? { status: checkRes.failed, errors }
    : { status: checkRes.success, errors: [] };
};

export default checkHeaderOrder;
