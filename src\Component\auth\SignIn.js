import React, { useContext } from "react";

// firebase
import { getAuth } from "firebase/auth";
import StyledFirebaseAuth from "react-firebaseui/StyledFirebaseAuth";

// config
import firebaseUiConfig from "../../api/config/config-firebase-ui";
import { StoreContext } from "../../store/StoreProvider";
import { isEmpty, getFormatUser } from "../../utils";
import Act from "../../store/actions";
import { getUser, updateUser } from "../Authenticate/firebase/realtimeDatabase";
import role from "../../config/App-role";

const SignIn = () => {
  // eslint-disable-next-line no-unused-vars
  const [state, dispatch] = useContext(StoreContext);
  const { user } = state;

  firebaseUiConfig.callbacks = {
    signInSuccessWithAuthResult: async (authResult) => {
      const userInfo = getFormatUser(authResult.user);
      const { uid, displayName, email } = userInfo;
      if (uid && (displayName || email)) {
        const curUserData = await getUser(uid);
        const newUserData = { ...curUserData };
        // 第一次註冊的使用者，role角色是anonymous，登入成功就先給"reader"這個角色權限
        if (curUserData.role === role.anonymous) {
          newUserData.role = role.reader;
          updateUser(uid, newUserData);
        }
        dispatch({
          type: Act.FIREBASE_LOGIN_USER,
          payload: newUserData,
        });
      }
      return false;
    },
  };

  //  center style
  const textAlign = { textAlign: "center" };
  return (
    <>
      <h2 style={textAlign}>Welcome to 土地台帳建檔輔助系統</h2>
      {isEmpty(user) ? (
        <StyledFirebaseAuth
          uiConfig={firebaseUiConfig}
          firebaseAuth={getAuth()}
        />
      ) : (
        <h3 style={textAlign}>{user.displayName} 已登入</h3>
      )}
    </>
  );
};

export default SignIn;
