<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE log SYSTEM "logger.dtd">
<log>
<record>
  <date>2025-08-20T01:46:14.447828300Z</date>
  <millis>1755654374447</millis>
  <nanos>828300</nanos>
  <sequence>0</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startSuite</method>
  <thread>1</thread>
  <message>Start Test Suite : Test Suites/Mandarin/v1/Gis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="rerunTestFailImmediately">false</property>
  <property name="retryCount">0</property>
  <property name="name">Gis</property>
  <property name="description"></property>
  <property name="id">Test Suites/Mandarin/v1/Gis</property>
</record>
<record>
  <date>2025-08-20T01:46:14.480011500Z</date>
  <millis>1755654374480</millis>
  <nanos>11500</nanos>
  <sequence>1</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>userFullName = Lin Kai Yee</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="userFullName">Lin Kai Yee</property>
</record>
<record>
  <date>2025-08-20T01:46:14.481025400Z</date>
  <millis>1755654374481</millis>
  <nanos>25400</nanos>
  <sequence>2</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>projectName = katalon</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="projectName">katalon</property>
</record>
<record>
  <date>2025-08-20T01:46:14.482025100Z</date>
  <millis>1755654374482</millis>
  <nanos>25100</nanos>
  <sequence>3</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>hostName = saw63 - host.docker.internal</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="hostName">saw63 - host.docker.internal</property>
</record>
<record>
  <date>2025-08-20T01:46:14.483023400Z</date>
  <millis>1755654374483</millis>
  <nanos>23400</nanos>
  <sequence>4</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>os = Windows 11 64bit</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="os">Windows 11 64bit</property>
</record>
<record>
  <date>2025-08-20T01:46:14.484022900Z</date>
  <millis>1755654374484</millis>
  <nanos>22900</nanos>
  <sequence>5</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>hostAddress = ************</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="hostAddress">************</property>
</record>
<record>
  <date>2025-08-20T01:46:14.485075500Z</date>
  <millis>1755654374485</millis>
  <nanos>75500</nanos>
  <sequence>6</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>katalonVersion = ********</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="katalonVersion">********</property>
</record>
<record>
  <date>2025-08-20T01:46:14.498024400Z</date>
  <millis>1755654374498</millis>
  <nanos>24400</nanos>
  <sequence>7</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start listener action : beforeSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:14.527022500Z</date>
  <millis>1755654374527</millis>
  <nanos>22500</nanos>
  <sequence>9</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:46:14.826080300Z</date>
  <millis>1755654374826</millis>
  <nanos>80300</nanos>
  <sequence>11</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Starting &amp;apos;Chrome&amp;apos; driver</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:14.923239800Z</date>
  <millis>1755654374923</millis>
  <nanos>239800</nanos>
  <sequence>12</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Action delay is set to 0 milliseconds</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:16.745165300Z</date>
  <millis>1755654376745</millis>
  <nanos>165300</nanos>
  <sequence>14</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>sessionId = 4da81be517a6c043ed67c080ea02a56a</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="sessionId">4da81be517a6c043ed67c080ea02a56a</property>
</record>
<record>
  <date>2025-08-20T01:46:16.746684400Z</date>
  <millis>1755654376746</millis>
  <nanos>684400</nanos>
  <sequence>15</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>browser = Chrome 138.0.7204.185</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="browser">Chrome 138.0.7204.185</property>
</record>
<record>
  <date>2025-08-20T01:46:16.747192200Z</date>
  <millis>1755654376747</millis>
  <nanos>192200</nanos>
  <sequence>16</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>platform = Windows 11</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="platform">Windows 11</property>
</record>
<record>
  <date>2025-08-20T01:46:16.747192200Z</date>
  <millis>1755654376747</millis>
  <nanos>192200</nanos>
  <sequence>17</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>seleniumVersion = 4.28.1</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="seleniumVersion">4.28.1</property>
</record>
<record>
  <date>2025-08-20T01:46:16.759321100Z</date>
  <millis>1755654376759</millis>
  <nanos>321100</nanos>
  <sequence>18</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&amp;quot;&amp;quot;, isApplyToDesiredCapabilities=true }</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="proxyInformation">ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }</property>
</record>
<record>
  <date>2025-08-20T01:46:16.810830100Z</date>
  <millis>1755654376810</millis>
  <nanos>830100</nanos>
  <sequence>19</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is opened with url: &amp;apos;&amp;apos;</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.OpenBrowserKeyword.openBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:16.813882200Z</date>
  <millis>1755654376813</millis>
  <nanos>882200</nanos>
  <sequence>20</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:16.814392100Z</date>
  <millis>1755654376814</millis>
  <nanos>392100</nanos>
  <sequence>21</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : maximizeWindow()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:46:16.909010400Z</date>
  <millis>1755654376909</millis>
  <nanos>10400</nanos>
  <sequence>23</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Current window maximized</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.MaximizeWindowKeyword.maximizeWindow</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:16.910022900Z</date>
  <millis>1755654376910</millis>
  <nanos>22900</nanos>
  <sequence>25</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : maximizeWindow()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:16.911028400Z</date>
  <millis>1755654376911</millis>
  <nanos>28400</nanos>
  <sequence>26</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End listener action : beforeSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:17.343602500Z</date>
  <millis>1755654377343</millis>
  <nanos>602500</nanos>
  <sequence>27</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/GoToGis</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/GoToGis</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\GoToGis.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T01:46:17.402424100Z</date>
  <millis>1755654377402</millis>
  <nanos>424100</nanos>
  <sequence>29</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">20</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:46:17.407426300Z</date>
  <millis>1755654377407</millis>
  <nanos>426300</nanos>
  <sequence>31</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>A browser is already opened. Closing browser and opening a new one</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:17.587507800Z</date>
  <millis>1755654377587</millis>
  <nanos>507800</nanos>
  <sequence>32</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Starting &amp;apos;Chrome&amp;apos; driver</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:17.591258100Z</date>
  <millis>1755654377591</millis>
  <nanos>258100</nanos>
  <sequence>33</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Action delay is set to 0 milliseconds</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:18.477277500Z</date>
  <millis>1755654378477</millis>
  <nanos>277500</nanos>
  <sequence>35</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>sessionId = 3e561d059535bb105d19f83287d1f35b</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="sessionId">3e561d059535bb105d19f83287d1f35b</property>
</record>
<record>
  <date>2025-08-20T01:46:18.478288100Z</date>
  <millis>1755654378478</millis>
  <nanos>288100</nanos>
  <sequence>36</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>browser = Chrome 138.0.7204.185</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="browser">Chrome 138.0.7204.185</property>
</record>
<record>
  <date>2025-08-20T01:46:18.479289500Z</date>
  <millis>1755654378479</millis>
  <nanos>289500</nanos>
  <sequence>37</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>platform = Windows 11</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="platform">Windows 11</property>
</record>
<record>
  <date>2025-08-20T01:46:18.479289500Z</date>
  <millis>1755654378479</millis>
  <nanos>289500</nanos>
  <sequence>38</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>seleniumVersion = 4.28.1</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="seleniumVersion">4.28.1</property>
</record>
<record>
  <date>2025-08-20T01:46:18.480287500Z</date>
  <millis>1755654378480</millis>
  <nanos>287500</nanos>
  <sequence>39</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&amp;quot;&amp;quot;, isApplyToDesiredCapabilities=true }</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="proxyInformation">ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }</property>
</record>
<record>
  <date>2025-08-20T01:46:18.504144800Z</date>
  <millis>1755654378504</millis>
  <nanos>144800</nanos>
  <sequence>40</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is opened with url: &amp;apos;&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.OpenBrowserKeyword.openBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:18.505204200Z</date>
  <millis>1755654378505</millis>
  <nanos>204200</nanos>
  <sequence>41</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:18.505204200Z</date>
  <millis>1755654378505</millis>
  <nanos>204200</nanos>
  <sequence>42</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : navigateToUrl(&amp;quot;https://land2.daoyidh.com/Gis&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:46:18.965438900Z</date>
  <millis>1755654378965</millis>
  <nanos>438900</nanos>
  <sequence>45</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Navigate to &amp;apos;https://land2.daoyidh.com/Gis&amp;apos; successfully</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.NavigateToUrlKeyword.navigateToUrl</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:18.966427Z</date>
  <millis>1755654378966</millis>
  <nanos>427000</nanos>
  <sequence>46</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : navigateToUrl(&amp;quot;https://land2.daoyidh.com/Gis&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:18.966427Z</date>
  <millis>1755654378966</millis>
  <nanos>427000</nanos>
  <sequence>47</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:18.967451800Z</date>
  <millis>1755654378967</millis>
  <nanos>451800</nanos>
  <sequence>48</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T01:46:18.975426Z</date>
  <millis>1755654378975</millis>
  <nanos>426000</nanos>
  <sequence>49</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</property>
  <property name="description">Login</property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\LoginWithEmail.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T01:46:19.039398Z</date>
  <millis>1755654379039</millis>
  <nanos>398000</nanos>
  <sequence>51</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">20</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:46:20.174900200Z</date>
  <millis>1755654380174</millis>
  <nanos>900200</nanos>
  <sequence>61</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:20.175903300Z</date>
  <millis>1755654380175</millis>
  <nanos>903300</nanos>
  <sequence>62</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:20.176900700Z</date>
  <millis>1755654380176</millis>
  <nanos>900700</nanos>
  <sequence>63</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:46:20.684437Z</date>
  <millis>1755654380684</millis>
  <nanos>437000</nanos>
  <sequence>73</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:20.687056200Z</date>
  <millis>1755654380687</millis>
  <nanos>56200</nanos>
  <sequence>74</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:20.688065900Z</date>
  <millis>1755654380688</millis>
  <nanos>65900</nanos>
  <sequence>75</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;quot;), &amp;quot;<EMAIL>&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">24</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-20T01:46:21.500795100Z</date>
  <millis>1755654381500</millis>
  <nanos>795100</nanos>
  <sequence>91</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;<EMAIL>&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:21.502796400Z</date>
  <millis>1755654381502</millis>
  <nanos>796400</nanos>
  <sequence>92</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;quot;), &amp;quot;<EMAIL>&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:21.502796400Z</date>
  <millis>1755654381502</millis>
  <nanos>796400</nanos>
  <sequence>93</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-20T01:46:21.792147600Z</date>
  <millis>1755654381792</millis>
  <nanos>147600</nanos>
  <sequence>103</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:21.794148400Z</date>
  <millis>1755654381794</millis>
  <nanos>148400</nanos>
  <sequence>104</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:21.794148400Z</date>
  <millis>1755654381794</millis>
  <nanos>148400</nanos>
  <sequence>105</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setEncryptedText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;quot;), &amp;quot;0D41p6Ct1VJ35FCiyaw3Gw==&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">28</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-20T01:46:22.708176Z</date>
  <millis>1755654382708</millis>
  <nanos>176000</nanos>
  <sequence>116</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text ****** has been set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetEncryptedTextKeyword.setEncryptedText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:22.710177Z</date>
  <millis>1755654382710</millis>
  <nanos>177000</nanos>
  <sequence>117</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setEncryptedText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;quot;), &amp;quot;0D41p6Ct1VJ35FCiyaw3Gw==&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:22.710177Z</date>
  <millis>1755654382710</millis>
  <nanos>177000</nanos>
  <sequence>118</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-20T01:46:23.001238100Z</date>
  <millis>1755654383001</millis>
  <nanos>238100</nanos>
  <sequence>128</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:23.001238100Z</date>
  <millis>1755654383001</millis>
  <nanos>238100</nanos>
  <sequence>129</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:23.001238100Z</date>
  <millis>1755654383001</millis>
  <nanos>238100</nanos>
  <sequence>130</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:23.003238600Z</date>
  <millis>1755654383003</millis>
  <nanos>238600</nanos>
  <sequence>131</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T01:46:23.005238200Z</date>
  <millis>1755654383005</millis>
  <nanos>238200</nanos>
  <sequence>132</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\SearchTextInGis.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T01:46:23.071235Z</date>
  <millis>1755654383071</millis>
  <nanos>235000</nanos>
  <sequence>134</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button__1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:46:25.604514800Z</date>
  <millis>1755654385604</millis>
  <nanos>514800</nanos>
  <sequence>144</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button__1&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:25.605514Z</date>
  <millis>1755654385605</millis>
  <nanos>514000</nanos>
  <sequence>145</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button__1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:25.606513200Z</date>
  <millis>1755654385606</millis>
  <nanos>513200</nanos>
  <sequence>146</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;quot;), &amp;quot;新北勢&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">25</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:46:26.142002900Z</date>
  <millis>1755654386142</millis>
  <nanos>2900</nanos>
  <sequence>158</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;新北勢&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:26.142002900Z</date>
  <millis>1755654386142</millis>
  <nanos>2900</nanos>
  <sequence>159</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;quot;), &amp;quot;新北勢&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:26.143000600Z</date>
  <millis>1755654386143</millis>
  <nanos>600</nanos>
  <sequence>160</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:26.145006300Z</date>
  <millis>1755654386145</millis>
  <nanos>6300</nanos>
  <sequence>161</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T01:46:26.148998400Z</date>
  <millis>1755654386148</millis>
  <nanos>998400</nanos>
  <sequence>162</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\ChoosePerPage.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T01:46:26.205149Z</date>
  <millis>1755654386205</millis>
  <nanos>149000</nanos>
  <sequence>164</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">24</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:46:26.211106900Z</date>
  <millis>1755654386211</millis>
  <nanos>106900</nanos>
  <sequence>165</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:26.211106900Z</date>
  <millis>1755654386211</millis>
  <nanos>106900</nanos>
  <sequence>166</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;div[role=&amp;quot;button&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">25</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:46:26.217150700Z</date>
  <millis>1755654386217</millis>
  <nanos>150700</nanos>
  <sequence>167</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;div[role=&amp;quot;button&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:26.218145800Z</date>
  <millis>1755654386218</millis>
  <nanos>145800</nanos>
  <sequence>168</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(dropdownButton)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-20T01:46:26.623835Z</date>
  <millis>1755654386623</millis>
  <nanos>835000</nanos>
  <sequence>177</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:26.626836600Z</date>
  <millis>1755654386626</millis>
  <nanos>836600</nanos>
  <sequence>178</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(dropdownButton)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:26.627837500Z</date>
  <millis>1755654386627</millis>
  <nanos>837500</nanos>
  <sequence>179</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : option50 = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-20T01:46:26.632836800Z</date>
  <millis>1755654386632</millis>
  <nanos>836800</nanos>
  <sequence>180</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : option50 = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:26.632836800Z</date>
  <millis>1755654386632</millis>
  <nanos>836800</nanos>
  <sequence>181</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : option50.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;li.MuiMenuItem-root[data-value=&amp;quot;50&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-20T01:46:26.634836200Z</date>
  <millis>1755654386634</millis>
  <nanos>836200</nanos>
  <sequence>182</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : option50.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;li.MuiMenuItem-root[data-value=&amp;quot;50&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:26.634836200Z</date>
  <millis>1755654386634</millis>
  <nanos>836200</nanos>
  <sequence>183</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : waitForElementVisible(option50, 5)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">31</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-20T01:46:27.084805900Z</date>
  <millis>1755654387084</millis>
  <nanos>805900</nanos>
  <sequence>192</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object &amp;apos;&amp;apos; is visible</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call</property>
  <property name="testops-execution-stacktrace">com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:324)
com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:319)
com.kms.katalon.core.logging.KeywordLogger$logPassed$0.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:84)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
ChoosePerPage.run(ChoosePerPage:31)
</property>
</record>
<record>
  <date>2025-08-20T01:46:27.085805500Z</date>
  <millis>1755654387085</millis>
  <nanos>805500</nanos>
  <sequence>193</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : waitForElementVisible(option50, 5)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:27.085805500Z</date>
  <millis>1755654387085</millis>
  <nanos>805500</nanos>
  <sequence>194</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(option50)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">32</property>
  <property name="stepIndex">7</property>
</record>
<record>
  <date>2025-08-20T01:46:27.498395800Z</date>
  <millis>1755654387498</millis>
  <nanos>395800</nanos>
  <sequence>203</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:27.499398300Z</date>
  <millis>1755654387499</millis>
  <nanos>398300</nanos>
  <sequence>204</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(option50)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:27.499398300Z</date>
  <millis>1755654387499</millis>
  <nanos>398300</nanos>
  <sequence>205</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:27.501391100Z</date>
  <millis>1755654387501</millis>
  <nanos>391100</nanos>
  <sequence>206</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T01:46:27.505388800Z</date>
  <millis>1755654387505</millis>
  <nanos>388800</nanos>
  <sequence>207</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/EditCoordinates</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/EditCoordinates</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/EditCoordinates</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\EditCoordinates.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T01:46:27.574382300Z</date>
  <millis>1755654387574</millis>
  <nanos>382300</nanos>
  <sequence>209</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">21</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:46:28.073382800Z</date>
  <millis>1755654388073</millis>
  <nanos>382800</nanos>
  <sequence>221</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;148485&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:28.075385400Z</date>
  <millis>1755654388075</millis>
  <nanos>385400</nanos>
  <sequence>222</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:28.075385400Z</date>
  <millis>1755654388075</millis>
  <nanos>385400</nanos>
  <sequence>223</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:46:28.620080600Z</date>
  <millis>1755654388620</millis>
  <nanos>80600</nanos>
  <sequence>235</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;2406567 &amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:28.622085Z</date>
  <millis>1755654388622</millis>
  <nanos>85000</nanos>
  <sequence>236</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:28.622085Z</date>
  <millis>1755654388622</millis>
  <nanos>85000</nanos>
  <sequence>237</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">23</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-20T01:46:29.022335900Z</date>
  <millis>1755654389022</millis>
  <nanos>335900</nanos>
  <sequence>249</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;148485&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:29.023314600Z</date>
  <millis>1755654389023</millis>
  <nanos>314600</nanos>
  <sequence>250</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:29.023314600Z</date>
  <millis>1755654389023</millis>
  <nanos>314600</nanos>
  <sequence>251</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">24</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-20T01:46:29.469285100Z</date>
  <millis>1755654389469</millis>
  <nanos>285100</nanos>
  <sequence>263</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;2406567 &amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:29.470289300Z</date>
  <millis>1755654389470</millis>
  <nanos>289300</nanos>
  <sequence>264</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:29.471288300Z</date>
  <millis>1755654389471</millis>
  <nanos>288300</nanos>
  <sequence>265</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-20T01:46:30.139612100Z</date>
  <millis>1755654390139</millis>
  <nanos>612100</nanos>
  <sequence>275</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/pagination&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:30.140614Z</date>
  <millis>1755654390140</millis>
  <nanos>614000</nanos>
  <sequence>276</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:30.141615500Z</date>
  <millis>1755654390141</millis>
  <nanos>615500</nanos>
  <sequence>277</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">28</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-20T01:46:30.619594100Z</date>
  <millis>1755654390619</millis>
  <nanos>594100</nanos>
  <sequence>289</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;148485&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:30.620618Z</date>
  <millis>1755654390620</millis>
  <nanos>618000</nanos>
  <sequence>290</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:30.621594100Z</date>
  <millis>1755654390621</millis>
  <nanos>594100</nanos>
  <sequence>291</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">7</property>
</record>
<record>
  <date>2025-08-20T01:46:31.054633500Z</date>
  <millis>1755654391054</millis>
  <nanos>633500</nanos>
  <sequence>303</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;2406567 &amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:31.057641400Z</date>
  <millis>1755654391057</millis>
  <nanos>641400</nanos>
  <sequence>304</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:31.057641400Z</date>
  <millis>1755654391057</millis>
  <nanos>641400</nanos>
  <sequence>305</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination_1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">31</property>
  <property name="stepIndex">8</property>
</record>
<record>
  <date>2025-08-20T01:46:31.680850Z</date>
  <millis>1755654391680</millis>
  <nanos>850000</nanos>
  <sequence>315</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/pagination_1&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:31.681851600Z</date>
  <millis>1755654391681</millis>
  <nanos>851600</nanos>
  <sequence>316</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination_1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:31.681851600Z</date>
  <millis>1755654391681</millis>
  <nanos>851600</nanos>
  <sequence>317</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/EditCoordinates</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:31.681851600Z</date>
  <millis>1755654391681</millis>
  <nanos>851600</nanos>
  <sequence>318</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/EditCoordinates</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T01:46:31.683852700Z</date>
  <millis>1755654391683</millis>
  <nanos>852700</nanos>
  <sequence>319</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\SaveAndVerify.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T01:46:31.739221200Z</date>
  <millis>1755654391739</millis>
  <nanos>221200</nanos>
  <sequence>321</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_save&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">21</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:46:32.203199Z</date>
  <millis>1755654392203</millis>
  <nanos>199000</nanos>
  <sequence>331</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_save&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:32.205199Z</date>
  <millis>1755654392205</millis>
  <nanos>199000</nanos>
  <sequence>332</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_save&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:32.205199Z</date>
  <millis>1755654392205</millis>
  <nanos>199000</nanos>
  <sequence>333</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : waitForElementVisible(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;quot;), 5)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">23</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:46:32.655921800Z</date>
  <millis>1755654392655</millis>
  <nanos>921800</nanos>
  <sequence>343</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object &amp;apos;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;apos; is visible</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call</property>
  <property name="testops-execution-stacktrace">com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:324)
com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:319)
com.kms.katalon.core.logging.KeywordLogger$logPassed$0.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:84)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
SaveAndVerify.run(SaveAndVerify:23)
</property>
</record>
<record>
  <date>2025-08-20T01:46:32.657954400Z</date>
  <millis>1755654392657</millis>
  <nanos>954400</nanos>
  <sequence>344</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : waitForElementVisible(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;quot;), 5)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:32.657954400Z</date>
  <millis>1755654392657</millis>
  <nanos>954400</nanos>
  <sequence>345</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-20T01:46:32.978241500Z</date>
  <millis>1755654392978</millis>
  <nanos>241500</nanos>
  <sequence>355</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:32.980272700Z</date>
  <millis>1755654392980</millis>
  <nanos>272700</nanos>
  <sequence>356</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:32.980272700Z</date>
  <millis>1755654392980</millis>
  <nanos>272700</nanos>
  <sequence>357</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-20T01:46:36.789755700Z</date>
  <millis>1755654396789</millis>
  <nanos>755700</nanos>
  <sequence>365</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;apos; is: &amp;apos;148485&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:36.803880Z</date>
  <millis>1755654396803</millis>
  <nanos>880000</nanos>
  <sequence>367</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;148485&amp;apos; and expected object &amp;apos;148485&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:36.804858100Z</date>
  <millis>1755654396804</millis>
  <nanos>858100</nanos>
  <sequence>368</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:36.804858100Z</date>
  <millis>1755654396804</millis>
  <nanos>858100</nanos>
  <sequence>369</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-20T01:46:36.832791900Z</date>
  <millis>1755654396832</millis>
  <nanos>791900</nanos>
  <sequence>377</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;apos; is: &amp;apos;2406567&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:36.834794700Z</date>
  <millis>1755654396834</millis>
  <nanos>794700</nanos>
  <sequence>379</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;2406567&amp;apos; and expected object &amp;apos;2406567&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:36.834794700Z</date>
  <millis>1755654396834</millis>
  <nanos>794700</nanos>
  <sequence>380</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:36.835793500Z</date>
  <millis>1755654396835</millis>
  <nanos>793500</nanos>
  <sequence>381</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">31</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-20T01:46:36.859794400Z</date>
  <millis>1755654396859</millis>
  <nanos>794400</nanos>
  <sequence>389</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;apos; is: &amp;apos;148485&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:36.860794100Z</date>
  <millis>1755654396860</millis>
  <nanos>794100</nanos>
  <sequence>391</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;148485&amp;apos; and expected object &amp;apos;148485&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:36.862793900Z</date>
  <millis>1755654396862</millis>
  <nanos>793900</nanos>
  <sequence>392</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:36.862793900Z</date>
  <millis>1755654396862</millis>
  <nanos>793900</nanos>
  <sequence>393</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">32</property>
  <property name="stepIndex">7</property>
</record>
<record>
  <date>2025-08-20T01:46:36.891794400Z</date>
  <millis>1755654396891</millis>
  <nanos>794400</nanos>
  <sequence>401</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;apos; is: &amp;apos;2406567&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:36.893799Z</date>
  <millis>1755654396893</millis>
  <nanos>799000</nanos>
  <sequence>403</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;2406567&amp;apos; and expected object &amp;apos;2406567&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:36.895795600Z</date>
  <millis>1755654396895</millis>
  <nanos>795600</nanos>
  <sequence>404</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:36.895795600Z</date>
  <millis>1755654396895</millis>
  <nanos>795600</nanos>
  <sequence>405</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">33</property>
  <property name="stepIndex">8</property>
</record>
<record>
  <date>2025-08-20T01:46:37.324079300Z</date>
  <millis>1755654397324</millis>
  <nanos>79300</nanos>
  <sequence>415</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/pagination&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:37.328077900Z</date>
  <millis>1755654397328</millis>
  <nanos>77900</nanos>
  <sequence>416</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:37.328077900Z</date>
  <millis>1755654397328</millis>
  <nanos>77900</nanos>
  <sequence>417</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">35</property>
  <property name="stepIndex">9</property>
</record>
<record>
  <date>2025-08-20T01:46:37.391585900Z</date>
  <millis>1755654397391</millis>
  <nanos>585900</nanos>
  <sequence>425</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;apos; is: &amp;apos;148485&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:37.393636400Z</date>
  <millis>1755654397393</millis>
  <nanos>636400</nanos>
  <sequence>427</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;148485&amp;apos; and expected object &amp;apos;148485&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:37.393636400Z</date>
  <millis>1755654397393</millis>
  <nanos>636400</nanos>
  <sequence>428</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:37.393636400Z</date>
  <millis>1755654397393</millis>
  <nanos>636400</nanos>
  <sequence>429</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">36</property>
  <property name="stepIndex">10</property>
</record>
<record>
  <date>2025-08-20T01:46:37.421599400Z</date>
  <millis>1755654397421</millis>
  <nanos>599400</nanos>
  <sequence>437</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;apos; is: &amp;apos;2406567&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:37.422622300Z</date>
  <millis>1755654397422</millis>
  <nanos>622300</nanos>
  <sequence>439</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;2406567&amp;apos; and expected object &amp;apos;2406567&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:37.422622300Z</date>
  <millis>1755654397422</millis>
  <nanos>622300</nanos>
  <sequence>440</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:37.422622300Z</date>
  <millis>1755654397422</millis>
  <nanos>622300</nanos>
  <sequence>441</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:37.422622300Z</date>
  <millis>1755654397422</millis>
  <nanos>622300</nanos>
  <sequence>442</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: true,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T01:46:37.423601600Z</date>
  <millis>1755654397423</millis>
  <nanos>601600</nanos>
  <sequence>443</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start listener action : afterSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:37.423601600Z</date>
  <millis>1755654397423</millis>
  <nanos>601600</nanos>
  <sequence>445</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : closeBrowser()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">35</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:46:37.591314Z</date>
  <millis>1755654397591</millis>
  <nanos>314000</nanos>
  <sequence>447</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is closed</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.CloseBrowserKeyword.closeBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:46:37.592312800Z</date>
  <millis>1755654397592</millis>
  <nanos>312800</nanos>
  <sequence>449</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : closeBrowser()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:37.592312800Z</date>
  <millis>1755654397592</millis>
  <nanos>312800</nanos>
  <sequence>450</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End listener action : afterSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:46:37.620368500Z</date>
  <millis>1755654397620</millis>
  <nanos>368500</nanos>
  <sequence>451</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endSuite</method>
  <thread>1</thread>
  <message>End Test Suite : Test Suites/Mandarin/v1/Gis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
</log>
