<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE log SYSTEM "logger.dtd">
<log>
<record>
  <date>2025-08-20T01:58:32.350972100Z</date>
  <millis>1755655112350</millis>
  <nanos>972100</nanos>
  <sequence>0</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startSuite</method>
  <thread>1</thread>
  <message>Start Test Suite : Test Suites/Mandarin/v1/Gis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="rerunTestFailImmediately">false</property>
  <property name="retryCount">0</property>
  <property name="name">Gis</property>
  <property name="description"></property>
  <property name="id">Test Suites/Mandarin/v1/Gis</property>
</record>
<record>
  <date>2025-08-20T01:58:32.380767500Z</date>
  <millis>1755655112380</millis>
  <nanos>767500</nanos>
  <sequence>1</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>userFullName = Lin Kai Yee</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="userFullName">Lin Kai Yee</property>
</record>
<record>
  <date>2025-08-20T01:58:32.380767500Z</date>
  <millis>1755655112380</millis>
  <nanos>767500</nanos>
  <sequence>2</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>projectName = katalon</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="projectName">katalon</property>
</record>
<record>
  <date>2025-08-20T01:58:32.381766600Z</date>
  <millis>1755655112381</millis>
  <nanos>766600</nanos>
  <sequence>3</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>hostName = saw63 - host.docker.internal</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="hostName">saw63 - host.docker.internal</property>
</record>
<record>
  <date>2025-08-20T01:58:32.381766600Z</date>
  <millis>1755655112381</millis>
  <nanos>766600</nanos>
  <sequence>4</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>os = Windows 11 64bit</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="os">Windows 11 64bit</property>
</record>
<record>
  <date>2025-08-20T01:58:32.382764200Z</date>
  <millis>1755655112382</millis>
  <nanos>764200</nanos>
  <sequence>5</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>hostAddress = ************</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="hostAddress">************</property>
</record>
<record>
  <date>2025-08-20T01:58:32.383762Z</date>
  <millis>1755655112383</millis>
  <nanos>762000</nanos>
  <sequence>6</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>katalonVersion = ********</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="katalonVersion">********</property>
</record>
<record>
  <date>2025-08-20T01:58:32.392762900Z</date>
  <millis>1755655112392</millis>
  <nanos>762900</nanos>
  <sequence>7</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start listener action : beforeSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:32.419040900Z</date>
  <millis>1755655112419</millis>
  <nanos>40900</nanos>
  <sequence>9</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:58:32.661742300Z</date>
  <millis>1755655112661</millis>
  <nanos>742300</nanos>
  <sequence>11</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Starting &amp;apos;Chrome&amp;apos; driver</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:32.757265Z</date>
  <millis>1755655112757</millis>
  <nanos>265000</nanos>
  <sequence>12</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Action delay is set to 0 milliseconds</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:34.514474100Z</date>
  <millis>1755655114514</millis>
  <nanos>474100</nanos>
  <sequence>14</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>sessionId = 9e9e743081a218d900af9bbfa88a98f7</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="sessionId">9e9e743081a218d900af9bbfa88a98f7</property>
</record>
<record>
  <date>2025-08-20T01:58:34.516473300Z</date>
  <millis>1755655114516</millis>
  <nanos>473300</nanos>
  <sequence>15</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>browser = Chrome 138.0.7204.185</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="browser">Chrome 138.0.7204.185</property>
</record>
<record>
  <date>2025-08-20T01:58:34.517473100Z</date>
  <millis>1755655114517</millis>
  <nanos>473100</nanos>
  <sequence>16</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>platform = Windows 11</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="platform">Windows 11</property>
</record>
<record>
  <date>2025-08-20T01:58:34.518472600Z</date>
  <millis>1755655114518</millis>
  <nanos>472600</nanos>
  <sequence>17</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>seleniumVersion = 4.28.1</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="seleniumVersion">4.28.1</property>
</record>
<record>
  <date>2025-08-20T01:58:34.526926300Z</date>
  <millis>1755655114526</millis>
  <nanos>926300</nanos>
  <sequence>18</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&amp;quot;&amp;quot;, isApplyToDesiredCapabilities=true }</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="proxyInformation">ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }</property>
</record>
<record>
  <date>2025-08-20T01:58:34.573639800Z</date>
  <millis>1755655114573</millis>
  <nanos>639800</nanos>
  <sequence>19</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is opened with url: &amp;apos;&amp;apos;</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.OpenBrowserKeyword.openBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:34.575640800Z</date>
  <millis>1755655114575</millis>
  <nanos>640800</nanos>
  <sequence>20</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:34.576642600Z</date>
  <millis>1755655114576</millis>
  <nanos>642600</nanos>
  <sequence>21</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : maximizeWindow()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:58:34.649781900Z</date>
  <millis>1755655114649</millis>
  <nanos>781900</nanos>
  <sequence>23</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Current window maximized</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.MaximizeWindowKeyword.maximizeWindow</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:34.650794500Z</date>
  <millis>1755655114650</millis>
  <nanos>794500</nanos>
  <sequence>25</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : maximizeWindow()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:34.652817400Z</date>
  <millis>1755655114652</millis>
  <nanos>817400</nanos>
  <sequence>26</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End listener action : beforeSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:35.066202500Z</date>
  <millis>1755655115066</millis>
  <nanos>202500</nanos>
  <sequence>27</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/GoToGis</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/GoToGis</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\GoToGis.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T01:58:35.134238200Z</date>
  <millis>1755655115134</millis>
  <nanos>238200</nanos>
  <sequence>29</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">20</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:58:35.139204500Z</date>
  <millis>1755655115139</millis>
  <nanos>204500</nanos>
  <sequence>31</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>A browser is already opened. Closing browser and opening a new one</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:35.360230600Z</date>
  <millis>1755655115360</millis>
  <nanos>230600</nanos>
  <sequence>32</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Starting &amp;apos;Chrome&amp;apos; driver</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:35.362232500Z</date>
  <millis>1755655115362</millis>
  <nanos>232500</nanos>
  <sequence>33</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Action delay is set to 0 milliseconds</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:36.204505100Z</date>
  <millis>1755655116204</millis>
  <nanos>505100</nanos>
  <sequence>35</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>sessionId = b86771a5c22319e9e743a40f9e23f804</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="sessionId">b86771a5c22319e9e743a40f9e23f804</property>
</record>
<record>
  <date>2025-08-20T01:58:36.205568300Z</date>
  <millis>1755655116205</millis>
  <nanos>568300</nanos>
  <sequence>36</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>browser = Chrome 138.0.7204.185</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="browser">Chrome 138.0.7204.185</property>
</record>
<record>
  <date>2025-08-20T01:58:36.206555900Z</date>
  <millis>1755655116206</millis>
  <nanos>555900</nanos>
  <sequence>37</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>platform = Windows 11</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="platform">Windows 11</property>
</record>
<record>
  <date>2025-08-20T01:58:36.206555900Z</date>
  <millis>1755655116206</millis>
  <nanos>555900</nanos>
  <sequence>38</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>seleniumVersion = 4.28.1</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="seleniumVersion">4.28.1</property>
</record>
<record>
  <date>2025-08-20T01:58:36.207513800Z</date>
  <millis>1755655116207</millis>
  <nanos>513800</nanos>
  <sequence>39</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&amp;quot;&amp;quot;, isApplyToDesiredCapabilities=true }</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="proxyInformation">ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }</property>
</record>
<record>
  <date>2025-08-20T01:58:36.229639300Z</date>
  <millis>1755655116229</millis>
  <nanos>639300</nanos>
  <sequence>40</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is opened with url: &amp;apos;&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.OpenBrowserKeyword.openBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:36.232639600Z</date>
  <millis>1755655116232</millis>
  <nanos>639600</nanos>
  <sequence>41</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:36.232639600Z</date>
  <millis>1755655116232</millis>
  <nanos>639600</nanos>
  <sequence>42</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : navigateToUrl(&amp;quot;https://land2.daoyidh.com/Gis&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:58:36.547510900Z</date>
  <millis>1755655116547</millis>
  <nanos>510900</nanos>
  <sequence>45</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Navigate to &amp;apos;https://land2.daoyidh.com/Gis&amp;apos; successfully</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.NavigateToUrlKeyword.navigateToUrl</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:36.547510900Z</date>
  <millis>1755655116547</millis>
  <nanos>510900</nanos>
  <sequence>46</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : navigateToUrl(&amp;quot;https://land2.daoyidh.com/Gis&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:36.548767600Z</date>
  <millis>1755655116548</millis>
  <nanos>767600</nanos>
  <sequence>47</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:36.549776800Z</date>
  <millis>1755655116549</millis>
  <nanos>776800</nanos>
  <sequence>48</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T01:58:36.553811Z</date>
  <millis>1755655116553</millis>
  <nanos>811000</nanos>
  <sequence>49</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</property>
  <property name="description">Login</property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\LoginWithEmail.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T01:58:36.652885600Z</date>
  <millis>1755655116652</millis>
  <nanos>885600</nanos>
  <sequence>51</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">20</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:58:37.869538900Z</date>
  <millis>1755655117869</millis>
  <nanos>538900</nanos>
  <sequence>61</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:37.872539500Z</date>
  <millis>1755655117872</millis>
  <nanos>539500</nanos>
  <sequence>62</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:37.872539500Z</date>
  <millis>1755655117872</millis>
  <nanos>539500</nanos>
  <sequence>63</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:58:38.403916200Z</date>
  <millis>1755655118403</millis>
  <nanos>916200</nanos>
  <sequence>73</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:38.405929900Z</date>
  <millis>1755655118405</millis>
  <nanos>929900</nanos>
  <sequence>74</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:38.406927900Z</date>
  <millis>1755655118406</millis>
  <nanos>927900</nanos>
  <sequence>75</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;quot;), &amp;quot;<EMAIL>&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">24</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-20T01:58:39.208096300Z</date>
  <millis>1755655119208</millis>
  <nanos>96300</nanos>
  <sequence>91</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;<EMAIL>&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:39.211107600Z</date>
  <millis>1755655119211</millis>
  <nanos>107600</nanos>
  <sequence>92</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;quot;), &amp;quot;<EMAIL>&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:39.211107600Z</date>
  <millis>1755655119211</millis>
  <nanos>107600</nanos>
  <sequence>93</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-20T01:58:39.505291800Z</date>
  <millis>1755655119505</millis>
  <nanos>291800</nanos>
  <sequence>103</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:39.507291400Z</date>
  <millis>1755655119507</millis>
  <nanos>291400</nanos>
  <sequence>104</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:39.507291400Z</date>
  <millis>1755655119507</millis>
  <nanos>291400</nanos>
  <sequence>105</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setEncryptedText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;quot;), &amp;quot;0D41p6Ct1VJ35FCiyaw3Gw==&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">28</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-20T01:58:40.420683500Z</date>
  <millis>1755655120420</millis>
  <nanos>683500</nanos>
  <sequence>116</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text ****** has been set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetEncryptedTextKeyword.setEncryptedText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:40.421685700Z</date>
  <millis>1755655120421</millis>
  <nanos>685700</nanos>
  <sequence>117</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setEncryptedText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;quot;), &amp;quot;0D41p6Ct1VJ35FCiyaw3Gw==&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:40.421685700Z</date>
  <millis>1755655120421</millis>
  <nanos>685700</nanos>
  <sequence>118</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-20T01:58:40.705350900Z</date>
  <millis>1755655120705</millis>
  <nanos>350900</nanos>
  <sequence>128</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:40.706349700Z</date>
  <millis>1755655120706</millis>
  <nanos>349700</nanos>
  <sequence>129</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:40.706349700Z</date>
  <millis>1755655120706</millis>
  <nanos>349700</nanos>
  <sequence>130</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:40.707349300Z</date>
  <millis>1755655120707</millis>
  <nanos>349300</nanos>
  <sequence>131</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T01:58:40.709351100Z</date>
  <millis>1755655120709</millis>
  <nanos>351100</nanos>
  <sequence>132</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\SearchTextInGis.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T01:58:40.771357200Z</date>
  <millis>1755655120771</millis>
  <nanos>357200</nanos>
  <sequence>134</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button__1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:58:43.159758800Z</date>
  <millis>1755655123159</millis>
  <nanos>758800</nanos>
  <sequence>144</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button__1&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:43.160758200Z</date>
  <millis>1755655123160</millis>
  <nanos>758200</nanos>
  <sequence>145</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button__1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:43.161757800Z</date>
  <millis>1755655123161</millis>
  <nanos>757800</nanos>
  <sequence>146</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;quot;), &amp;quot;新北勢&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">25</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:58:43.772807300Z</date>
  <millis>1755655123772</millis>
  <nanos>807300</nanos>
  <sequence>158</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;新北勢&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:43.773808200Z</date>
  <millis>1755655123773</millis>
  <nanos>808200</nanos>
  <sequence>159</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;quot;), &amp;quot;新北勢&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:43.773808200Z</date>
  <millis>1755655123773</millis>
  <nanos>808200</nanos>
  <sequence>160</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:43.775805900Z</date>
  <millis>1755655123775</millis>
  <nanos>805900</nanos>
  <sequence>161</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T01:58:43.779807300Z</date>
  <millis>1755655123779</millis>
  <nanos>807300</nanos>
  <sequence>162</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\ChoosePerPage.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T01:58:43.862302700Z</date>
  <millis>1755655123862</millis>
  <nanos>302700</nanos>
  <sequence>164</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">24</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:58:43.873350400Z</date>
  <millis>1755655123873</millis>
  <nanos>350400</nanos>
  <sequence>165</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:43.873350400Z</date>
  <millis>1755655123873</millis>
  <nanos>350400</nanos>
  <sequence>166</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;div[role=&amp;quot;button&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">25</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:58:43.879316500Z</date>
  <millis>1755655123879</millis>
  <nanos>316500</nanos>
  <sequence>167</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;div[role=&amp;quot;button&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:43.880319400Z</date>
  <millis>1755655123880</millis>
  <nanos>319400</nanos>
  <sequence>168</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(dropdownButton)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-20T01:58:44.282947800Z</date>
  <millis>1755655124282</millis>
  <nanos>947800</nanos>
  <sequence>177</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:44.284948100Z</date>
  <millis>1755655124284</millis>
  <nanos>948100</nanos>
  <sequence>178</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(dropdownButton)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:44.284948100Z</date>
  <millis>1755655124284</millis>
  <nanos>948100</nanos>
  <sequence>179</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : option50 = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-20T01:58:44.286948500Z</date>
  <millis>1755655124286</millis>
  <nanos>948500</nanos>
  <sequence>180</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : option50 = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:44.287948Z</date>
  <millis>1755655124287</millis>
  <nanos>948000</nanos>
  <sequence>181</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : option50.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;li.MuiMenuItem-root[data-value=&amp;quot;50&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-20T01:58:44.288948500Z</date>
  <millis>1755655124288</millis>
  <nanos>948500</nanos>
  <sequence>182</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : option50.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;li.MuiMenuItem-root[data-value=&amp;quot;50&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:44.289948900Z</date>
  <millis>1755655124289</millis>
  <nanos>948900</nanos>
  <sequence>183</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : waitForElementVisible(option50, 5)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">31</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-20T01:58:44.827178400Z</date>
  <millis>1755655124827</millis>
  <nanos>178400</nanos>
  <sequence>192</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object &amp;apos;&amp;apos; is visible</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call</property>
  <property name="testops-execution-stacktrace">com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:324)
com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:319)
com.kms.katalon.core.logging.KeywordLogger$logPassed$0.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:84)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
ChoosePerPage.run(ChoosePerPage:31)
</property>
</record>
<record>
  <date>2025-08-20T01:58:44.828179400Z</date>
  <millis>1755655124828</millis>
  <nanos>179400</nanos>
  <sequence>193</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : waitForElementVisible(option50, 5)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:44.829180600Z</date>
  <millis>1755655124829</millis>
  <nanos>180600</nanos>
  <sequence>194</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(option50)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">32</property>
  <property name="stepIndex">7</property>
</record>
<record>
  <date>2025-08-20T01:58:45.251087600Z</date>
  <millis>1755655125251</millis>
  <nanos>87600</nanos>
  <sequence>203</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:45.252138Z</date>
  <millis>1755655125252</millis>
  <nanos>138000</nanos>
  <sequence>204</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(option50)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:45.252138Z</date>
  <millis>1755655125252</millis>
  <nanos>138000</nanos>
  <sequence>205</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:45.253089200Z</date>
  <millis>1755655125253</millis>
  <nanos>89200</nanos>
  <sequence>206</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T01:58:45.259087200Z</date>
  <millis>1755655125259</millis>
  <nanos>87200</nanos>
  <sequence>207</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/EditCoordinates</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/EditCoordinates</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/EditCoordinates</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\EditCoordinates.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T01:58:45.310794600Z</date>
  <millis>1755655125310</millis>
  <nanos>794600</nanos>
  <sequence>209</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">21</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:58:45.754328900Z</date>
  <millis>1755655125754</millis>
  <nanos>328900</nanos>
  <sequence>221</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;148485&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:45.756327100Z</date>
  <millis>1755655125756</millis>
  <nanos>327100</nanos>
  <sequence>222</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:45.757326100Z</date>
  <millis>1755655125757</millis>
  <nanos>326100</nanos>
  <sequence>223</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:58:46.257815400Z</date>
  <millis>1755655126257</millis>
  <nanos>815400</nanos>
  <sequence>235</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;2406567 &amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:46.263102600Z</date>
  <millis>1755655126263</millis>
  <nanos>102600</nanos>
  <sequence>236</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:46.264101100Z</date>
  <millis>1755655126264</millis>
  <nanos>101100</nanos>
  <sequence>237</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">23</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-20T01:58:46.669985800Z</date>
  <millis>1755655126669</millis>
  <nanos>985800</nanos>
  <sequence>249</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;148485&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:46.670983400Z</date>
  <millis>1755655126670</millis>
  <nanos>983400</nanos>
  <sequence>250</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:46.671982500Z</date>
  <millis>1755655126671</millis>
  <nanos>982500</nanos>
  <sequence>251</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">24</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-20T01:58:47.099568200Z</date>
  <millis>1755655127099</millis>
  <nanos>568200</nanos>
  <sequence>263</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;2406567 &amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:47.102571200Z</date>
  <millis>1755655127102</millis>
  <nanos>571200</nanos>
  <sequence>264</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:47.103568Z</date>
  <millis>1755655127103</millis>
  <nanos>568000</nanos>
  <sequence>265</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-20T01:58:47.751224800Z</date>
  <millis>1755655127751</millis>
  <nanos>224800</nanos>
  <sequence>275</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/pagination&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:47.754225800Z</date>
  <millis>1755655127754</millis>
  <nanos>225800</nanos>
  <sequence>276</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:47.755226700Z</date>
  <millis>1755655127755</millis>
  <nanos>226700</nanos>
  <sequence>277</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">28</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-20T01:58:48.201822500Z</date>
  <millis>1755655128201</millis>
  <nanos>822500</nanos>
  <sequence>289</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;148485&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:48.204268400Z</date>
  <millis>1755655128204</millis>
  <nanos>268400</nanos>
  <sequence>290</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:48.204268400Z</date>
  <millis>1755655128204</millis>
  <nanos>268400</nanos>
  <sequence>291</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">7</property>
</record>
<record>
  <date>2025-08-20T01:58:48.635900600Z</date>
  <millis>1755655128635</millis>
  <nanos>900600</nanos>
  <sequence>303</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;2406567 &amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:48.636899400Z</date>
  <millis>1755655128636</millis>
  <nanos>899400</nanos>
  <sequence>304</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:48.636899400Z</date>
  <millis>1755655128636</millis>
  <nanos>899400</nanos>
  <sequence>305</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination_1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">31</property>
  <property name="stepIndex">8</property>
</record>
<record>
  <date>2025-08-20T01:58:49.260470400Z</date>
  <millis>1755655129260</millis>
  <nanos>470400</nanos>
  <sequence>315</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/pagination_1&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:49.261497900Z</date>
  <millis>1755655129261</millis>
  <nanos>497900</nanos>
  <sequence>316</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination_1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:49.262484800Z</date>
  <millis>1755655129262</millis>
  <nanos>484800</nanos>
  <sequence>317</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyElementVisible(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">33</property>
  <property name="stepIndex">9</property>
</record>
<record>
  <date>2025-08-20T01:58:49.283469Z</date>
  <millis>1755655129283</millis>
  <nanos>469000</nanos>
  <sequence>319</sequence>
  <level>ERROR</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.
Reason:
groovy.lang.MissingMethodException: No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)
	at EditCoordinates.run(EditCoordinates:33)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755655109207.run(TempTestSuite1755655109207.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</message>
  <exception>
    <message>com.kms.katalon.core.exception.StepFailedException: No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)</message>
    <frame>
      <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
      <method>logMessage</method>
      <line>500</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.logging.KeywordLogger</class>
      <method>logMessage</method>
      <line>439</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>logError</method>
      <line>593</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>logErrorForTheLastError</method>
      <line>206</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>onExecutionError</method>
      <line>171</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>processExecutionPhase</method>
      <line>465</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>accessMainPhase</method>
      <line>447</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>execute</method>
      <line>321</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.common.CommonExecutor</class>
      <method>accessTestCaseMainPhase</method>
      <line>71</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestSuiteExecutor</class>
      <method>accessTestSuiteMainPhase</method>
      <line>160</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestSuiteExecutor</class>
      <method>execute</method>
      <line>107</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseMain</class>
      <method>startTestSuite</method>
      <line>195</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.vmplugin.v8.IndyInterface</class>
      <method>fromCache</method>
      <line>318</line>
    </frame>
    <frame>
      <class>TempTestSuite1755655109207</class>
      <method>run</method>
      <line>35</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>runScriptOrMainOrTestOrRunnable</method>
      <line>254</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>run</method>
      <line>360</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>run</method>
      <line>349</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>processOnce</method>
      <line>652</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>run</method>
      <line>398</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>access$1400</method>
      <line>68</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain$GroovyCommand</class>
      <method>process</method>
      <line>322</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>processArgs</method>
      <line>142</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>main</method>
      <line>115</line>
    </frame>
    <frame>
      <class>jdk.internal.reflect.NativeMethodAccessorImpl</class>
      <method>invoke0</method>
    </frame>
    <frame>
      <class>jdk.internal.reflect.NativeMethodAccessorImpl</class>
      <method>invoke</method>
      <line>77</line>
    </frame>
    <frame>
      <class>jdk.internal.reflect.DelegatingMethodAccessorImpl</class>
      <method>invoke</method>
      <line>43</line>
    </frame>
    <frame>
      <class>java.lang.reflect.Method</class>
      <method>invoke</method>
      <line>569</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.tools.GroovyStarter</class>
      <method>rootLoader</method>
      <line>117</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.tools.GroovyStarter</class>
      <method>main</method>
      <line>39</line>
    </frame>
  </exception>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.stacktrace">groovy.lang.MissingMethodException: No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)
	at EditCoordinates.run(EditCoordinates:33)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755655109207.run(TempTestSuite1755655109207.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.class">groovy.lang.MissingMethodException</property>
  <property name="failed.exception.message">No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)</property>
</record>
<record>
  <date>2025-08-20T01:58:49.313468600Z</date>
  <millis>1755655129313</millis>
  <nanos>468600</nanos>
  <sequence>321</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyElementVisible(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:49.324468800Z</date>
  <millis>1755655129324</millis>
  <nanos>468800</nanos>
  <sequence>323</sequence>
  <level>ERROR</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.
Reason:
groovy.lang.MissingMethodException: No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)
	at EditCoordinates.run(EditCoordinates:33)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755655109207.run(TempTestSuite1755655109207.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</message>
  <exception>
    <message>com.kms.katalon.core.exception.StepFailedException: No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)</message>
    <frame>
      <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
      <method>logMessage</method>
      <line>500</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.logging.KeywordLogger</class>
      <method>logMessage</method>
      <line>439</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>logError</method>
      <line>593</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>onExecutionError</method>
      <line>187</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>processExecutionPhase</method>
      <line>465</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>accessMainPhase</method>
      <line>447</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>execute</method>
      <line>321</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.common.CommonExecutor</class>
      <method>accessTestCaseMainPhase</method>
      <line>71</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestSuiteExecutor</class>
      <method>accessTestSuiteMainPhase</method>
      <line>160</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestSuiteExecutor</class>
      <method>execute</method>
      <line>107</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseMain</class>
      <method>startTestSuite</method>
      <line>195</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.vmplugin.v8.IndyInterface</class>
      <method>fromCache</method>
      <line>318</line>
    </frame>
    <frame>
      <class>TempTestSuite1755655109207</class>
      <method>run</method>
      <line>35</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>runScriptOrMainOrTestOrRunnable</method>
      <line>254</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>run</method>
      <line>360</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>run</method>
      <line>349</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>processOnce</method>
      <line>652</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>run</method>
      <line>398</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>access$1400</method>
      <line>68</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain$GroovyCommand</class>
      <method>process</method>
      <line>322</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>processArgs</method>
      <line>142</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>main</method>
      <line>115</line>
    </frame>
    <frame>
      <class>jdk.internal.reflect.NativeMethodAccessorImpl</class>
      <method>invoke0</method>
    </frame>
    <frame>
      <class>jdk.internal.reflect.NativeMethodAccessorImpl</class>
      <method>invoke</method>
      <line>77</line>
    </frame>
    <frame>
      <class>jdk.internal.reflect.DelegatingMethodAccessorImpl</class>
      <method>invoke</method>
      <line>43</line>
    </frame>
    <frame>
      <class>java.lang.reflect.Method</class>
      <method>invoke</method>
      <line>569</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.tools.GroovyStarter</class>
      <method>rootLoader</method>
      <line>117</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.tools.GroovyStarter</class>
      <method>main</method>
      <line>39</line>
    </frame>
  </exception>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.stacktrace">groovy.lang.MissingMethodException: No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)
	at EditCoordinates.run(EditCoordinates:33)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755655109207.run(TempTestSuite1755655109207.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.class">groovy.lang.MissingMethodException</property>
  <property name="failed.exception.message">No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)</property>
</record>
<record>
  <date>2025-08-20T01:58:49.348470300Z</date>
  <millis>1755655129348</millis>
  <nanos>470300</nanos>
  <sequence>325</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/EditCoordinates</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T01:58:49.351468700Z</date>
  <millis>1755655129351</millis>
  <nanos>468700</nanos>
  <sequence>326</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\SaveAndVerify.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T01:58:49.404468600Z</date>
  <millis>1755655129404</millis>
  <nanos>468600</nanos>
  <sequence>328</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_save&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">21</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:58:49.746697Z</date>
  <millis>1755655129746</millis>
  <nanos>697000</nanos>
  <sequence>338</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_save&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:49.748698700Z</date>
  <millis>1755655129748</millis>
  <nanos>698700</nanos>
  <sequence>339</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_save&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:49.748698700Z</date>
  <millis>1755655129748</millis>
  <nanos>698700</nanos>
  <sequence>340</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : waitForElementVisible(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;quot;), 5)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">23</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T01:58:50.204656600Z</date>
  <millis>1755655130204</millis>
  <nanos>656600</nanos>
  <sequence>350</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object &amp;apos;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;apos; is visible</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call</property>
  <property name="testops-execution-stacktrace">com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:324)
com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:319)
com.kms.katalon.core.logging.KeywordLogger$logPassed$0.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:84)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
SaveAndVerify.run(SaveAndVerify:23)
</property>
</record>
<record>
  <date>2025-08-20T01:58:50.205683500Z</date>
  <millis>1755655130205</millis>
  <nanos>683500</nanos>
  <sequence>351</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : waitForElementVisible(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;quot;), 5)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:50.206657900Z</date>
  <millis>1755655130206</millis>
  <nanos>657900</nanos>
  <sequence>352</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-20T01:58:50.538425500Z</date>
  <millis>1755655130538</millis>
  <nanos>425500</nanos>
  <sequence>362</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:50.541519600Z</date>
  <millis>1755655130541</millis>
  <nanos>519600</nanos>
  <sequence>363</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:50.541519600Z</date>
  <millis>1755655130541</millis>
  <nanos>519600</nanos>
  <sequence>364</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-20T01:58:54.352439900Z</date>
  <millis>1755655134352</millis>
  <nanos>439900</nanos>
  <sequence>372</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;apos; is: &amp;apos;148485&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:54.366913Z</date>
  <millis>1755655134366</millis>
  <nanos>913000</nanos>
  <sequence>374</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;148485&amp;apos; and expected object &amp;apos;148485&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:54.367977600Z</date>
  <millis>1755655134367</millis>
  <nanos>977600</nanos>
  <sequence>375</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:54.367977600Z</date>
  <millis>1755655134367</millis>
  <nanos>977600</nanos>
  <sequence>376</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-20T01:58:54.397976500Z</date>
  <millis>1755655134397</millis>
  <nanos>976500</nanos>
  <sequence>384</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;apos; is: &amp;apos;2406567&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:54.399006Z</date>
  <millis>1755655134399</millis>
  <nanos>6000</nanos>
  <sequence>386</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;2406567&amp;apos; and expected object &amp;apos;2406567&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:54.399974100Z</date>
  <millis>1755655134399</millis>
  <nanos>974100</nanos>
  <sequence>387</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:54.399974100Z</date>
  <millis>1755655134399</millis>
  <nanos>974100</nanos>
  <sequence>388</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">31</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-20T01:58:54.424977800Z</date>
  <millis>1755655134424</millis>
  <nanos>977800</nanos>
  <sequence>396</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;apos; is: &amp;apos;148485&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:54.426978300Z</date>
  <millis>1755655134426</millis>
  <nanos>978300</nanos>
  <sequence>398</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;148485&amp;apos; and expected object &amp;apos;148485&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:54.427978900Z</date>
  <millis>1755655134427</millis>
  <nanos>978900</nanos>
  <sequence>399</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:54.427978900Z</date>
  <millis>1755655134427</millis>
  <nanos>978900</nanos>
  <sequence>400</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">32</property>
  <property name="stepIndex">7</property>
</record>
<record>
  <date>2025-08-20T01:58:54.455993Z</date>
  <millis>1755655134455</millis>
  <nanos>993000</nanos>
  <sequence>408</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;apos; is: &amp;apos;2406567&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:54.457995200Z</date>
  <millis>1755655134457</millis>
  <nanos>995200</nanos>
  <sequence>410</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;2406567&amp;apos; and expected object &amp;apos;2406567&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:54.458509600Z</date>
  <millis>1755655134458</millis>
  <nanos>509600</nanos>
  <sequence>411</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:54.458509600Z</date>
  <millis>1755655134458</millis>
  <nanos>509600</nanos>
  <sequence>412</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">33</property>
  <property name="stepIndex">8</property>
</record>
<record>
  <date>2025-08-20T01:58:54.878229500Z</date>
  <millis>1755655134878</millis>
  <nanos>229500</nanos>
  <sequence>422</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/pagination&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:54.880191200Z</date>
  <millis>1755655134880</millis>
  <nanos>191200</nanos>
  <sequence>423</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:54.881189900Z</date>
  <millis>1755655134881</millis>
  <nanos>189900</nanos>
  <sequence>424</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">35</property>
  <property name="stepIndex">9</property>
</record>
<record>
  <date>2025-08-20T01:58:54.963188Z</date>
  <millis>1755655134963</millis>
  <nanos>188000</nanos>
  <sequence>432</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;apos; is: &amp;apos;148485&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:54.965182200Z</date>
  <millis>1755655134965</millis>
  <nanos>182200</nanos>
  <sequence>434</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;148485&amp;apos; and expected object &amp;apos;148485&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:54.966181100Z</date>
  <millis>1755655134966</millis>
  <nanos>181100</nanos>
  <sequence>435</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:54.966181100Z</date>
  <millis>1755655134966</millis>
  <nanos>181100</nanos>
  <sequence>436</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">36</property>
  <property name="stepIndex">10</property>
</record>
<record>
  <date>2025-08-20T01:58:54.994212800Z</date>
  <millis>1755655134994</millis>
  <nanos>212800</nanos>
  <sequence>444</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;apos; is: &amp;apos;2406567&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:54.995182800Z</date>
  <millis>1755655134995</millis>
  <nanos>182800</nanos>
  <sequence>446</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;2406567&amp;apos; and expected object &amp;apos;2406567&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:54.995182800Z</date>
  <millis>1755655134995</millis>
  <nanos>182800</nanos>
  <sequence>447</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:54.995182800Z</date>
  <millis>1755655134995</millis>
  <nanos>182800</nanos>
  <sequence>448</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:54.996180500Z</date>
  <millis>1755655134996</millis>
  <nanos>180500</nanos>
  <sequence>449</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: true,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T01:58:54.996180500Z</date>
  <millis>1755655134996</millis>
  <nanos>180500</nanos>
  <sequence>450</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start listener action : afterSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:54.997180800Z</date>
  <millis>1755655134997</millis>
  <nanos>180800</nanos>
  <sequence>452</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : closeBrowser()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">35</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T01:58:55.190675200Z</date>
  <millis>1755655135190</millis>
  <nanos>675200</nanos>
  <sequence>454</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is closed</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.CloseBrowserKeyword.closeBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T01:58:55.190675200Z</date>
  <millis>1755655135190</millis>
  <nanos>675200</nanos>
  <sequence>456</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : closeBrowser()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:55.190675200Z</date>
  <millis>1755655135190</millis>
  <nanos>675200</nanos>
  <sequence>457</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End listener action : afterSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:58:55.213675500Z</date>
  <millis>1755655135213</millis>
  <nanos>675500</nanos>
  <sequence>458</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endSuite</method>
  <thread>1</thread>
  <message>End Test Suite : Test Suites/Mandarin/v1/Gis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
</log>
