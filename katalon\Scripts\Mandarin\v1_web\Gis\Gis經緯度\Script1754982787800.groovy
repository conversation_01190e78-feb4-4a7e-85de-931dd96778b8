import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject
import static com.kms.katalon.core.testobject.ObjectRepository.findWindowsObject
import com.kms.katalon.core.checkpoint.Checkpoint as Checkpoint
import com.kms.katalon.core.cucumber.keyword.CucumberBuiltinKeywords as CucumberKW
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling as FailureHandling
import com.kms.katalon.core.testcase.TestCase as TestCase
import com.kms.katalon.core.testdata.TestData as TestData
import com.kms.katalon.core.testng.keyword.TestNGBuiltinKeywords as TestNGKW
import com.kms.katalon.core.testobject.TestObject as TestObject
import com.kms.katalon.core.testobject.ConditionType as ConditionType
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI
import com.kms.katalon.core.windows.keyword.WindowsBuiltinKeywords as Windows
import internal.GlobalVariable as GlobalVariable
import org.openqa.selenium.Keys as Keys

WebUI.openBrowser('')

WebUI.navigateToUrl('http://localhost:3000/Gis')

// 登入

WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Gis/button_'))

WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail'))

WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Gis/input_Email_email'), '<EMAIL>')

WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Gis/button_Next'))

WebUI.setEncryptedText(findTestObject('Object Repository/Mandarin/v1_web/Gis/input_Password_password'), '0D41p6Ct1VJ35FCiyaw3Gw==')

WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Gis/button_Sign In'))


// 跳到經緯度頁面

WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Gis/button__1'))
// 搜尋新北勢

WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Gis/input__mui-3'), '新北勢')

// 選擇單頁50筆
// 點擊下拉按鈕（role="combobox"）
TestObject dropdownButton = new TestObject()
dropdownButton.addProperty("css", ConditionType.EQUALS, 'div[role="combobox"]')
WebUI.click(dropdownButton)

// 等待選單出現並選擇「每頁顯示 50 筆」
TestObject option50 = new TestObject()
option50.addProperty("css", ConditionType.EQUALS, 'li.MuiMenuItem-root[data-value="50"]')
WebUI.waitForElementVisible(option50, 5)
WebUI.click(option50)

// 編輯
WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/119-1_twd97_x'), '148485')
WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/119-1_twd97_y'), '2406567 ')
WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/1261_twd97_x'), '148485')
WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/1261_twd97_y'), '2406567 ')
// 去第二頁
WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Gis/pagination'))

WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/249_twd97_x'), '148485')
WebUI.setText(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/249_twd97_y'), '2406567 ')
// 回第一頁
WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Gis/pagination_1'))

// 打開save modal
WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Gis/button_save'))
// 等待save modal 內的儲存按鈕出現
WebUI.waitForElementVisible(findTestObject('Object Repository/Mandarin/v1_web/Gis/save_modal_save_button'), 5)
//WebUI.acceptAlert()
// 點save modal 內的儲存按鈕
WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Gis/save_modal_save_button'))

// 驗證輸入值是否正確
WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/119-1_twd97_x'), 'value'), '148485')
WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/119-1_twd97_y'), 'value'), '2406567')
WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/1261_twd97_x'), 'value'), '148485')
WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/1261_twd97_y'), 'value'), '2406567')
WebUI.click(findTestObject('Object Repository/Mandarin/v1_web/Gis/pagination'))

WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/249_twd97_x'), 'value'), '148485')
WebUI.verifyEqual(WebUI.getAttribute(findTestObject('Object Repository/Mandarin/v1_web/Gis/Spy_element/249_twd97_y'), 'value'), '2406567')

//


//WebUI.closeBrowser()
