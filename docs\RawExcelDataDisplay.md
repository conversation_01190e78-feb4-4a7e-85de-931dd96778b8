# 原始 Excel 資料顯示功能

## 功能概述

在開發階段，當使用者上傳 Excel 檔案進行驗證時，現在可以在「表格檢視」中查看實際讀取到的原始 Excel 資料。此功能有助於開發人員快速診斷資料問題，比對驗證邏輯與實際資料。

## 使用方式

### 1. 上傳 Excel 檔案
在「匯入資料」頁面上傳一個 Excel 檔案進行驗證。

### 2. 切換到表格檢視
如果驗證失敗，系統會顯示錯誤資訊。點擊切換至「表格檢視」模式。

### 3. 展開列詳情
在表格檢視中，點擊任一列右側的展開圖示 (▼) 查看該列的詳細錯誤資訊。

### 4. 查看原始資料
在展開的詳情區域底部，會顯示「原始 Excel 資料 (開發模式)」區塊，內含：
- **工作表名稱**：Excel 工作表的名稱
- **列號**：資料所在的列號
- **欄位資料表格**：
  - **欄位 ID**：如 `Land-->landName`
  - **欄位名稱**：中文欄位名稱
  - **實際值**：從 Excel 讀取到的實際值
    - 空值會顯示為 `(空值)` 並以灰色斜體標示
  - **類型**：儲存格的資料類型 (如 `string`, `number`, `date` 等)

## 技術實作

### 資料收集流程

#### ImportDataPage/index.js

在驗證過程中，同時收集原始資料：

```javascript
// 初始化原始資料收集器
const rawRowData = {}; // 收集原始資料 (開發用)

// 在每列資料驗證時收集
worksheet.eachRow((row, rowNumber) => {
  if (rowNumber >= startRow) {
    // 初始化該列的資料結構
    if (!rawRowData[rowNumber]) {
      rawRowData[rowNumber] = {
        sheetName: worksheet.name,
        rowNumber,
        data: {}
      };
    }
    
    row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
      const colId = worksheet.getRow(idIdx).getCell(colNumber).value;
      
      // 收集此儲存格的原始值
      if (colId) {
        rawRowData[rowNumber].data[colId] = {
          value: cell.value,
          type: cell.type,
          columnIndex: colNumber,
          columnName: fbExHeader.find(h => h.id === colId)?.label || colId
        };
      }
      
      // ... 驗證邏輯
    });
  }
});
```

#### 資料結構

```javascript
rawRowData = {
  4: {  // 列號
    sheetName: "Sheet1",
    rowNumber: 4,
    data: {
      "Land-->landName": {
        value: "測試地號",
        type: "string",
        columnIndex: 1,
        columnName: "地號"
      },
      "Land-->landSerialNumber": {
        value: "001",
        type: "string",
        columnIndex: 2,
        columnName: "地號序號"
      },
      // ... 更多欄位
    }
  },
  5: { /* ... */ },
  // ... 更多列
}
```

### 資料傳遞

```javascript
// 傳遞給 ValidationErrorsEnhanced 組件
<ValidationErrorsEnhanced 
  errors={detailedErrors} 
  rawExcelData={rawExcelData}
/>
```

### UI 顯示

#### ValidationErrorsEnhanced.js

在表格檢視的展開區域中顯示：

```jsx
{/* 顯示原始 Excel 資料 (開發用) */}
{rawExcelData[rowNumber] && (
  <Box sx={{ mt: 2, p: 2, bgcolor: 'info.lighter', borderRadius: 1 }}>
    <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
      <AssignmentIcon /> 原始 Excel 資料 (開發模式)
    </Typography>
    <Box sx={{ /* 樣式 */ }}>
      <Typography variant="caption">
        工作表: {rawExcelData[rowNumber].sheetName} | 
        列號: {rawExcelData[rowNumber].rowNumber}
      </Typography>
      <Table size="small">
        {/* 顯示欄位資料 */}
      </Table>
    </Box>
  </Box>
)}
```

## 使用場景

### 1. 驗證邏輯除錯
當驗證規則標記某個欄位為錯誤，但不確定實際讀取到的值是什麼時，可以直接查看原始資料。

**範例**：
- 錯誤訊息：「地號序號格式錯誤」
- 原始資料：`value: "０01"` (全形數字零)
- 診斷：發現使用者輸入了全形字元

### 2. 資料類型檢查
確認 ExcelJS 如何解析不同格式的資料。

**範例**：
- 預期：數字
- 原始資料：`type: "string"`, `value: "123"`
- 診斷：Excel 儲存格被格式化為文字

### 3. 空值問題診斷
區分 `null`、`undefined`、空字串 `""` 等不同的空值情況。

**範例**：
- 錯誤訊息：「必填欄位為空」
- 原始資料：`value: "   "` (只有空白)
- 診斷：儲存格有內容但都是空白字元

### 4. 特殊字元問題
識別不可見字元、特殊符號等問題。

**範例**：
- 預期：普通文字
- 原始資料：`value: "測試\n資料"` (包含換行符號)
- 診斷：儲存格內容包含換行符號

## 設計考量

### 1. 效能優化
- 原始資料僅在驗證失敗時收集
- 不影響驗證速度
- 資料結構扁平化，查詢快速

### 2. 記憶體管理
- 只收集有錯誤的列的資料
- 驗證通過後自動釋放
- 使用 React state 管理生命週期

### 3. 使用者體驗
- 明確標示「開發模式」
- 資料以表格呈現，易於閱讀
- 空值以特殊樣式標示
- 支援滾動查看大量欄位

### 4. 安全性
- 資料僅在客戶端瀏覽器中處理
- 不會傳送至伺服器
- 重新整理頁面後清除

## 視覺設計

### 顏色配置
- **背景色**：淡藍色 (`info.lighter`)，區分錯誤訊息
- **標題**：粗體 + 圖示 (AssignmentIcon)
- **內容區**：白色背景，等寬字體顯示
- **空值**：灰色斜體，清楚標示

### 佈局結構
```
┌─────────────────────────────────────────┐
│ 📋 原始 Excel 資料 (開發模式)              │
├─────────────────────────────────────────┤
│ 工作表: Sheet1 | 列號: 4                  │
│                                         │
│ ┌───────────────────────────────────┐   │
│ │ 欄位ID │ 欄位名稱 │ 實際值 │ 類型  │   │
│ ├───────────────────────────────────┤   │
│ │ Land-->│   地號   │ 測試  │ string│   │
│ │ Land-->│ 地號序號 │ 001   │ string│   │
│ │  ...   │   ...   │  ...  │  ...  │   │
│ └───────────────────────────────────┘   │
└─────────────────────────────────────────┘
```

## 未來改進方向

### 1. 篩選功能
- 僅顯示有錯誤的欄位
- 高亮標示錯誤欄位

### 2. 資料匯出
- 將原始資料匯出為 JSON
- 方便進一步分析

### 3. 比對模式
- 並排顯示「預期值」vs「實際值」
- 視覺化差異

### 4. 環境控制
- 生產環境可選擇性關閉
- 透過環境變數控制顯示

## 相關檔案

- `src/pages/pages/ImportDataPage/index.js` - 資料收集邏輯
- `src/pages/pages/ImportDataPage/subComponents/ValidationErrorsEnhanced.js` - UI 顯示
- `docs/ValidationErrorDisplayOptimization.md` - 錯誤顯示優化文檔
- `docs/ViewRowDetailsButtonFix.md` - 查看詳情按鈕修復文檔

## 測試建議

### 測試案例 1：正常資料
1. 上傳包含正確格式的 Excel
2. 驗證應通過，不顯示原始資料

### 測試案例 2：錯誤資料
1. 上傳包含錯誤的 Excel（如欄位順序錯誤）
2. 切換至表格檢視
3. 展開任一錯誤列
4. 應顯示該列的原始資料，包含所有欄位

### 測試案例 3：空值處理
1. 上傳包含空值的 Excel
2. 展開包含空值的列
3. 空值應顯示為「(空值)」並以灰色斜體標示

### 測試案例 4：特殊字元
1. 上傳包含特殊字元（換行、全形、特殊符號）的資料
2. 展開該列
3. 應正確顯示特殊字元

### 測試案例 5：大量欄位
1. 上傳包含所有欄位的完整資料
2. 展開列查看
3. 應可滾動查看所有欄位，不影響效能

## 總結

原始 Excel 資料顯示功能為開發人員提供了強大的除錯工具，讓驗證邏輯更加透明，問題診斷更加快速。透過直觀的表格呈現，開發者可以立即了解 Excel 檔案的實際內容，大幅提升開發效率。
