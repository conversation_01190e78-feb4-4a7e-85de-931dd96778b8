Warning: NLS unused message: QTEST in: com.kms.katalon.constants.globalMessages
Warning: NLS missing message: QTEST_NAME in: com.kms.katalon.constants.globalMessages
2025-08-20 15:46:55.992 INFO  c.k.katalon.core.main.TestSuiteExecutor  - START Test Suites/Mandarin/v1/Gis
2025-08-20 15:46:56.094 INFO  c.k.katalon.core.main.TestSuiteExecutor  - userFullName = Lin Kai Yee
2025-08-20 15:46:56.095 INFO  c.k.katalon.core.main.TestSuiteExecutor  - projectName = katalon
2025-08-20 15:46:56.096 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostName = saw63 - host.docker.internal
2025-08-20 15:46:56.097 INFO  c.k.katalon.core.main.TestSuiteExecutor  - os = Windows 11 64bit
2025-08-20 15:46:56.098 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostAddress = ************
2025-08-20 15:46:56.099 INFO  c.k.katalon.core.main.TestSuiteExecutor  - katalonVersion = ********
2025-08-20 15:46:56.465 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-20 15:46:56.560 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 20, 2025 3:46:58 下午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-20 15:46:58.612 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 28ba1ae67c6c144ea6fc12172d90492f
2025-08-20 15:46:58.614 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-20 15:46:58.615 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-20 15:46:58.615 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-20 15:46:58.627 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-20 15:46:59.230 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 15:46:59.230 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-20 15:46:59.297 DEBUG testcase.GoToGis                         - 1: openBrowser("")
2025-08-20 15:46:59.304 WARN  c.k.k.core.webui.driver.DriverFactory    - A browser is already opened. Closing browser and opening a new one
2025-08-20 15:46:59.561 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-20 15:46:59.565 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 20, 2025 3:47:00 下午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-20 15:47:00.419 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 2bcb0ede66381d7c5dbba5168e4e9d8a
2025-08-20 15:47:00.419 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-20 15:47:00.420 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-20 15:47:00.421 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-20 15:47:00.421 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-20 15:47:00.446 DEBUG testcase.GoToGis                         - 2: navigateToUrl("https://land2.daoyidh.com/Gis")
2025-08-20 15:47:00.855 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-20 15:47:00.864 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 15:47:00.864 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-20 15:47:00.960 DEBUG testcase.LoginWithEmail                  - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_"))
2025-08-20 15:47:02.427 DEBUG testcase.LoginWithEmail                  - 2: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail"))
2025-08-20 15:47:02.975 DEBUG testcase.LoginWithEmail                  - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Email_email"), "<EMAIL>")
2025-08-20 15:47:03.788 DEBUG testcase.LoginWithEmail                  - 4: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Next"))
2025-08-20 15:47:04.077 DEBUG testcase.LoginWithEmail                  - 5: setEncryptedText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Password_password"), "0D41p6Ct1VJ35FCiyaw3Gw==")
2025-08-20 15:47:05.137 DEBUG testcase.LoginWithEmail                  - 6: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign In"))
2025-08-20 15:47:05.427 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-20 15:47:05.429 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 15:47:05.429 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-20 15:47:05.484 DEBUG testcase.SearchTextInGis                 - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button__1"))
2025-08-20 15:47:08.041 DEBUG testcase.SearchTextInGis                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input__mui-3"), "新北勢")
2025-08-20 15:47:09.760 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-20 15:47:09.764 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 15:47:09.764 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-20 15:47:09.844 DEBUG testcase.ChoosePerPage                   - 1: dropdownButton = new com.kms.katalon.core.testobject.TestObject()
2025-08-20 15:47:09.856 DEBUG testcase.ChoosePerPage                   - 2: dropdownButton.addProperty("css", EQUALS, "div[role="button"]")
2025-08-20 15:47:09.867 DEBUG testcase.ChoosePerPage                   - 3: click(dropdownButton)
2025-08-20 15:47:10.262 DEBUG testcase.ChoosePerPage                   - 4: option50 = new com.kms.katalon.core.testobject.TestObject()
2025-08-20 15:47:10.265 DEBUG testcase.ChoosePerPage                   - 5: option50.addProperty("css", EQUALS, "li.MuiMenuItem-root[data-value="50"]")
2025-08-20 15:47:10.269 DEBUG testcase.ChoosePerPage                   - 6: waitForElementVisible(option50, 5)
2025-08-20 15:47:10.727 DEBUG testcase.ChoosePerPage                   - 7: click(option50)
2025-08-20 15:47:11.138 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-20 15:47:11.143 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 15:47:11.144 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-20 15:47:11.197 DEBUG testcase.EditCoordinates                 - 1: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/119-1_twd97_x"), "148485")
2025-08-20 15:47:11.652 DEBUG testcase.EditCoordinates                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/119-1_twd97_y"), "2406567 ")
2025-08-20 15:47:12.142 DEBUG testcase.EditCoordinates                 - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/1261_twd97_x"), "148485")
2025-08-20 15:47:12.568 DEBUG testcase.EditCoordinates                 - 4: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/1261_twd97_y"), "2406567 ")
2025-08-20 15:47:12.981 DEBUG testcase.EditCoordinates                 - 5: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination"))
2025-08-20 15:47:13.429 DEBUG testcase.EditCoordinates                 - 6: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/249_twd97_x"), "148485")
2025-08-20 15:47:13.848 DEBUG testcase.EditCoordinates                 - 7: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/249_twd97_y"), "2406567 ")
2025-08-20 15:47:14.279 DEBUG testcase.EditCoordinates                 - 8: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination_1"))
2025-08-20 15:47:14.726 DEBUG testcase.EditCoordinates                 - 9: verifyElementVisible(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/span_unsave_hint"))
2025-08-20 15:47:14.763 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-20 15:47:14.765 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 15:47:14.766 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SaveAndVerify
2025-08-20 15:47:14.811 DEBUG testcase.SaveAndVerify                   - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_save"))
