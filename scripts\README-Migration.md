# 資料遷移指南

## 問題說明

執行 `migrateUserRoles.js` 時遇到 `PERMISSION_DENIED` 錯誤，因為：
- **前端 Firebase SDK** 受到 Database Rules 限制
- **需要 Admin SDK** 才能批次寫入資料

---

## ✅ 方案 1：使用 Firebase Admin SDK（推薦）

### 步驟 1：安裝 Firebase Admin SDK

```powershell
npm install firebase-admin --save-dev
```

### 步驟 2：取得 Service Account 金鑰

1. 前往 [Firebase Console](https://console.firebase.google.com/)
2. 選擇專案 `land-web-4f1ad`
3. 點擊左側 ⚙️ **專案設定** > **服務帳戶**
4. 點擊 **產生新的私密金鑰**
5. 下載 JSON 檔案
6. 重新命名為 `serviceAccountKey.json`
7. 放在 `scripts/` 資料夾

### 步驟 3：執行遷移腳本

```powershell
cd C:\daoyidh\land-web\scripts
node migrateUserRoles-admin.js
```

### 步驟 4：清理金鑰檔案（安全）

```powershell
# 遷移完成後刪除金鑰檔案
rm serviceAccountKey.json

# 確認 .gitignore 已包含
# scripts/serviceAccountKey.json
```

---

## 🔧 方案 2：臨時開放 Database Rules（不推薦）

> ⚠️ **僅用於測試環境！正式環境請使用方案 1**

### 步驟 1：修改 Firebase Database Rules

1. 前往 Firebase Console
2. Realtime Database > 規則
3. 臨時修改為：

```json
{
  "rules": {
    "users": {
      ".read": "auth != null",
      ".write": "auth != null"
    }
  }
}
```

### 步驟 2：執行原始腳本

```powershell
node migrateUserRoles.js
```

### 步驟 3：恢復安全規則

```json
{
  "rules": {
    "users": {
      "$uid": {
        ".read": "auth.uid === $uid",
        ".write": "auth.uid === $uid"
      }
    }
  }
}
```

---

## 📊 遷移結果預期

成功執行後應該看到：

```
========================================
✅ 遷移完成！
========================================
成功為 13 個使用者新增 roleDev 欄位
其中 8 個使用者使用多選角色格式

建議：
1. 登入系統確認使用者資料正常
2. 測試不同環境下的角色切換
3. 檢查權限控制是否正常運作
```

---

## 🔍 驗證資料

### 方法 1：Firebase Console

1. Realtime Database > 資料
2. 展開 `/users/{uid}`
3. 確認每個使用者都有：
   - `role`: "editor,reader"（原有欄位）
   - `roleDev`: "editor,reader"（新增欄位）

### 方法 2：使用腳本查詢

```javascript
// scripts/verifyMigration.js
const firebase = require('firebase/compat/app');
require('firebase/compat/database');

firebase.initializeApp({
  databaseURL: "https://land-web-4f1ad-default-rtdb.asia-southeast1.firebasedatabase.app"
});

firebase.database().ref('/users').once('value').then(snapshot => {
  const users = snapshot.val();
  let hasRoleDev = 0;
  let missingRoleDev = 0;
  
  Object.entries(users).forEach(([uid, data]) => {
    if (data.roleDev) {
      hasRoleDev++;
    } else {
      missingRoleDev++;
      console.log(`❌ ${data.email} 缺少 roleDev`);
    }
  });
  
  console.log(`✅ 有 roleDev: ${hasRoleDev}`);
  console.log(`❌ 缺少 roleDev: ${missingRoleDev}`);
  process.exit(0);
});
```

---

## ⚠️ 注意事項

### 安全性

- ✅ Service Account 金鑰不可提交到版本控制
- ✅ `.gitignore` 應包含 `scripts/serviceAccountKey.json`
- ✅ 金鑰擁有完整權限，使用後立即刪除

### 資料備份

執行前建議備份：

```bash
# 使用 Firebase CLI
firebase database:get / > backup-$(date +%Y%m%d).json
```

或從 Firebase Console：
1. Realtime Database > 三點選單
2. 匯出 JSON

### 多選角色格式

腳本會保留原始格式：
- ✅ `"admin,editor"` → `roleDev: "admin,editor"`
- ✅ `"developer"` → `roleDev: "developer"`
- ✅ `"anonymous,reader,editor"` → `roleDev: "anonymous,reader,editor"`

---

## 🐛 常見錯誤排查

### 錯誤 1：找不到 Service Account 金鑰

```
❌ 找不到 Service Account 金鑰檔案
```

**解決**：確認 `scripts/serviceAccountKey.json` 存在

### 錯誤 2：Database URL 錯誤

```
Error: INVALID_URL
```

**解決**：確認 `databaseURL` 正確：
```javascript
databaseURL: "https://land-web-4f1ad-default-rtdb.asia-southeast1.firebasedatabase.app"
```

### 錯誤 3：權限不足（Admin SDK）

```
PERMISSION_DENIED with Admin SDK
```

**解決**：
1. 確認使用正確的 Service Account 金鑰
2. 確認金鑰對應的專案正確
3. 重新產生金鑰

---

## 📝 執行檢查清單

- [ ] 已備份 Firebase Realtime Database
- [ ] 已安裝 `firebase-admin` (方案 1)
- [ ] 已下載 Service Account 金鑰 (方案 1)
- [ ] 已將金鑰放在 `scripts/serviceAccountKey.json` (方案 1)
- [ ] 已確認 Firebase 專案正確
- [ ] 執行遷移腳本
- [ ] 驗證資料更新成功
- [ ] 刪除 Service Account 金鑰檔案
- [ ] 測試系統功能正常

---

## 🎯 下一步

遷移完成後：

1. **登入測試**
   - 使用不同角色帳號登入
   - 確認 `currentRole` 和 `currentRoles` 正確

2. **環境切換測試**
   - 切換 `REACT_APP_ENV` 環境變數
   - 重新 build 並測試

3. **權限功能測試**
   - 測試編輯、刪除、匯入等操作
   - 確認 developer 在正式站受限

4. **進行 Phase 2**
   - 開始實作頁面級權限控制
   - EditPage, GisPage, ImportDataPage, AuthorityPage
