import { useState, useCallback } from 'react';

/**
 * usePermissionSnackbar - 管理 PermissionSnackbar 狀態的自定義 Hook
 * 
 * @returns {Object} 包含 snackbar 狀態和控制方法
 * @returns {Object} snackbarState - Snackbar 的狀態物件
 * @returns {boolean} snackbarState.open - 是否顯示
 * @returns {string} snackbarState.message - 訊息內容
 * @returns {string} snackbarState.severity - 嚴重程度
 * @returns {Function} showSnackbar - 顯示 Snackbar 的方法
 * @returns {Function} showError - 顯示錯誤訊息的快捷方法
 * @returns {Function} showWarning - 顯示警告訊息的快捷方法
 * @returns {Function} showInfo - 顯示提示訊息的快捷方法
 * @returns {Function} showSuccess - 顯示成功訊息的快捷方法
 * @returns {Function} hideSnackbar - 隱藏 Snackbar 的方法
 */
const usePermissionSnackbar = () => {
  const [snackbarState, setSnackbarState] = useState({
    open: false,
    message: '',
    severity: 'info',
  });

  const showSnackbar = useCallback((message, severity = 'info') => {
    setSnackbarState({
      open: true,
      message,
      severity,
    });
  }, []);

  const hideSnackbar = useCallback(() => {
    setSnackbarState((prev) => ({
      ...prev,
      open: false,
    }));
  }, []);

  // 快捷方法
  const showError = useCallback((message) => {
    showSnackbar(message, 'error');
  }, [showSnackbar]);

  const showWarning = useCallback((message) => {
    showSnackbar(message, 'warning');
  }, [showSnackbar]);

  const showInfo = useCallback((message) => {
    showSnackbar(message, 'info');
  }, [showSnackbar]);

  const showSuccess = useCallback((message) => {
    showSnackbar(message, 'success');
  }, [showSnackbar]);

  return {
    snackbarState,
    showSnackbar,
    showError,
    showWarning,
    showInfo,
    showSuccess,
    hideSnackbar,
  };
};

export default usePermissionSnackbar;
