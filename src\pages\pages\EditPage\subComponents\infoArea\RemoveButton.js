import React, { useContext } from "react";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

function RemoveButton({ itemType, index }) {
  const [state, dispatch] = useContext(StoreContext);
  const { landMarkInfo, landRightInfo, basicInfo } = state.edit;

  const removeItem = () => {
    if (itemType === "LMK") {
      if (index > -1) {
        const tmpLandMarkInfo = JSON.parse(JSON.stringify(landMarkInfo));
        tmpLandMarkInfo.splice(index, 1);
        dispatch({
          type: Act.SET_LANDMARKINFO,
          payload: tmpLandMarkInfo,
        });
      }
    } else if (itemType === "LRT") {
      if (index > -1) {
        const tmpLandRightInfo = JSON.parse(JSON.stringify(landRightInfo));
        tmpLandRightInfo.splice(index, 1);
        dispatch({
          type: Act.SET_LANDRIGHTINFO,
          payload: tmpLandRightInfo,
        });
      }
    }
  };

  return (
    <RemoveCircleOutlineIcon
      color="error"
      className="icon"
      onClick={removeItem}
    />
  );
}

export default RemoveButton;
