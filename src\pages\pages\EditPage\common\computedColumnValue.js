import proj4 from "proj4";

export const computedColumnKey = {
  wgs84_lat: "wgs84_lat",
  wgs84_long: "wgs84_long",
};

export const baseColumnKey = {
  twd97_x: "twd97_x",
  twd97_y: "twd97_y",
};

export const convertKey = {
  [baseColumnKey.twd97_x]: computedColumnKey.wgs84_long, // X -> 經度
  [baseColumnKey.twd97_y]: computedColumnKey.wgs84_lat, // Y -> 緯度
};

export const REGION_MAP = {
  121: "121",
  119: "119",
};

// 定義座標系統
proj4.defs(
  "TWD97",
  "+proj=tmerc +lat_0=0 +lon_0=121 +k=0.9999 +x_0=250000 +y_0=0 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=m +no_defs"
);

proj4.defs(
  "TWD97_PKM",
  "+proj=tmerc +lat_0=0 +lon_0=119 +k=0.9999 +x_0=250000 +y_0=0 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=m +no_defs"
);

// 座標轉換函數
const actualCoordinateConversion = (x, y, isPKM = false) => {
  try {
    const sourceCRS = isPKM ? "TWD97_PKM" : "TWD97";
    const [lng, lat] = proj4(sourceCRS, "WGS84", [
      parseFloat(x),
      parseFloat(y),
    ]);

    return {
      lat: String(lat.toFixed(8)),
      long: String(lng.toFixed(8)),
    };
  } catch (error) {
    console.error("座標轉換錯誤:", error);
    return null;
  }
};

const isValidCoordinate = (value) =>
  value !== null &&
  value !== undefined &&
  value !== "" &&
  !Number.isNaN(parseFloat(value));

// 工具函數：從 basicInfo 中取得座標值
const getCoordinateFromBasicInfo = (basicInfo, predicate) => {
  const item = basicInfo.find((element) => element.predicate === predicate);
  return item ? item.value : null;
};

// 工具函數：更新 basicInfo 中的欄位值
const updateBasicInfoField = (basicInfo, predicate, value) => {
  const item = basicInfo.find((element) => element.predicate === predicate);
  if (item) {
    item.value = value;
  }
};

const boundIngBoxex = {
  twMainland: {
    minX: 148485,
    maxX: 352000,
    minY: 2406567,
    maxY: 2799500,
  },
  // penghu: {
  //   minX: 170025,
  //   maxX: 223000,
  //   minY: 2566000,
  //   maxY: 2631000,
  // },
  // kinmen: {
  //   minX: 80000,
  //   maxX: 105000,
  //   minY: 2697000,
  //   maxY: 2716000,
  // },
  // matsu: {
  //   minX: 297000,
  //   maxX: 301000,
  //   minY: 2893000,
  //   maxY: 2920000,
  // },
  all: {
    minX: 148485,
    maxX: 352000,
    minY: 2406567,
    maxY: 2799500,
  },
};

// 工具函數：檢查座標是否在台灣本島範圍
const isInTaiwanMainland = (x, y) =>
  x >= boundIngBoxex.twMainland.minX &&
  x <= boundIngBoxex.twMainland.maxX &&
  y >= boundIngBoxex.twMainland.minY &&
  y <= boundIngBoxex.twMainland.maxY;

// 工具函數：檢查座標是否在澎湖範圍
// const isInPenghu = (x, y) =>
//   x >= boundIngBoxex.penghu.minX &&
//   x <= boundIngBoxex.penghu.maxX &&
//   y >= boundIngBoxex.penghu.minY &&
//   y <= boundIngBoxex.penghu.maxY;

// 工具函數：檢查座標是否在金門範圍
// const isInKinmen = (x, y) =>
//   x >= boundIngBoxex.kinmen.minX &&
//   x <= boundIngBoxex.kinmen.maxX &&
//   y >= boundIngBoxex.kinmen.minY &&
//   y <= boundIngBoxex.kinmen.maxY;

// 工具函數：檢查座標是否在馬祖範圍
// const isInMatsu = (x, y) =>
//   x >= boundIngBoxex.matsu.minX &&
//   x <= boundIngBoxex.matsu.maxX &&
//   y >= boundIngBoxex.matsu.minY &&
//   y <= boundIngBoxex.matsu.maxY;

// 工具函數：自動判斷是否為澎金馬地區
const autoDetectPKM = (x, y) => {
  if (!isValidCoordinate(x) || !isValidCoordinate(y)) return false;
  // if (isInPenghu(x, y)) return true;
  // if (isInKinmen(x, y)) return true;
  // if (isInMatsu(x, y)) return true;
  return false;
};

export {
  actualCoordinateConversion,
  getCoordinateFromBasicInfo,
  updateBasicInfoField,
  autoDetectPKM,
  isInTaiwanMainland,
  isValidCoordinate,
  boundIngBoxex,
};
