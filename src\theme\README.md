# 主題系統使用指南

## 📋 概述

這個專案現在有統一的主題系統，確保 Material-UI 組件和自定義樣式使用一致的配色方案。

## 🎨 色彩系統

### 主要顏色
- **Primary**: `#008cba` (藍色) - 主要操作按鈕、連結
- **Secondary**: `#1abc9c` (青色) - 次要操作、強調元素
- **Danger/Error**: `#f44336` (紅色) - 錯誤、刪除操作
- **Warning**: `#ff9800` (橙色) - 警告訊息
- **Info**: `#2196f3` (亮藍) - 資訊提示
- **Success**: `#4caf50` (綠色) - 成功狀態

### 背景顏色
- **Surface**: `#ffffff` - 卡片、面板背景
- **Surface Alt**: `#f5f7fa` - 替代背景
- **Background**: `#eef3f7` - 頁面背景

### 文字顏色
- **Text Primary**: `#1f2a37` - 主要文字
- **Text Secondary**: `#4b5563` - 次要文字
- **Text Muted**: `#6b7280` - 弱化文字
- **Text Disabled**: `#9ca3af` - 禁用文字

### 邊框顏色
- **Border**: `#d5e3eb` - 一般邊框
- **Border Light**: `#e0e7ef` - 淺色邊框
- **Border Strong**: `#b7c9d6` - 強調邊框

## 🔧 使用方式

### 1. Material-UI 組件
```jsx
// ✅ 推薦：使用 theme 中定義的顏色
<Button variant="contained" color="primary">主要按鈕</Button>
<Button variant="outlined" color="secondary">次要按鈕</Button>
<Button variant="text" color="secondary">文字按鈕</Button>

// ❌ 避免：硬編碼顏色
<Button sx={{ color: '#9c27b0' }}>按鈕</Button>
```

### 2. CSS 變數（推薦）
```scss
// ✅ 推薦：使用 CSS 變數
.my-component {
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

// ❌ 避免：硬編碼顏色值
.my-component {
  background-color: #ffffff;
  color: #1f2a37;
  border: 1px solid #d5e3eb;
}
```

### 3. SCSS 變數
```scss
@use "../../../scss/common";

.my-component {
  background-color: common.$color-surface;
  color: common.$color-text-primary;
  border: 1px solid common.$color-border;
}
```

### 4. Material-UI sx 屬性
```jsx
// ✅ 推薦：使用 theme 顏色
<Box sx={{ 
  bgcolor: 'background.paper',
  color: 'text.primary',
  borderColor: 'divider'
}}>
  內容
</Box>

// ✅ 也可以：使用 CSS 變數
<Box sx={{ 
  bgcolor: 'var(--color-surface)',
  color: 'var(--color-text-primary)'
}}>
  內容
</Box>
```

## 🛠️ 通用 CSS 類別

### 文字顏色
```html
<div class="text-primary">主要文字</div>
<div class="text-secondary">次要文字</div>
<div class="text-muted">弱化文字</div>
<div class="text-disabled">禁用文字</div>
```

### 背景顏色
```html
<div class="bg-primary">主要背景</div>
<div class="bg-secondary">次要背景</div>
<div class="bg-surface">表面背景</div>
<div class="bg-surface-alt">替代表面背景</div>
```

### 邊框顏色
```html
<div class="border border-primary">主要邊框</div>
<div class="border border-secondary">次要邊框</div>
<div class="border border-light">淺色邊框</div>
```

### 卡片樣式
```html
<div class="card">基本卡片</div>
<div class="card-hover">可懸停卡片</div>
```

## 🎯 常見問題解決

### 問題：Button color="secondary" 顯示紫色 (#9c27b0)
**解決方案：**
```jsx
// 現在 secondary 已經映射到青色系 (#1abc9c)
<Button color="secondary">次要按鈕</Button> // 現在是青色

// 或者明確使用 CSS 變數
<Button sx={{ color: 'var(--color-secondary)' }}>次要按鈕</Button>
```

### 問題：需要自定義顏色
**解決方案：**
```jsx
// 方案 1：在 theme/index.js 中添加新顏色
// 方案 2：使用 CSS 變數擴展 common.scss
// 方案 3：使用 sx 屬性但保持一致性
<Button sx={{ 
  color: 'var(--color-accent)',  // 使用已定義的變數
  '&:hover': { color: 'var(--color-accent-hover)' }
}}>
  自定義按鈕
</Button>
```

### 問題：Dark Mode 支援
主題系統已包含 Dark Mode 支援：
```html
<!-- 切換到 Dark Mode -->
<html data-theme="dark">
```

## 📱 響應式設計

使用 SCSS 斷點：
```scss
@use "../../../scss/common";

.my-component {
  @media (max-width: map.get(common.$breakpoints, "md")) {
    // 平板以下樣式
  }
  
  @media (max-width: map.get(common.$breakpoints, "sm")) {
    // 手機樣式
  }
}
```

## ✅ 最佳實踐

1. **優先使用 Material-UI 主題顏色**：`color="primary"`, `color="secondary"`
2. **使用 CSS 變數**：`var(--color-primary)` 而非硬編碼
3. **保持一致性**：同類型元素使用相同的顏色系統
4. **考慮 Dark Mode**：使用主題系統自動支援暗色模式
5. **測試響應式**：確保在不同裝置上的顯示效果

## 🔄 遷移指南

如果發現使用了硬編碼顏色，請按以下步驟遷移：

1. 識別顏色用途（主要/次要/錯誤/背景等）
2. 找到對應的主題顏色或 CSS 變數
3. 替換硬編碼值
4. 測試在 Light/Dark 模式下的效果

例如：
```jsx
// ❌ 舊寫法
<Button sx={{ color: '#9c27b0' }}>

// ✅ 新寫法
<Button color="secondary">
// 或者
<Button sx={{ color: 'var(--color-secondary)' }}>
```