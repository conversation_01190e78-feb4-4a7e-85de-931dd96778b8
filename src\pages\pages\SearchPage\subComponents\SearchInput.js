import React, { useContext, useEffect } from "react";

import SearchIcon from "@mui/icons-material/Search";
import { Box, TextField, InputAdornment } from "@mui/material";
import { StoreContext } from "../../../../store/StoreProvider";
import CustomDebounce from "../../../../hooks/CustomDebounce";
import { isEmpty } from "../../../../utils";
import { Api, fetchPOSTData } from "../../../../api/land/Api";
import Act from "../../../../store/actions";

function SearchInput() {
  const [state, dispatch] = useContext(StoreContext);
  const { searchColList, keyWord } = state.search;
  const debounceValue = CustomDebounce(keyWord, 400);
  const hasActiveColumns = searchColList.some((group) =>
    group.items.some((item) => item.select)
  );

  useEffect(() => {
    const trimmedKeyword = (debounceValue || "").trim();
    if (trimmedKeyword === "" || !hasActiveColumns) {
      dispatch({
        type: Act.SET_RESULTLIST,
        payload: [],
      });
      dispatch({
        type: Act.SET_SEARCHDURATION,
        payload: 0,
      });
      dispatch({
        type: Act.SET_SEARCHLOADING,
        payload: false,
      });
    }
  }, [debounceValue, hasActiveColumns, dispatch]);

  const runSearch = () => {
    const activeColumns = searchColList.flatMap((group) =>
      group.items.filter((item) => item.select)
    );
    const searchColStr = activeColumns.map((col) => col.value).join();
    const trimmedKeyword = (keyWord || "").trim();

    if (isEmpty(trimmedKeyword) || isEmpty(searchColStr)) {
      dispatch({
        type: Act.SET_RESULTLIST,
        payload: [],
      });
      return;
    }

    const apiStr = Api.getPostSearchInfo();
    dispatch({
      type: Act.SET_SEARCHLOADING,
      payload: true,
    });
    fetchPOSTData({
      apiStr,
      entry: {
        ids: searchColStr,
        keyword: trimmedKeyword,
      },
    }).then((result) => {
      dispatch({
        type: Act.SET_RESULTLIST,
        payload: result.data,
      });
      dispatch({
        type: Act.SET_SEARCHDURATION,
        payload: result.durationSS,
      });
      dispatch({
        type: Act.SET_SEARCHLOADING,
        payload: false,
      });
    });
  };

  return (
    <Box className="SearchInput">
      <TextField
        fullWidth
        label="搜尋"
        InputProps={{
          endAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
        variant="outlined"
        value={keyWord}
        onChange={(event) => {
          dispatch({
            type: Act.SET_KEYWORD,
            payload: event.target.value,
          });
        }}
        onKeyDown={(event) => {
          if (event.key === "Enter") {
            event.preventDefault();
            runSearch();
          }
        }}
        inputProps={{ enterKeyHint: "search" }}
        helperText="輸入關鍵字後按 Enter 進行搜尋"
      />
    </Box>
  );
}

export default SearchInput;
