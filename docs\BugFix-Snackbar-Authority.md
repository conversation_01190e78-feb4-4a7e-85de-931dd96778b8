# Bug 修復報告 - Snackbar 顯示問題 & AuthorityPage 權限漏洞

**修復日期**: 2025-10-02  
**問題編號**: #001, #002

---

## 🐛 Bug #001 - EditPage Snackbar 文字顏色問題

### 問題描述
在 `/Edit` 頁面，Snackbar 顯示的文字是白色的，在某些背景下看不見。而 `/Gis` 頁面的 Snackbar 顯示正常。

### 根本原因
- **EditPage**: 使用 `PermissionSnackbar` 組件，`variant="filled"` 搭配強制白色文字
- **GisPage**: 使用原生 MUI Alert，`variant="standard"`（預設），無自定義顏色

問題在於 PermissionSnackbar 使用 `variant="filled"` 並強制設定 `color: '#fff'`，這在某些主題配置下會導致白色文字在白色背景上不可見。

### 解決方案

**修改檔案**: `src/Component/PermissionSnackbar/index.js`

**Before**:
```javascript
<Alert
  severity={severity}
  variant="filled"  // filled variant 可能背景色不一致
  sx={{ 
    color: '#fff',  // 強制白色文字
    '& .MuiAlert-message': {
      color: '#fff',
    },
    // ...
  }}
>
```

**After**:
```javascript
<Alert
  severity={severity}
  variant="standard"  // 改用 standard variant
  sx={{ 
    boxShadow: 3,  // 加入陰影提高可見度
    '& .MuiAlert-message': {
      whiteSpace: 'pre-line',
      width: '100%',
    }
  }}
>
```

### 修改內容

1. 將 `variant="filled"` 改為 `variant="standard"`
2. 移除所有強制白色文字的 CSS
3. 加入 `boxShadow: 3` 提高可見度
4. 讓 MUI 根據 severity 自動配置顏色

### 視覺效果對比

| Variant | 背景 | 文字 | 圖示 | 邊框 |
|---------|------|------|------|------|
| **filled** (舊) | 填滿色 | 白色（強制） | 白色 | 無 |
| **standard** (新) | 淺色 | 深色（自動） | 對應色 | 有 |

### 優點

- ✅ 文字顏色自動適配，保證可讀性
- ✅ 與 GisPage 的 Snackbar 視覺一致
- ✅ 支援淺色/深色主題自動切換
- ✅ 維持 4 種 severity 的視覺區分

---

## 🐛 Bug #002 - AuthorityPage 待授權分頁權限漏洞

### 問題描述
在 `/Authority` 頁面的 "DASHBOARD權限" Tab 中：
- **已授權分頁**: ✅ 有權限管控（非 Admin 無法操作）
- **待授權分頁**: ❌ 沒有權限管控（所有人都可以操作）

這造成非 Admin 使用者可以在「待授權」分頁授權使用者，繞過權限檢查。

### 安全風險

🔴 **High**: 非授權使用者可以：
1. 將待授權使用者添加 reader 角色
2. 修改 Firebase 資料庫
3. 繞過 Admin 權限檢查

### 根本原因

**ValidUser.js** (已授權):
```javascript
✅ import { canManageUsers } from "../../../../utils/permissionUtils";
✅ const hasManagePermission = canManageUsers(currentUser);
✅ disabled={!hasManagePermission}
```

**InvalidUser.js** (待授權):
```javascript
❌ 沒有 import canManageUsers
❌ 沒有權限檢查
❌ Checkbox 沒有 disabled 控制
```

### 解決方案

**修改檔案**: `src/pages/pages/AuthorityPage/subComponents/InvalidUser.js`

#### 1. 加入權限檢查 import

```javascript
import { canManageUsers, getPermissionMessage, OPERATION } from "../../../../utils/permissionUtils";
```

#### 2. 初始化權限狀態

```javascript
const [state, dispatch] = useContext(StoreContext);
const { userInfo, roles } = state.authority;
const currentUser = state.user;  // 新增
const hasManagePermission = canManageUsers(currentUser);  // 新增
```

#### 3. handleClick 加入權限檢查

```javascript
const handleClick = (user) => {
    // 權限檢查
    if (!hasManagePermission) {
        alert(getPermissionMessage(currentUser, OPERATION.MANAGE_USERS));
        return;
    }
    
    // 原有邏輯...
    const tmpUserInfo = JSON.parse(JSON.stringify(userInfo));
    // ...
}
```

#### 4. Checkbox 加入 disabled 屬性

```javascript
<Checkbox 
    onClick={() => handleClick(user)}
    disabled={!hasManagePermission}  // 新增
/>
```

### 修改對比

| 項目 | Before | After |
|------|--------|-------|
| 權限檢查 | ❌ 無 | ✅ canManageUsers() |
| Checkbox disabled | ❌ 無 | ✅ 根據權限 |
| 點擊提示 | ❌ 無 | ✅ alert 權限訊息 |
| 與 ValidUser 一致性 | ❌ 不一致 | ✅ 一致 |

### 防護效果

**Developer 在正式站**:
- Checkbox 顯示為 disabled 狀態（灰色，無法勾選）
- 點擊時顯示權限錯誤訊息
- 無法執行 updateUser() 操作
- 無法修改 Firebase 資料

**Admin**:
- Checkbox 正常可用
- 可以授權待授權使用者
- 功能完全正常

---

## 📊 修改統計

### Bug #001 - Snackbar 顏色
- **修改檔案**: 1
- **修改行數**: 15 行
- **測試頁面**: EditPage, GisPage

### Bug #002 - AuthorityPage 權限
- **修改檔案**: 1
- **新增 import**: 1 行
- **新增邏輯**: 10 行
- **修改 JSX**: 3 行
- **安全等級**: 🔴 High → ✅ Fixed

### 總計
- **修改檔案**: 2
- **新增/修改代碼**: ~28 行
- **修復漏洞**: 1 個嚴重權限漏洞

---

## ✅ 測試檢查清單

### Snackbar 顯示測試

**EditPage**:
- [ ] Developer 在正式站點擊「編輯」按鈕
- [ ] 確認 Snackbar 顯示
- [ ] 確認文字清楚可見（不是白色）
- [ ] 確認錯誤訊息正確
- [ ] 確認 4 秒後自動消失

**GisPage**:
- [ ] Developer 在正式站點擊「儲存變更」按鈕
- [ ] 確認 Snackbar 顯示
- [ ] 確認文字清楚可見
- [ ] 確認與 EditPage 視覺一致

### AuthorityPage 權限測試

**Admin 測試**:
- [ ] 進入 AuthorityPage > DASHBOARD權限
- [ ] 切換到「已授權」分頁，確認可以操作
- [ ] 切換到「待授權」分頁，確認可以操作
- [ ] Checkbox 正常可勾選

**Developer 測試**:
- [ ] 以 Developer 角色登入
- [ ] 進入 AuthorityPage > DASHBOARD權限
- [ ] 切換到「已授權」分頁
  - [ ] Checkbox 顯示 disabled（灰色）
  - [ ] 點擊時顯示權限錯誤
- [ ] 切換到「待授權」分頁
  - [ ] Checkbox 顯示 disabled（灰色）
  - [ ] 點擊時顯示權限錯誤訊息
  - [ ] 確認無法授權使用者

**Editor 測試**:
- [ ] 以 Editor 角色登入
- [ ] 進入 AuthorityPage > DASHBOARD權限
- [ ] 確認「已授權」和「待授權」分頁都無法操作

---

## 🔒 安全性改進

### Before
```
Developer 在正式站:
  EditPage 編輯按鈕 -> ✅ 有權限檢查
  GisPage 儲存按鈕 -> ✅ 有權限檢查
  AuthorityPage 已授權 -> ✅ 有權限檢查
  AuthorityPage 待授權 -> ❌ 沒有權限檢查 (漏洞)
```

### After
```
Developer 在正式站:
  EditPage 編輯按鈕 -> ✅ 有權限檢查
  GisPage 儲存按鈕 -> ✅ 有權限檢查
  AuthorityPage 已授權 -> ✅ 有權限檢查
  AuthorityPage 待授權 -> ✅ 有權限檢查 (已修復)
```

---

## 📝 後續建議

### 1. Snackbar 統一化
考慮將所有頁面的 alert() 都替換為 Snackbar：
- [ ] ValidUser.js 的權限 alert
- [ ] InvalidUser.js 的權限 alert
- [ ] 其他頁面的 alert 調用

### 2. 權限檢查審查
建議審查所有頁面的權限控制：
```bash
# 搜尋可能遺漏權限檢查的地方
grep -r "onClick.*handleClick" src/pages/pages/AuthorityPage/
grep -r "updateUser" src/pages/pages/AuthorityPage/
```

### 3. 自動化測試
考慮加入以下測試：
- 權限檢查單元測試
- E2E 測試（Katalon）驗證權限控制
- 安全性測試檢查權限繞過

### 4. 程式碼審查準則
未來新增功能時：
- ✅ 所有資料變更操作都需要權限檢查
- ✅ UI 控制（disabled）+ 邏輯檢查（if）雙重防護
- ✅ 子組件權限檢查與父組件一致

---

## 🎉 總結

### 已修復問題
1. ✅ **Snackbar 文字可見性**: 從 filled variant 改為 standard variant
2. ✅ **AuthorityPage 權限漏洞**: InvalidUser 加入完整權限檢查

### 安全性提升
- 🔒 關閉 1 個嚴重權限繞過漏洞
- 🔒 「待授權」分頁現在與「已授權」分頁權限一致
- 🔒 非 Admin 使用者無法授權新使用者

### 使用者體驗改進
- 🎨 Snackbar 文字在所有主題下都清楚可見
- 🎨 EditPage 和 GisPage 的 Snackbar 視覺一致
- 🎨 權限不足時有明確的視覺反饋（disabled + alert）

---

**修復狀態**: ✅ 全部完成  
**需要測試**: 是  
**下一步**: 執行完整的權限測試檢查清單
