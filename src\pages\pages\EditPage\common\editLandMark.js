import { checkLandMarkInfo } from "./index";
import { isEmpty } from "../../../../utils";
import {
  Api,
  createOntoData,
  deleteOntoData,
  updateOntoData,
} from "../../../../api/land/Api";
import { historyType, recordHistory } from "./produceHistory";
import evtType from "../../../../utils/evtType";

const deleteLandMarkInfo = (allDeleteLandMarkID, landId) => {
  allDeleteLandMarkID.forEach((id) => {
    const entry = {
      graph: "south",
      classType: "Land",
      srcId: landId,
      value: {
        hasEvent: {
          srcId: id,
          classType: evtType.LandMark,
          value: {},
        },
      },
    };
    // console.log("entry ", entry);
    deleteOntoData(Api.restfulCRUD(), entry);
  });
};

const createLandMarkInfo = (newData, landId) => {
  if (isEmpty(newData)) return;
  newData.forEach((data, index) => {
    const srcValue = data
      .filter((item) => item.value.length !== 0)
      .reduce((cur, next) => {
        if (next.predicate === "cause") {
          return { ...cur, [next.predicate]: next.value.join("，") };
        }
        return { ...cur, [next.predicate]: next.value };
      }, {});

    // create LandMarkEvent
    const date = new Date();
    const entry = {
      graph: "south",
      classType: "Land",
      srcId: landId,
      value: {
        hasEvent: {
          classType: evtType.LandMark,
          srcId: "",
          value: {
            ...srcValue,
            label: `${landId}-${Date.parse(date)}-${index}`,
          },
        },
      },
    };

    // console.log("entry ", entry);
    createOntoData(Api.restfulCRUD(), entry);
  });
};

export const updateLandMarkInfo = (preValue, modifyValue) => {
  if (isEmpty(preValue) || isEmpty(modifyValue)) return;

  // 找出所有landMark ID
  let LMIDs = preValue.map((element) => element.landMarkId);
  LMIDs = LMIDs.filter((item, pos) => LMIDs.indexOf(item) === pos);

  LMIDs.forEach((LMId) => {
    // 從preValue和modifyValue找出相對應的landMarkId
    const tmpPreValue = preValue.filter(
      (element) => element.landMarkId === LMId
    );
    const tmpModifyValue = modifyValue.filter(
      (element) => element.landMarkId === LMId
    );

    const srcValue = tmpPreValue
      .filter((item) => item.value.length !== 0)
      .reduce((cur, next) => {
        if (next.predicate === "cause") {
          return { ...cur, [next.predicate]: next.value.join("，") };
        }
        return { ...cur, [next.predicate]: next.value };
      }, {});

    const dstValue = tmpModifyValue
      .filter((item) => item.value.length !== 0)
      .reduce((cur, next) => {
        if (next.predicate === "cause") {
          return { ...cur, [next.predicate]: next.value.join("，") };
        }
        return { ...cur, [next.predicate]: next.value };
      }, {});

    const entrySrc = {
      graph: "south",
      classType: evtType.LandMark,
      srcId: LMId,
      value: srcValue,
    };

    const entryDst = {
      graph: "south",
      classType: evtType.LandMark,
      srcId: LMId,
      value: dstValue,
    };
    // console.log(entrySrc, entryDst);
    updateOntoData(Api.restfulCRUD(), entrySrc, entryDst, -1, 0);
  });
};

export const compareLandMarkInfo = async (edit) => {
  const { landMarkInfo, basicInfo } = edit;
  let landMarkInfoRecord = "";

  const { landId } = basicInfo.find((element) => element.landId);
  const latestLandMarkInfo = await checkLandMarkInfo(edit);
  // console.log("latestLandMarkInfo ", latestLandMarkInfo);
  // console.log("landMarkInfo ", landMarkInfo);
  // 找出create item
  const latestLandMarkNumber = latestLandMarkInfo.filter(
    (item) => item.predicate === "landMarkNumber"
  );
  const allCreateData = landMarkInfo.filter((element) => {
    // 找每個element的landMarkNumber value
    const findLandMarkId = element.find(
      (item) => item.predicate === "landMarkNumber"
    ).landMarkId;
    return !latestLandMarkNumber.find(
      (item) => item.landMarkId === findLandMarkId
    );
  });

  if (!isEmpty(allCreateData)) {
    // 清空新增資料的landMarkId
    const newData = allCreateData.map((info) =>
      info.map((data) => ({ ...data, landMarkId: "" }))
    );

    let tmpRecord = "";
    tmpRecord = recordHistory(newData, [], historyType.createLandMarkInfo);
    if (tmpRecord) {
      landMarkInfoRecord += tmpRecord;
      createLandMarkInfo(newData, landId);
    }
  }

  // 找出delete item
  const latestLMIDs = latestLandMarkInfo.reduce((acc, { landMarkId }) => {
    acc[landMarkId] = landMarkId;
    return acc;
  }, {});

  const allLatestEvtId = Object.keys(latestLMIDs);
  const landMarkNumbers = landMarkInfo.reduce((acc, curArr) => {
    const findObj = curArr.find((item) => item.landMarkId);
    if (findObj) {
      const { landMarkId } = findObj;
      acc[landMarkId] = landMarkId;
    }
    return acc;
  }, {});
  const allDeleteLandMarkID = allLatestEvtId.filter(
    (key) => !Object.keys(landMarkNumbers).includes(key)
  );

  if (!isEmpty(allDeleteLandMarkID)) {
    let tmpRecord = "";
    tmpRecord = recordHistory(
      allDeleteLandMarkID,
      [],
      historyType.deleteLandMarkInfo,
      latestLandMarkInfo
    );
    if (tmpRecord) {
      landMarkInfoRecord += tmpRecord;
      deleteLandMarkInfo(allDeleteLandMarkID, landId);
    }
  }

  // 找出現在畫面上與資料庫皆存在的landMark
  const allUpdateID = allLatestEvtId.filter((key) =>
    Object.keys(landMarkNumbers).includes(key)
  );
  const allUpdateData = landMarkInfo.filter((arrEl) => {
    const findObj = arrEl.find((item) => item.landMarkId);
    if (findObj) {
      const { landMarkId } = findObj;
      return allUpdateID.includes(landMarkId);
    }
    return false;
  });

  if (!isEmpty(allUpdateData)) {
    const preColumn = [];
    const modifyColumn = [];
    allUpdateData.forEach((info) => {
      info.forEach((data) => {
        const findObj = latestLandMarkInfo.find(
          (element) =>
            element.landMarkId === data.landMarkId &&
            element.predicate === data.predicate
        );
        if (findObj) {
          let tmpValue = data.value;
          if (findObj.predicate === "cause") {
            tmpValue = data.value.join("，");
          }
          if (findObj.value !== tmpValue) {
            //
            if (findObj.predicate === "cause") {
              findObj.value = findObj.value.split("，");
            }
            preColumn.push(findObj);
            modifyColumn.push(data);
          }
        } else if (data.predicate === "cause") {
          if (data.value.length !== 0) {
            const emptyData = { ...data };
            emptyData.value = [];
            preColumn.push(emptyData);
            modifyColumn.push(data);
          }
        } else if (data.value !== "") {
          const emptyData = { ...data };
          emptyData.value = "";
          preColumn.push(emptyData);
          modifyColumn.push(data);
        }
      });
    });

    let tmpRecord = "";
    tmpRecord += recordHistory(
      preColumn,
      modifyColumn,
      historyType.updateLandMarkInfo,
      allUpdateData
    );
    // console.log(preColumn, modifyColumn);
    if (tmpRecord) {
      landMarkInfoRecord += tmpRecord;
      updateLandMarkInfo(preColumn, modifyColumn);
    }
  }
  return landMarkInfoRecord;
};
