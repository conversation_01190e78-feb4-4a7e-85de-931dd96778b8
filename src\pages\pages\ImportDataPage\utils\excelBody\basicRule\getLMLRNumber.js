import { specRow } from "../../../config";

/**
 * get landMark and landRights number
 * */
const getLMLRNumber = (cell, worksheet) => {
  const headerRow = worksheet.getRow(specRow.idIdx).values; // first row
  const row = worksheet.getRow(cell.fullAddress.row); // current row

  // 要特別檢查的欄位
  return [
    "Land-->hasEvent-->LandMark-->landMarkNumber",
    "Land-->hasEvent-->LandRights-->landRightsNumber",
  ].map((val) => {
    const idx = headerRow.findIndex((el) => el === val);
    if (idx !== -1) {
      return row.values[idx] ? row.values[idx].toString() : "";
    }
    return "-1";
  });
};

export default getLMLRNumber;
