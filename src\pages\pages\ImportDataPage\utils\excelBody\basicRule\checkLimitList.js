import { specRow, splitTag } from "../../../config";

/**
 * 檢查說明: 檢查資料在有限清單內
 * */
const checkLimitList = (cell, worksheet, checkList = []) => {
  const colLabel = worksheet
    .getRow(specRow.labelIdx)
    .getCell(cell.fullAddress.col).value;

  let tmpResStr = "";

  if (cell.value) {
    let reason = "";
    if (typeof cell.value === "string") {
      const multiVal = cell.value.split(splitTag);
      if (cell.value && !multiVal.every((val) => checkList.includes(val))) {
        reason = `內容錯誤，僅能填寫下列選項: ${checkList}`;
      }
    } else if (!checkList.includes(cell.value)) {
      reason = `內容錯誤，僅能填寫下列選項: ${checkList}`;
    }

    if (reason) {
      tmpResStr += `${cell.address}, [${cell.value}], 欄位:${colLabel}，${reason}。\n`;
    }
  }

  return tmpResStr;
};

export default checkLimitList;
