import React, { useContext } from 'react';

// material ui
import { Box, FormControl, FormLabel, RadioGroup, FormControlLabel, Radio } from '@mui/material';
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

function DateTypeSelect() {
    const [, dispatch] = useContext(StoreContext);
    const selectDateType = [
        { value: "single", label: "單日" },
        { value: "multiple", label: "多日" }
    ];

    const handleChange = (event) => {
        dispatch({
            type: Act.SET_SELECTDATETYPE,
            payload: event.target.value
        });
    }

    return (
        <Box sx={{ width: '100%' }}>
            <FormControl component="fieldset">
                <FormLabel 
                    component="legend"
                    sx={{
                        color: 'text.primary',
                        fontWeight: 600,
                        fontSize: '1.1rem',
                        mb: 1,
                        '&.Mui-focused': {
                            color: 'primary.main',
                        },
                    }}
                >
                    選擇搜尋日期範圍方式
                </FormLabel>
                <RadioGroup
                    row
                    defaultValue="single"
                    sx={{
                        mt: 1,
                        '& .MuiFormControlLabel-root': {
                            marginRight: 3,
                        },
                    }}
                >
                    {selectDateType.map((obj, index) => (
                        <FormControlLabel
                            key={index}
                            value={obj.value}
                            control={
                                <Radio
                                    onChange={handleChange}
                                    sx={{
                                        color: 'primary.main',
                                        '&.Mui-checked': {
                                            color: 'primary.main',
                                        },
                                    }}
                                />
                            }
                            label={obj.label}
                            sx={{
                                '& .MuiFormControlLabel-label': {
                                    color: 'text.primary',
                                    fontWeight: 500,
                                    fontSize: '0.95rem',
                                },
                            }}
                        />
                    ))}
                </RadioGroup>
            </FormControl>
        </Box>
    );
}

export default DateTypeSelect;