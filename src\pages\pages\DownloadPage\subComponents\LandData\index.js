import React, { useContext, useEffect, useState } from "react";

// material ui
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Divider from "@mui/material/Divider";

// components
import LandTypeSelect from "./LandTypeSelect";
import DataFormSelect from "./DataFormSelect";
import DownLoadBtn from "./DownLoadBtn";
import InputColumn from "./InputColumn";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";
import { Api, readOntoData } from "../../../../../api/land/Api";
import { isEmpty } from "../../../../../utils";
import Act from "../../../../../store/actions";

function LandData() {
  const [state, dispatch] = useContext(StoreContext);
  const { landType } = state.download;
  const [showInput, setShowInput] = useState(false);

  useEffect(() => {
    const apiStr = Api.getLandEvtTypes();
    readOntoData(apiStr).then((res) => {
      const tmpLandType = res.data.map((el) => ({ ...el, select: false }));
      dispatch({
        type: Act.SET_LANDTYPE,
        payload: tmpLandType,
      });
    });
  }, []);

  useEffect(() => {
    if (isEmpty(landType)) return;
    const check = landType.some((el) => el.select);
    setShowInput(check);
  }, [landType]);

  return (
    <Box 
      className="LandData"
      sx={{
        '& .MuiTextField-root': {
          borderRadius: 2,
        },
        '& .MuiInputLabel-root': {
          color: 'text.secondary',
        },
      }}
    >
      {/* 第一段：土地類型選擇 */}
      <Grid container px={2} py={{ xs: 1, md: 2 }} className="topArea">
        <Grid item xs={12}>
          <Box sx={{ mb: { xs: 1, md: 2 } }}>
            <LandTypeSelect />
          </Box>
        </Grid>
      </Grid>
      
      {/* 第二段：搜尋方式選擇 */}
      {showInput && (
        <>
          <Divider sx={{ borderColor: 'divider' }} />
          <Grid container px={2} py={{ xs: 1, md: 2 }} className="middleArea">
            <Grid item xs={12}>
              <DataFormSelect />
            </Grid>
          </Grid>
        </>
      )}
      
      {/* 第三段：輸入欄位和下載 */}
      {showInput && (
        <>
          <Divider sx={{ borderColor: 'divider' }} />
          <Grid container px={2} py={{ xs: 1, md: 2 }} className="bottomArea">
            <Grid item xs={12}>
              <Box sx={{ mb: { xs: 1.5, md: 3 } }}>
                <InputColumn />
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                <DownLoadBtn />
              </Box>
            </Grid>
          </Grid>
        </>
      )}
    </Box>
  );
}

export default LandData;
