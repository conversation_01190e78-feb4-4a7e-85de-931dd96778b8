.ValidationErrors {
  margin-top: 16px;

  .error-accordion {
    margin-bottom: 8px;
    border: 1px solid var(--color-danger);
    border-radius: 8px !important;
    overflow: hidden;

    &:before {
      display: none;
    }

    &.Mui-expanded {
      margin: 0 0 8px 0;
    }
  }

  .error-accordion-summary {
    background-color: rgba(211, 47, 47, 0.08);
    
    &:hover {
      background-color: rgba(211, 47, 47, 0.12);
    }

    &.error {
      border-left: 4px solid var(--color-danger);
    }

    &.warning {
      background-color: rgba(237, 108, 2, 0.08);
      border-left: 4px solid var(--color-warning);

      &:hover {
        background-color: rgba(237, 108, 2, 0.12);
      }
    }

    .MuiAccordionSummary-content {
      margin: 12px 0;
    }
  }

  .error-accordion-details {
    padding: 16px;
    background-color: var(--color-surface-alt);
    border-top: 1px solid rgba(211, 47, 47, 0.2);

    .<PERSON><PERSON><PERSON><PERSON>t-root {
      &:last-child {
        margin-bottom: 0;
      }
    }

    code {
      background-color: rgba(0, 0, 0, 0.08);
      padding: 2px 6px;
      border-radius: 4px;
      font-family: "Courier New", monospace;
      font-size: 0.85em;
    }
  }

  // 響應式調整
  @media (max-width: 600px) {
    .MuiChip-root {
      font-size: 0.7rem;
      height: 20px;
    }

    .error-accordion-details {
      padding: 12px;
    }
  }
}
