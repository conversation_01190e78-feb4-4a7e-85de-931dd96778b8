/**
 * 使用者角色資料遷移腳本（使用 Admin SDK）
 * 
 * 目的：為所有現有使用者新增 roleDev 欄位
 * 
 * 執行方式：
 * 1. 從 Firebase Console 下載 Service Account 金鑰
 * 2. 將金鑰檔案放在 scripts/serviceAccountKey.json
 * 3. 執行：node scripts/migrateUserRoles-admin.js
 * 
 * 注意事項：
 * 1. 此腳本會保留多選角色格式（如 "admin,editor" → roleDev: "admin,editor"）
 * 2. 預設 roleDev = role（保持向後相容）
 * 3. 已有 roleDev 欄位的使用者會被跳過
 * 4. 執行前建議先備份 Firebase Realtime Database
 */

/* eslint-disable no-console */

// 使用 Firebase Admin SDK
const admin = require('firebase-admin');
const path = require('path');

// Service Account 金鑰路徑
const serviceAccountPath = path.join(__dirname, 'serviceAccountKey.json');

// 檢查 Service Account 金鑰是否存在
const fs = require('fs');

if (!fs.existsSync(serviceAccountPath)) {
  console.error('========================================');
  console.error('❌ 找不到 Service Account 金鑰檔案');
  console.error('========================================');
  console.error(`請將 Firebase Service Account 金鑰放在：`);
  console.error(`${serviceAccountPath}\n`);
  console.error('取得金鑰步驟：');
  console.error('1. 前往 Firebase Console');
  console.error('2. 專案設定 > 服務帳戶');
  console.error('3. 點擊「產生新的私密金鑰」');
  console.error('4. 下載 JSON 檔案並重新命名為 serviceAccountKey.json');
  console.error('5. 將檔案放在 scripts/ 資料夾\n');
  process.exit(1);
}

// 初始化 Firebase Admin
const serviceAccount = require(serviceAccountPath);

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://land-web-4f1ad-default-rtdb.asia-southeast1.firebasedatabase.app"
});

const db = admin.database();

/**
 * 執行使用者資料遷移
 */
const migrateUsers = async () => {
  console.log('========================================');
  console.log('🚀 開始使用者角色資料遷移（Admin SDK）');
  console.log('========================================\n');
  
  try {
    const usersRef = db.ref('/users');
    console.log('📡 正在從 Firebase Realtime Database 讀取使用者資料...\n');
    
    const snapshot = await usersRef.once('value');
    const users = snapshot.val();
    
    if (!users) {
      console.log('⚠️  沒有找到使用者資料');
      console.log('   請確認 Firebase Realtime Database 路徑: /users\n');
      await admin.app().delete();
      process.exit(0);
    }
    
    console.log(`✅ 成功讀取 ${Object.keys(users).length} 個使用者\n`);
    console.log('========================================');
    console.log('開始分析使用者資料...');
    console.log('========================================\n');
    
    const updates = {};
    let updatedCount = 0;
    let skippedCount = 0;
    let multiRoleCount = 0;
    
    Object.entries(users).forEach(([uid, userData]) => {
      // 檢查是否已有 roleDev 欄位
      if (!userData.roleDev) {
        // 預設：roleDev 與 role 相同（保持多選格式）
        const roleValue = userData.role || 'anonymous';
        updates[`/users/${uid}/roleDev`] = roleValue;
        updatedCount += 1;
        
        // 檢查是否為多選角色
        const isMultiRole = roleValue.includes(',');
        if (isMultiRole) {
          multiRoleCount += 1;
        }
        
        console.log(`✓ [${updatedCount}] 使用者: ${userData.email || uid}`);
        console.log(`  - UID: ${uid}`);
        console.log(`  - role: ${roleValue}`);
        console.log(`  - roleDev: ${roleValue} (新增)`);
        
        if (isMultiRole) {
          const roles = roleValue.split(',').map(r => r.trim());
          console.log(`  - 角色清單: [${roles.join(', ')}]`);
        }
        
        console.log('');
      } else {
        skippedCount += 1;
        console.log(`- [跳過] 使用者: ${userData.email || uid}`);
        console.log(`  已有 roleDev 欄位 (role=${userData.role}, roleDev=${userData.roleDev})\n`);
      }
    });
    
    console.log('========================================');
    console.log('分析結果');
    console.log('========================================');
    console.log(`總使用者數: ${Object.keys(users).length}`);
    console.log(`需要更新: ${updatedCount} 個`);
    console.log(`跳過: ${skippedCount} 個（已有 roleDev）`);
    console.log(`多選角色: ${multiRoleCount} 個\n`);
    
    if (Object.keys(updates).length > 0) {
      console.log('========================================');
      console.log('🔄 開始更新資料庫...');
      console.log('========================================\n');
      
      // 使用 Admin SDK 更新（有完整權限）
      await db.ref().update(updates);
      
      console.log('========================================');
      console.log('✅ 遷移完成！');
      console.log('========================================');
      console.log(`成功為 ${updatedCount} 個使用者新增 roleDev 欄位`);
      
      if (multiRoleCount > 0) {
        console.log(`其中 ${multiRoleCount} 個使用者使用多選角色格式`);
      }
      
      console.log('\n建議：');
      console.log('1. 登入系統確認使用者資料正常');
      console.log('2. 測試不同環境下的角色切換');
      console.log('3. 檢查權限控制是否正常運作\n');
    } else {
      console.log('========================================');
      console.log('✅ 資料庫已是最新狀態');
      console.log('========================================');
      console.log('所有使用者已有 roleDev 欄位，無需更新\n');
    }
    
    // 清理 Admin 連線
    await admin.app().delete();
    process.exit(0);
  } catch (error) {
    console.error('========================================');
    console.error('❌ 遷移失敗');
    console.error('========================================');
    console.error('錯誤訊息:', error.message);
    console.error('錯誤詳情:', error);
    console.error('\n建議排查：');
    console.error('1. 檢查網路連線');
    console.error('2. 確認 Service Account 金鑰正確');
    console.error('3. 確認 Firebase 專案 ID 正確');
    console.error('4. 確認 Database URL 正確\n');
    
    await admin.app().delete();
    process.exit(1);
  }
};

// 執行遷移
console.log('\n⚠️  執行前確認：');
console.log('1. 是否已備份 Firebase Realtime Database？');
console.log('2. 是否在正確的 Firebase 專案？');
console.log('3. Service Account 金鑰是否正確？\n');

console.log('開始執行...\n');
migrateUsers();
