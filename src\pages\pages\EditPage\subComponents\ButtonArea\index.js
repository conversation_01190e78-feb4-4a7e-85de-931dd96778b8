import React, { useContext, useState } from "react";
import PropTypes from "prop-types";
import { Box, Grid } from "@mui/material";
import EditButton from "./EditButton";
import LSNDropdown from "./LSNDropdown";
import LNDropdown from "./LNDropdown";
import PreButton from "./PreButton";
import NextButton from "./NextButton";
import SaveButton from "./SaveButton";
import HistoryButton from "./HistoryButton";
import CustomPopper from "../../../../../Component/CustomPopper/CustomPopper";
import { StoreContext } from "../../../../../store/StoreProvider";
import ExtraBasicInfo from "./ExtraBasicInfo";
import AddLandDialog from "../PopWindow/AddLandDialog";
import AddLandButton from "./AddLandButton";

function ButtonArea({ showPermissionError }) {
  const [state] = useContext(StoreContext);
  const { showSaveBtnPopper } = state.edit;
  const [anchorEl, setAnchorEl] = useState(null); // popper位置參數
  const [openAddLandDialog, setOpenAddLandDialog] = useState(false); // AddLandDialog開啟控制

  return (
    <Box className="buttonArea">
      <Grid container justifyContent="center" className="topArea">
        <Grid
          item
          container
          xs={1}
          direction="row"
          alignItems="center"
          justifyContent="center"
        >
          <AddLandButton setOpenAddLandDialog={setOpenAddLandDialog} />
        </Grid>
        <Grid
          item
          container
          xs={4}
          direction="row"
          justifyContent="space-around"
        >
          <LNDropdown />
          <LSNDropdown />
        </Grid>
        <Grid item container xs={2} direction="row" justifyContent="center">
          <EditButton showPermissionError={showPermissionError} />
        </Grid>
        <Grid
          item
          container
          xs={2}
          direction="row"
          justifyContent="space-between"
        >
          <PreButton />
          <NextButton />
        </Grid>
        <Grid item container xs={2} direction="row" justifyContent="center">
          <SaveButton setAnchorEl={setAnchorEl} showPermissionError={showPermissionError} />
        </Grid>
        <Grid
          item
          container
          xs={1}
          direction="row"
          alignItems="center"
          justifyContent="center"
        >
          <HistoryButton />
        </Grid>
      </Grid>
      <Grid container className="middleArea">
        <ExtraBasicInfo />
      </Grid>
      <CustomPopper open={showSaveBtnPopper} anchorEl={anchorEl} />
      <AddLandDialog
        openAddLandDialog={openAddLandDialog}
        setOpenAddLandDialog={setOpenAddLandDialog}
      />
    </Box>
  );
}

ButtonArea.propTypes = {
  showPermissionError: PropTypes.func,
};

ButtonArea.defaultProps = {
  showPermissionError: null,
};

export default ButtonArea;
