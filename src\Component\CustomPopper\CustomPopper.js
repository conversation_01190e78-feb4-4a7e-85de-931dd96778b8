import React from "react";

// scss
import "./CustomPopper.scss";

// material ui
import Popper from "@mui/material/Popper";
import Fade from "@mui/material/Fade";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";

function CustomPopper({ open, anchorEl }) {
  return (
    <Popper
      open={open}
      anchorEl={anchorEl}
      placement="top"
      transition
      className="CustomPopper"
    >
      {({ TransitionProps }) => (
        <Fade {...TransitionProps} timeout={350}>
          <Paper className="papper">
            <Typography sx={{ p: 2 }}>尚有輸入欄位錯誤訊息</Typography>
          </Paper>
        </Fade>
      )}
    </Popper>
  );
}

export default CustomPopper;
