import React, { useContext, useState, useEffect } from "react";
import { useHistory } from "react-router-dom";

// material ui
import Button from "@mui/material/Button";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../utils";
import Act from "../../../../../store/actions";
import getLandID from "../../../../../utils/getLandID";
import hisAddSearch from "../../../../../utils/hisAddSearch";

function NextButton() {
  const [state, dispatch] = useContext(StoreContext);
  const {
    landName,
    landSerialNumber,
    selctionIndex,
    LSNList,
    isLock,
    lockUser,
  } = state.edit;
  const { displayName } = state.user;
  const [disable, setDisable] = useState(true);
  const history = useHistory();

  useEffect(() => {
    if (
      !isEmpty(landName) &&
      !isEmpty(landSerialNumber) &&
      selctionIndex !== LSNList.length - 1
    ) {
      setDisable(false);
    } else {
      setDisable(true);
    }
  }, [landName, landSerialNumber, selctionIndex]);

  return (
    <Button
      className={
        disable || (isLock && displayName === lockUser)
          ? "nextBtnDisable"
          : "nextBtn"
      }
      disabled={disable || (isLock && displayName === lockUser)}
      onClick={async () => {
        if (selctionIndex + 1 < LSNList.length) {
          const nextLSN = LSNList[selctionIndex + 1].landSerialNumber;
          const nextID = await getLandID(landName, nextLSN);
          hisAddSearch(history, { landId: nextID });

          dispatch({
            type: Act.SET_SELECTIONINDEX,
            payload: selctionIndex + 1,
          });
          dispatch({
            type: Act.SET_LANDSERIALNUMBER,
            payload: nextLSN,
          });
        }
      }}
    >
      下一筆
    </Button>
  );
}

export default NextButton;
