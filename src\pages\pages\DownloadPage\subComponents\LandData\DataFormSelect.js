import React, { useContext, useEffect, useState } from "react";

// material ui
import Box from "@mui/material/Box";
import FormControl from "@mui/material/FormControl";
import FormLabel from "@mui/material/FormLabel";
import FormControlLabel from "@mui/material/FormControlLabel";
import Radio from "@mui/material/Radio";
import RadioGroup from "@mui/material/RadioGroup";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

function DataFormSelect() {
  const [state, dispatch] = useContext(StoreContext);
  const { landType, dataFormType } = state.download;
  const [landTypeStr, setLandTypeStr] = useState("");

  useEffect(() => {
    const findLandTypeSelect = landType.find((obj) => obj.select);
    if (findLandTypeSelect) {
      setLandTypeStr(findLandTypeSelect.property);
    }
  }, [landType]);

  const handleChange = (evt) => {
    let tmpDataFormType = JSON.parse(JSON.stringify(dataFormType));
    // init dataFormType
    tmpDataFormType = tmpDataFormType.map((el) => ({
      ...el,
      check: false,
    }));
    const findSelect = tmpDataFormType.find(
      (el) => el.type === evt.target.value
    );
    if (findSelect) {
      findSelect.check = true;
    }
    dispatch({
      type: Act.SET_DATAFORMTYPE,
      payload: tmpDataFormType,
    });
  };

  return (
    <Box sx={{ width: '100%' }}>
      <FormControl component="fieldset" sx={{ width: '100%' }}>
        <FormLabel 
          component="legend"
          sx={{
            color: 'text.primary',
            fontWeight: 600,
            fontSize: '1.1rem',
            mb: 1,
            '&.Mui-focused': {
              color: 'primary.main',
            },
          }}
        >
          選擇搜尋方式
        </FormLabel>
        <RadioGroup 
          defaultValue={dataFormType[0]?.type}
          sx={{
            mt: 1,
            '& .MuiFormControlLabel-root': {
              marginBottom: 1,
            },
          }}
        >
          {landTypeStr &&
            dataFormType.map((obj, index) => (
              <FormControlLabel
                key={`type${index}`}
                value={obj.type}
                control={
                  <Radio
                    onChange={handleChange}
                    sx={{
                      color: 'primary.main',
                      '&.Mui-checked': {
                        color: 'primary.main',
                      },
                    }}
                  />
                }
                label={obj.label[landTypeStr]}
                sx={{
                  '& .MuiFormControlLabel-label': {
                    color: 'text.primary',
                    fontWeight: 500,
                    fontSize: '0.95rem',
                  },
                }}
              />
            ))}
        </RadioGroup>
      </FormControl>
    </Box>
  );
}

export default DataFormSelect;
