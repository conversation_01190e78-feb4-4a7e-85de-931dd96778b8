/* eslint-disable */
import React from "react";
import { TableHead, TableRow, TableCell } from "@mui/material";
import { boundIngBoxex } from "../../EditPage/common/computedColumnValue";
// 常數配置
const TABLE_HEADERS = [
    "地段",
    "地號",
    "台灣座標系X座標 (TWD97)",
    "台灣座標系Y座標 (TWD97)",
    "全球座標系經度 (WGS84)",
    "全球座標系緯度 (WGS84)"
];

const CELL_STYLE = { border: "1px solid var(--color-border)" };
const HEADER_STYLE = {
  backgroundColor: "var(--color-surface-alt)",
  color: "var(--color-text-secondary)",
  fontWeight: 600,
  ...CELL_STYLE,
};

const renderTableHeaders = () => (
    <TableHead>
        <TableRow>
            {TABLE_HEADERS.map((header, index) => (
                <TableCell key={index} sx={HEADER_STYLE}>
                    {header}
                </TableCell>
            ))}
        </TableRow>
    </TableHead>
);

// 常數配置
const PAGINATION_OPTIONS = [10, 25, 50];
const COORDINATE_BOUNDS = {
    x: { min: boundIngBoxex.all.minX, max: boundIngBoxex.all.maxX },
    y: { min: boundIngBoxex.all.minY, max: boundIngBoxex.all.maxY }
};

export { TABLE_HEADERS, CELL_STYLE, HEADER_STYLE, renderTableHeaders, PAGINATION_OPTIONS, COORDINATE_BOUNDS };
