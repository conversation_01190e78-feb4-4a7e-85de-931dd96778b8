import React, { useContext } from "react";
import { Button, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";

import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

function ClearAll() {
  const [, dispatch] = useContext(StoreContext);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const handleClick = () => {
    dispatch({
      type: Act.SET_KEYWORD,
      payload: "",
    });
  };

  return (
    <Button
      variant="text"
      color="primary"
      size="medium"
      onClick={handleClick}
      sx={{ whiteSpace: "nowrap", alignSelf: isMobile ? "stretch" : "center" }}
    >
      清除搜尋文字
    </Button>
  );
}

export default ClearAll;
