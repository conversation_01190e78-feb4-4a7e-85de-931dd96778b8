import React, { useContext } from "react";

// material ui
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

function LandTypeSelect() {
  const [state, dispatch] = useContext(StoreContext);
  const { landType } = state.download;

  return (
    <Box sx={{ width: '100%' }}>
      <Typography 
        variant="h6" 
        sx={{ 
          mb: 2, 
          color: "text.primary",
          fontWeight: 600
        }}
      >
        選擇土地資料類別
      </Typography>
      <Autocomplete
        options={landType.map((element) => element.label)}
        renderInput={(params) => (
          <TextField 
            {...params} 
            label="請選擇資料類別"
            variant="outlined"
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                backgroundColor: 'background.paper',
                '&.Mui-focused': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                    borderWidth: 2,
                  },
                },
                '&:hover:not(.Mui-focused)': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                  },
                },
              },
              '& .MuiInputLabel-root': {
                color: 'text.secondary',
                fontWeight: 500,
              },
            }}
          />
        )}
        onChange={(event, value) => {
          let tmpLandType = JSON.parse(JSON.stringify(landType));
          // init landType
          tmpLandType = tmpLandType.map((element) => ({
            ...element,
            select: false,
          }));

          const findObj = tmpLandType.find((element) => element.label === value);
          if (findObj) {
            findObj.select = true;
          }

          dispatch({
            type: Act.SET_LANDTYPE,
            payload: tmpLandType,
          });
        }}
        sx={{
          '& .MuiAutocomplete-listbox': {
            '& .MuiAutocomplete-option': {
              fontSize: '0.95rem',
              padding: '12px 16px',
              '&[aria-selected="true"]': {
                backgroundColor: 'primary.light',
                color: 'primary.contrastText',
              },
              '&.Mui-focused': {
                backgroundColor: 'action.hover',
              },
            },
          },
        }}
      />
    </Box>
  );
}

export default LandTypeSelect;
