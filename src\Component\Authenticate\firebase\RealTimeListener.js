import { useContext, useEffect } from "react";
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";
import {
  getDefaultSettings,
  getWebStyle,
  SETTINGS_PATH,
  getSettingPath,
  getSwgJSON,
  getFieldSetting,
} from "../../../api/firebase/realtimeDatabase";
import { getImage } from "../../../api/firebase/storage";
import { leastOneValueEmpty } from "../../../utils";
import { Api } from "../../../api/land/Api";

const RealTimeListener = ({ firebaseAuth, realTimeDb }) => {
  const [state, dispatch] = useContext(StoreContext);
  const { user, main } = state;
  const { uid, currentUser, role } = user;
  const { image, swgJSON } = main;

  // get webStyle setting
  useEffect(() => {
    // 取得 webStyle setting
    getWebStyle(SETTINGS_PATH.webStyle)
      .then((data) => {
        dispatch({
          type: Act.SET_WEB_STYLE,
          payload: { ...data },
        });
      })
      .catch((err) => console.log(err));

    // image path 區分 production or development
    const imgPath = getSettingPath(
      process.env.REACT_APP_MODE,
      SETTINGS_PATH.image
    );

    // 取得 web image url: background, banner, logo
    getWebStyle(imgPath)
      .then((data) => {
        dispatch({
          type: Act.SET_IMAGE,
          payload: { ...data },
        });
      })
      .catch((err) => console.log(err));

    // 取得系統預設的欄位排序 (for browse/information table column)
    getDefaultSettings()
      .then((data) => {
        // console.log(data);
        dispatch({
          type: Act.SETTINGS_SORTED_HEADER_SET,
          payload: data,
        });
      })
      .catch((err) => console.log(err));

    getFieldSetting()
      .then((data) => {
        // console.log(data);
        dispatch({
          type: Act.SETTINGS_FIELD_SETTING_SET,
          payload: data,
        });
      })
      .catch((err) => console.log(err));

    // 取得 swgJSON route
  }, []);

  useEffect(() => {
    // get swgJON file path of firebase storage
    getSwgJSON(role).then((res) => {
      // console.log("getSwgJSON res", res);
      dispatch({
        type: Act.SET_SWG_JSON,
        payload: {
          storage: res.storage,
          JSON: swgJSON.JSON,
        },
      });
    });
  }, [role]);

  // 取得 web image url with token: background, banner, logo
  useEffect(() => {
    if (leastOneValueEmpty(image.desktop) || leastOneValueEmpty(image.mobile))
      return;
    // get desktop logo
    getImage(image.desktop.logo).then((urlToken) =>
      dispatch({
        type: Act.SET_IMAGE_DESK_LOGO,
        payload: urlToken,
      })
    );
    // get desktop background
    getImage(image.desktop.background).then((urlToken) =>
      dispatch({
        type: Act.SET_IMAGE_DESK_BG,
        payload: urlToken,
      })
    );
    // get desktop banner
    getImage(image.desktop.banner).then((urlToken) =>
      dispatch({
        type: Act.SET_IMAGE_DESK_BANNER,
        payload: urlToken,
      })
    );
    // get mobile logo
    getImage(image.mobile.logo).then((urlToken) =>
      dispatch({
        type: Act.SET_IMAGE_MOBILE_LOGO,
        payload: urlToken,
      })
    );
    // get mobile background
    getImage(image.mobile.background).then((urlToken) =>
      dispatch({
        type: Act.SET_IMAGE_MOBILE_BG,
        payload: urlToken,
      })
    );
    // get mobile banner
    getImage(image.mobile.banner).then((urlToken) =>
      dispatch({
        type: Act.SET_IMAGE_MOBILE_BANNER,
        payload: urlToken,
      })
    );
  }, [image]);

  useEffect(() => {
    // firebase realtime database 取得 userInfo 的資料路徑
    const userInfo = (userUid) => `users/${userUid}`;

    if (!currentUser) {
      return;
    }
    if (!uid) {
      dispatch({
        type: Act.FIREBASE_LOGIN_USER,
        payload: { role: role.anonymous },
      });
    } else {
      // Get authToken once.
      currentUser.getIdToken(true).then((token) => {
        realTimeDb
          .ref(userInfo(uid))
          .once("value")
          .then((snapshot) => {
            dispatch({
              type: Act.FIREBASE_LOGIN_USER,
              payload: { ...snapshot.val(), token },
            });
            // set token to headers.Authorization in global axios
            Api.setAxiosAuth(token);

            /**
             * save token to localStorage:
             * 使用者登入時若刷新頁面,從 firebase 取得 token 較慢,
             * 可以先拿 localStorage 的 token 來使用
             * 以便 fetch API 時 headers.Authorization 可以帶 token
             * */
            localStorage.setItem("token", JSON.stringify(token));
          });
      });
    }
  }, [uid, currentUser, dispatch, realTimeDb]);

  return null;
};

export default RealTimeListener;
