# ValidationErrors 元件使用說明

## 📦 元件概述

`ValidationErrors` 是一個用於顯示 Excel 驗證錯誤的可視化元件，採用 Accordion（手風琴）設計，清晰展示表頭錯誤和資料錯誤。

## 🎯 功能特點

- ✅ 分類顯示錯誤（表頭錯誤 / 資料內容錯誤）
- ✅ 使用 Chip 標籤標記工作表、列號、欄位
- ✅ 提供智慧建議和解決方案
- ✅ 顯示預期值 vs 實際值的對比
- ✅ 錯誤數量統計
- ✅ 響應式設計

## 📋 錯誤資料結構

```javascript
const errors = [
  {
    type: 'header_order',           // 錯誤類型
    sheetName: '屏東1-庭羽',         // 工作表名稱
    columnIndex: 3,                 // 欄位索引
    rowNumber: 5,                   // 列號（可選）
    columnName: '來源',             // 欄位名稱（可選）
    expected: 'Land-->source',      // 預期值
    actual: 'Land->source',         // 實際值
    message: '第 3 欄：預期為...',  // 錯誤訊息
    suggestion: '您是否要輸入...', // 建議（可選）
    severity: 'error',              // 嚴重程度：'error' | 'warning'
    missing: [...],                 // 缺少的欄位列表（可選）
    extra: [...]                    // 多餘的欄位列表（可選）
  }
];
```

## 🔧 使用方法

### 1. 導入元件

```javascript
import ValidationErrors from "./subComponents/ValidationErrors";
```

### 2. 基本使用

```javascript
function ImportDataPage() {
  const [detailedErrors, setDetailedErrors] = useState([]);

  return (
    <Box>
      {/* 其他內容 */}
      
      {detailedErrors.length > 0 && (
        <ValidationErrors errors={detailedErrors} />
      )}
    </Box>
  );
}
```

### 3. 與驗證結果整合

```javascript
{!isValidating && validationResult && (
  <Box sx={{ mt: 3 }}>
    <Alert
      severity={validationResult.pass ? "success" : "error"}
      icon={validationResult.pass ? <CheckCircleIcon /> : <ErrorIcon />}
    >
      <AlertTitle>
        {validationResult.pass ? "驗證通過" : "驗證失敗"}
      </AlertTitle>
      {validationResult.pass ? (
        <Typography>...</Typography>
      ) : (
        <Box>
          <Typography variant="body2" gutterBottom>
            發現 <strong>{validationResult.errorCount}</strong> 個錯誤
          </Typography>
          <Typography variant="caption" color="text.secondary">
            💡 提示：請查看下方詳細錯誤資訊
          </Typography>
        </Box>
      )}
    </Alert>
    
    {/* 詳細錯誤訊息 */}
    {!validationResult.pass && detailedErrors.length > 0 && (
      <ValidationErrors errors={detailedErrors} />
    )}
  </Box>
)}
```

## 🎨 錯誤類型範例

### 表頭順序錯誤

```javascript
{
  type: 'header_order',
  sheetName: '完成進度',
  columnIndex: 5,
  expected: 'Land-->landName',
  actual: 'Land-->abstract',
  message: '第 5 欄：預期為「Land-->landName」，實際為「Land-->abstract」'
}
```

**顯示效果**：
```
❌ 第 5 欄：預期為「Land-->landName」，實際為「Land-->abstract」
   欄位位置：第 5 欄
   ┌──────────────────────┐
   │ 預期：Land-->landName │
   │ 實際：Land-->abstract │
   └──────────────────────┘
```

### 表頭內容錯誤（含建議）

```javascript
{
  type: 'header_content',
  sheetName: '完成進度',
  columnIndex: 3,
  actual: 'Land->boxNumber',
  message: '第 3 欄的欄位 ID「Land->boxNumber」不在標準欄位列表中',
  suggestion: '您是否要輸入「Land-->boxNumber」？',
  severity: 'error'
}
```

**顯示效果**：
```
❌ 第 3 欄的欄位 ID「Land->boxNumber」不在標準欄位列表中
   欄位位置：第 3 欄
   💡 建議：您是否要輸入「Land-->boxNumber」？
```

### 缺少必要欄位

```javascript
{
  type: 'header_content',
  sheetName: '完成進度',
  message: '缺少 2 個必要欄位',
  missing: [
    { id: 'Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent', label: '土地標示起始日期', order: 13 },
    { id: 'Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent', label: '土地權利起始日期', order: 19 }
  ],
  severity: 'error'
}
```

**顯示效果**：
```
❌ 缺少 2 個必要欄位
   缺少的欄位：
   ┌────────────────────┐
   │ 土地標示起始日期   │
   │ 土地權利起始日期   │
   └────────────────────┘
```

### 資料內容錯誤

```javascript
{
  type: 'data_error',
  sheetName: '屏東1-庭羽',
  rowNumber: 5,
  columnName: '來源',
  message: '來源欄位格式錯誤：只能填寫 0-5 之間的整數',
  value: '7',
  severity: 'error'
}
```

**顯示效果**：
```
❌ [工作表: 屏東1-庭羽] [第 5 列] [來源]
   來源欄位格式錯誤：只能填寫 0-5 之間的整數
   當前值：7
```

## 🎭 嚴重程度

### Error（錯誤）- 紅色
```javascript
{ severity: 'error' }  // 或不設定（預設為 error）
```
- 使用 ErrorIcon（紅色）
- 必須修正才能通過驗證

### Warning（警告）- 黃色
```javascript
{ severity: 'warning' }
```
- 使用 WarningIcon（黃色）
- 建議修正，但不一定阻擋驗證

## 📱 響應式設計

元件會自動適應不同螢幕尺寸：

- **桌面（> 960px）**：完整顯示所有資訊
- **平板（600px - 960px）**：調整 Chip 大小
- **手機（< 600px）**：Chip 自動換行，減小內距

## 🎨 樣式自訂

可以透過全域 CSS 變數自訂顏色：

```scss
:root {
  --color-danger: #d32f2f;    // 錯誤顏色
  --color-warning: #ed6c02;   // 警告顏色
  --color-primary: #1976d2;   // 主色
  --color-surface-alt: #fafafa; // 背景色
}
```

## 🔍 限制與注意事項

### 錯誤數量限制
- 資料內容錯誤最多顯示 **50 個**
- 超過 50 個時會顯示提示訊息

```javascript
{dataErrors.length > 50 && (
  <Alert severity="warning">
    顯示前 50 個錯誤，總共 {dataErrors.length} 個錯誤
  </Alert>
)}
```

### 自動展開
- 預設展開「表頭錯誤」區塊
- 使用者可以切換展開/收合

## 📦 依賴套件

```json
{
  "@mui/material": "^5.x",
  "@mui/icons-material": "^5.x",
  "react": "^18.x"
}
```

## 🚀 完整範例

```javascript
import React, { useState } from "react";
import { Box, Alert, AlertTitle, Typography } from "@mui/material";
import { CheckCircleIcon, ErrorIcon } from "@mui/icons-material";
import ValidationErrors from "./subComponents/ValidationErrors";

function ImportDataPage() {
  const [validationResult, setValidationResult] = useState(null);
  const [detailedErrors, setDetailedErrors] = useState([]);

  // 模擬驗證失敗
  const handleValidationFailed = () => {
    setDetailedErrors([
      {
        type: 'header_order',
        sheetName: '完成進度',
        columnIndex: 3,
        expected: 'Land-->boxNumber',
        actual: 'Land->boxNumber',
        message: '第 3 欄：預期為「Land-->boxNumber」，實際為「Land->boxNumber」'
      },
      {
        type: 'header_content',
        sheetName: '完成進度',
        message: '缺少 2 個必要欄位',
        missing: [
          { label: '土地標示起始日期' },
          { label: '土地權利起始日期' }
        ]
      }
    ]);
    
    setValidationResult({
      pass: false,
      errorCount: 2
    });
  };

  return (
    <Box>
      {/* 驗證結果 */}
      {validationResult && (
        <Box sx={{ mt: 3 }}>
          <Alert
            severity={validationResult.pass ? "success" : "error"}
            icon={validationResult.pass ? <CheckCircleIcon /> : <ErrorIcon />}
          >
            <AlertTitle>
              {validationResult.pass ? "驗證通過" : "驗證失敗"}
            </AlertTitle>
            {!validationResult.pass && (
              <Typography variant="body2">
                發現 <strong>{validationResult.errorCount}</strong> 個錯誤
              </Typography>
            )}
          </Alert>
          
          {/* 詳細錯誤訊息 */}
          {!validationResult.pass && detailedErrors.length > 0 && (
            <ValidationErrors errors={detailedErrors} />
          )}
        </Box>
      )}
    </Box>
  );
}

export default ImportDataPage;
```

## 🎯 最佳實踐

1. **錯誤訊息要清晰**
   ```javascript
   // ✅ 好的訊息
   message: '第 3 欄：預期為「Land-->boxNumber」，實際為「Land->boxNumber」'
   
   // ❌ 不好的訊息
   message: '欄位錯誤'
   ```

2. **提供具體位置**
   ```javascript
   {
     sheetName: '屏東1-庭羽',
     rowNumber: 5,
     columnName: '來源'
   }
   ```

3. **提供智慧建議**
   ```javascript
   {
     message: '...',
     suggestion: '您是否要輸入「Land-->source」？'
   }
   ```

4. **使用適當的嚴重程度**
   ```javascript
   severity: 'error'   // 必須修正
   severity: 'warning' // 建議修正
   ```

---

**元件版本**: 1.0.0  
**最後更新**: 2025-10-01  
**作者**: GitHub Copilot
