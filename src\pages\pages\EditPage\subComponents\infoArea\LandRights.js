import React, { useContext, useEffect, useState } from "react";

// material ui
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import MenuIcon from "@mui/icons-material/Menu";

// react-beautiful-dnd
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../utils";
import { Api, readOntoData } from "../../../../../api/land/Api";
import Act from "../../../../../store/actions";
import { landRightsPlaceHolder } from "../../common/phDes";
import {
  autoCompleteColumn,
  checkAllStartDate,
  disableEditFunction,
  disableFontColor,
  eachRuleRequired<PERSON>he<PERSON>,
  getCol<PERSON>rder,
  getOwners,
} from "../../common";
import evtType from "../../../../../utils/evtType";

// components
import AddButton from "./AddButton";
import RemoveButton from "./RemoveButton";
import CustomLoading from "../../../../../Component/CustomLoading/CustomLoading";
import StartDateInput from "./subComponents/StartDateInput";
import LmAutoComplete from "./subComponents/LMAutoComplete";

function LandRights() {
  const [state, dispatch] = useContext(StoreContext);
  const { landRightInfo, infoLoading, landId } = state.edit;
  const [checkDateRepeat, setCheckDateRepeat] = useState([]); // 判斷有無重複日期

  useEffect(() => {
    if (isEmpty(landId)) {
      dispatch({
        type: Act.SET_LANDRIGHTINFO,
        payload: [],
      });
      return;
    }

    const apiStr = Api.getLandRightsID().replace("{landId}", landId);

    // 打開Info Loading circle
    dispatch({
      type: Act.SET_INFOLOADING,
      payload: true,
    });

    readOntoData(apiStr).then((result) => {
      if (!isEmpty(result.data)) {
        const LRIds = result.data.map((element) => element.landRightsId);
        const LRApiStr = Api.getLandRightsEvents().replace(
          "{ids}",
          LRIds.join()
        );
        readOntoData(LRApiStr).then(async (LREvents) => {
          // console.log("LREvents ", LREvents);
          // [
          //   {LRT1: [{ ... }, { ... } ... { ... }]},
          //   {LRT2: [{ ... }, { ... } ... { ... }]},
          // ]

          // 先依照landRightsNumber建立空的物件
          const tmpResultData = JSON.parse(JSON.stringify(LREvents.data));
          const idObjs = tmpResultData.reduce((acc, { landRightsId }) => {
            acc[landRightsId] = landRightsId;
            return acc;
          }, {});

          const initLRT = Object.keys(idObjs).map((key) => ({ [key]: [] }));
          // 每個LRT event 欄位順序
          const colOrder = await getColOrder("LRT");

          let tmpLRTs = JSON.parse(JSON.stringify(initLRT));
          initLRT.forEach((lrt) => {
            const lrtId = Object.keys(lrt)[0];
            colOrder.forEach((element) => {
              const findAllObj = tmpResultData.filter(
                (eventObj) =>
                  eventObj.landRightsId === lrtId &&
                  eventObj.predicate === element.property
              );
              const findLRT = tmpLRTs.find(
                (obj) => Object.keys(obj)[0] === lrtId
              );
              if (findAllObj.length > 0) {
                const allValue = findAllObj.reduce(
                  (cur, next) => [...cur, next.value],
                  []
                );
                const tmpObj = JSON.parse(JSON.stringify(findAllObj[0]));
                if (element.property === "hasOwner") {
                  tmpObj.value = allValue;
                } else if (element.property === "cause") {
                  tmpObj.value = allValue[0].split("，");
                } else {
                  [tmpObj.value] = allValue;
                }
                tmpObj.object = element.label; // 把欄位名稱統一改成colOrder的命名
                Object.values(findLRT)[0].push(tmpObj);
              } else {
                Object.values(findLRT)[0].push({
                  object: element.label,
                  value: autoCompleteColumn(element.property) ? [] : "",
                  predicate: element.property,
                  landRightsId: lrtId,
                });
              }
            });
          });

          const latestOwnerList = await getOwners();
          tmpLRTs = tmpLRTs
            .map((element) => Object.values(element)[0])
            .map((arrEl) =>
              // 替換landRights的Owner ID
              arrEl.map((obj) => {
                if (obj.predicate === "hasOwner") {
                  if (obj.value.length > 0) {
                    return {
                      ...obj,
                      value: obj.value.map(
                        (item) =>
                          latestOwnerList.find(
                            (ownerObj) => ownerObj.label === item
                          ).value
                      ),
                    };
                  }
                }
                return obj;
              })
            );

          // 初始化checkDateRepeat
          const tmpArr = [];
          for (let i = 0; i < tmpLRTs.length; i += 1) {
            tmpArr.push(false);
          }
          setCheckDateRepeat(tmpArr);

          // 排序Events
          const sortTmpLRTs = tmpLRTs.sort((cur, next) => {
            const findCurOd =
              cur.find((el) => el.predicate === "landRightsNumber")?.value ||
              tmpLRTs.length;
            const findNextOd =
              next.find((el) => el.predicate === "landRightsNumber")?.value ||
              tmpLRTs.length;

            return findCurOd - findNextOd;
          });
          dispatch({
            type: Act.SET_LANDRIGHTINFO,
            payload: sortTmpLRTs,
          });

          // 關閉Info Loading circle
          dispatch({
            type: Act.SET_INFOLOADING,
            payload: false,
          });
        });
      } else {
        dispatch({
          type: Act.SET_LANDRIGHTINFO,
          payload: [],
        });

        // 關閉Info Loading circle
        dispatch({
          type: Act.SET_INFOLOADING,
          payload: false,
        });
      }
    });
  }, [landId]);

  const onDragEnd = (result) => {
    const startIndex = result.source.index;
    const endIndex = result.destination.index;
    const tmpLandRightsInfo = JSON.parse(JSON.stringify(landRightInfo));
    const tmpInfo = JSON.parse(JSON.stringify(tmpLandRightsInfo[startIndex]));
    tmpLandRightsInfo[startIndex] = tmpLandRightsInfo[endIndex];
    tmpLandRightsInfo[endIndex] = tmpInfo;

    // 變更次序互換
    const findNum1 = tmpLandRightsInfo[startIndex].find(
      (element) => element.predicate === "landRightsNumber"
    );
    const findNum2 = tmpLandRightsInfo[endIndex].find(
      (element) => element.predicate === "landRightsNumber"
    );
    const tmpNum = findNum1.value;
    findNum1.value = findNum2.value;
    findNum2.value = tmpNum;

    dispatch({
      type: Act.SET_LANDRIGHTINFO,
      payload: tmpLandRightsInfo,
    });
  };

  const handleChange = (inputValue, item) => {
    const tmpLandRightInfo = JSON.parse(JSON.stringify(landRightInfo));
    const findCol = Object.values(tmpLandRightInfo[item.index]).find(
      (element) => element.predicate === item.predicate
    );
    if (findCol) {
      findCol.value = inputValue;

      if (item.predicate === "hasStartDate") {
        setCheckDateRepeat(
          checkAllStartDate(
            tmpLandRightInfo,
            item,
            checkDateRepeat,
            "landRightsId"
          )
        );
      }

      dispatch({
        type: Act.SET_LANDRIGHTINFO,
        payload: tmpLandRightInfo,
      });
    }
  };

  return (
    <Grid item container className="LandRights">
      <Grid
        item
        container
        flexDirection="row"
        justifyContent="space-between"
        className="header"
      >
        <Grid
          item
          xs={2}
          container
          alignItems="center"
          display="flex"
          className="headerItem"
        >
          <Typography variant="h5">土地權利變更</Typography>
        </Grid>
        {!disableEditFunction(state) && (
          <Grid
            item
            container
            xs={3}
            justifyContent="flex-end"
            className="headerItem"
          >
            <AddButton itemType="LRT" />
          </Grid>
        )}
      </Grid>
      {infoLoading ? (
        <Grid container style={{ marginTop: "20px" }}>
          <CustomLoading />
        </Grid>
      ) : (
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="droppableId">
            {(provided) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                style={{ width: "100%" }}
              >
                {landRightInfo.map((LRInfo, index) => (
                  <Draggable
                    draggableId={`draggable_${index}`}
                    index={index}
                    key={index}
                    isDragDisabled={disableEditFunction(state)}
                  >
                    {(provided2) => (
                      <Grid
                        item
                        container
                        className="LRTItem"
                        ref={provided2.innerRef}
                        {...provided2.draggableProps}
                        {...provided2.dragHandleProps}
                      >
                        {!disableEditFunction(state) && (
                          <Grid item xs={1} className="colName">
                            <MenuIcon />
                          </Grid>
                        )}
                        <Grid
                          item
                          container
                          xs={!disableEditFunction(state) ? 9 : 12}
                          direction="column"
                          className="LRTinfoArea"
                        >
                          {Object.values(LRInfo).map((info, infoIndex) => (
                            <Grid item container key={infoIndex}>
                              <Grid item xs={3} className="labelName">
                                {info.object}
                              </Grid>
                              <Grid item xs={9}>
                                {info.predicate === "hasStartDate" && (
                                  <StartDateInput
                                    info={info}
                                    handleChange={handleChange}
                                    index={index}
                                    type={evtType.LandRights}
                                  />
                                )}
                                {autoCompleteColumn(info.predicate) && (
                                  <LmAutoComplete
                                    info={info}
                                    handleChange={handleChange}
                                    index={index}
                                  />
                                )}
                                {![
                                  "hasStartDate",
                                  "cause",
                                  "hasOwner",
                                ].includes(info.predicate) && (
                                  <TextField
                                    disabled={
                                      disableEditFunction(state) ||
                                      !Object.keys(
                                        landRightsPlaceHolder
                                      ).includes(info.predicate)
                                    }
                                    variant="standard"
                                    fullWidth
                                    value={info.value}
                                    placeholder={
                                      landRightsPlaceHolder[info.predicate]
                                    }
                                    onChange={(e) => {
                                      const newData = { ...info, index };
                                      handleChange(e.target.value, newData);
                                    }}
                                    multiline
                                    inputProps={disableFontColor}
                                    error={
                                      eachRuleRequiredCheck(state, info, index)
                                        .check
                                    }
                                    helperText={
                                      eachRuleRequiredCheck(state, info, index)
                                        .helpText
                                    }
                                  />
                                )}
                              </Grid>
                            </Grid>
                          ))}
                        </Grid>
                        {!disableEditFunction(state) && (
                          <Grid
                            item
                            container
                            xs={1}
                            className="addOrRemoveCol"
                          >
                            <Grid item xs={1} className="iconPos">
                              <RemoveButton itemType="LRT" index={index} />
                            </Grid>
                          </Grid>
                        )}
                      </Grid>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      )}
    </Grid>
  );
}

export default LandRights;
