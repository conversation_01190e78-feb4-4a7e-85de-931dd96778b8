import React, { useContext, useEffect, useState } from "react";

// material ui
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Divider from "@mui/material/Divider";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import Stack from "@mui/material/Stack";

// components
import DownLoadBtn from "./DownLoadBtn";
import DateTypeSelect from "./DateTypeSelect";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";

function EditorLand() {
  const [state] = useContext(StoreContext);
  const { selectDateType } = state.download;
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());

  useEffect(() => {
    if (selectDateType !== "multiple") {
      setEndDate(startDate);
    }
  }, [selectDateType, startDate]);

  return (
    <Box 
      className="EditorLand"
      sx={{
        '& .MuiTextField-root': {
          borderRadius: 2,
        },
        '& .MuiInputLabel-root': {
          color: 'text.secondary',
        },
      }}
    >
      <Grid container p={2} className="topArea">
        <Grid item xs={12}>
          <DateTypeSelect />
        </Grid>
      </Grid>
      <Divider sx={{ borderColor: 'divider' }} />
      <Grid container p={2} className="middleArea">
        <Grid item xs={12}>
          <Typography 
            variant="h6" 
            sx={{ 
              mb: 2, 
              color: "text.primary",
              fontWeight: 600
            }}
          >
            選擇編輯土地資料日期
          </Typography>
          <Stack 
            direction={{ xs: "column", sm: "row" }} 
            spacing={2} 
            alignItems={{ xs: "stretch", sm: "center" }}
            sx={{ maxWidth: 600 }}
          >
            <TextField
              label="開始日期"
              type="date"
              value={startDate.toISOString().split('T')[0]}
              onChange={(event) => {
                const newDate = new Date(event.target.value);
                if (!isNaN(newDate.getTime())) {
                  setStartDate(newDate);
                }
              }}
              sx={{ 
                minWidth: 200,
                '& .MuiInputLabel-root': {
                  color: 'text.secondary',
                },
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                }
              }}
              InputLabelProps={{
                shrink: true,
              }}
              variant="outlined"
              size="medium"
            />
            
            {selectDateType === "multiple" && (
              <>
                <Typography 
                  variant="body1" 
                  sx={{ 
                    color: "text.secondary",
                    fontWeight: 500,
                    px: 1,
                    alignSelf: "center"
                  }}
                >
                  到
                </Typography>
                
                <TextField
                  label="結束日期"
                  type="date"
                  value={endDate.toISOString().split('T')[0]}
                  onChange={(event) => {
                    const newDate = new Date(event.target.value);
                    if (!isNaN(newDate.getTime()) && newDate >= startDate) {
                      setEndDate(newDate);
                    }
                  }}
                  inputProps={{
                    min: startDate.toISOString().split('T')[0],
                  }}
                  sx={{ 
                    minWidth: 200,
                    '& .MuiInputLabel-root': {
                      color: 'text.secondary',
                    },
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                    }
                  }}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  variant="outlined"
                  size="medium"
                />
              </>
            )}
          </Stack>
        </Grid>
      </Grid>
      <Grid container p={2} className="bottomArea">
        <DownLoadBtn startDate={startDate} endDate={endDate} />
      </Grid>
    </Box>
  );
}

export default EditorLand;
