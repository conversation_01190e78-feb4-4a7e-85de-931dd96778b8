import React, { useContext, useEffect } from "react";

// firebase
import { signOut, getAuth } from "firebase/auth";
import { Redirect } from "react-router-dom";

// store
import { StoreContext } from "../../store/StoreProvider";
import act from "../../store/actions";

// config
import itemConfig from "../../api/config/config-localStorage";
import pathConfig from "../../config/route-path";

const SignOut = () => {
  const isLogin = JSON.parse(localStorage.getItem(itemConfig.isLogin));

  // eslint-disable-next-line no-unused-vars
  const [_, dispatch] = useContext(StoreContext);

  useEffect(() => {
    let isMounted = true; // note mutable flag
    if (isLogin) {
      const auth = getAuth();
      // SignOut firebase
      signOut(auth)
        .then(() => {
          if (isMounted) {
            // add conditional check
            // Sign-out successful.
          }
        })
        .catch((error) => {
          // An error happened.
          console.log(error);
        });
      // clean user data
      dispatch({ type: act.FIREBASE_LOGOUT_USER });
      // clean localStorage
      localStorage.removeItem(itemConfig.isLogin);
      // clean edit page
      dispatch({
        type: act.EDIT_SET_INIT,
      });
      // clean search page
      dispatch({
        type: act.SEARCH_SET_INIT,
      });
    }
    return () => {
      isMounted = false;
    }; // cleanup toggles value, if unmounted
  });

  return <Redirect to={pathConfig.signIn.url} />;
};

export default SignOut;
