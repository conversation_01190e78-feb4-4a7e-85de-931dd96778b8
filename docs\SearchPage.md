# /Search 頁面功能說明

## 頁面職責概覽
- `/Search` 頁面提供土地資料的關鍵字查詢，串接 StoreContext 的 `search` slice 並整合多個子元件組成搜尋體驗 (`src/pages/pages/SearchPage/index.js:68`, `src/store/StoreProvider/rootReducer.js:18`).
- 版面採左右分欄：左側為欄位篩選控制、右側為查詢操作與結果區 (`src/pages/pages/SearchPage/index.js:70`).

## 載入與欄位準備流程
- 首次 render 透過 `Api.getSearchColNames()` 搭配 `readOntoData` 拉取可搜尋欄位，依 `classtype` 分組並預設全部勾選 (`src/pages/pages/SearchPage/index.js:22`, `src/api/land/Api.js:91`, `src/api/land/Api.js:262`).
- 使用 `Intl.Collator("zh-Hant")` 將群組與欄位名稱排序，確保側欄顯示順序符合語言習慣 (`src/pages/pages/SearchPage/index.js:25`).

## UI 組成與互動
- **篩選欄位面板（ClassifyItem）**：呈現欄位群組與勾選狀態，提供「全選」、「全部取消」、「重設」與欄位關鍵字篩選。Desktop 版可收合為 72px 圖示，Mobile 版改用右側 Drawer (`src/pages/pages/SearchPage/subComponents/ClassifyItem.js:33`, `src/pages/pages/SearchPage/subComponents/ClassifyItem.js:215`, `src/pages/pages/SearchPage/subComponents/ClassifyItem.js:283`, `src/pages/pages/SearchPage/SearchPage.scss:18`).
- **關鍵字輸入（SearchInput）**：以 MUI `TextField` 取得關鍵字，Enter 時才執行查詢。透過自訂 `CustomDebounce` 監控輸入，若關鍵字清空或沒有欄位被勾選，就歸零搜尋結果與狀態 (`src/pages/pages/SearchPage/subComponents/SearchInput.js:14`, `src/hooks/CustomDebounce.js:1`, `src/pages/pages/SearchPage/subComponents/SearchInput.js:19`, `src/pages/pages/SearchPage/subComponents/SearchInput.js:52`).
- **快速清除（ClearAll）**：單鍵清空關鍵字，但保留欄位勾選，並依螢幕寬度調整按鈕填滿或自適應 (`src/pages/pages/SearchPage/subComponents/ClearAll.js:13`).
- **選取摘要（ChipItems）**：以晶片展示每個類別的勾選數量，點擊箭頭會滾動至對應 Accordion 方便調整 (`src/pages/pages/SearchPage/subComponents/ChipItems.js:11`, `src/pages/pages/SearchPage/subComponents/ChipItems.js:43`).
- **結果區塊（ResultField）**：顯示查詢結果、載入指示、空狀態與分頁；一頁固定 5 筆 (`src/pages/pages/SearchPage/subComponents/ResultField.js:27`, `src/pages/pages/SearchPage/subComponents/ResultField.js:114`, `src/Component/CustomPagination/CustomPagination.js:1`).

## 查詢流程與資料處理
- 執行查詢時會蒐集所有 `select` 為 true 的欄位並串成 `ids` 參數，再呼叫 `Api.getPostSearchInfo()` 進行 POST 搜尋；同時標記 loading (`src/pages/pages/SearchPage/subComponents/SearchInput.js:37`, `src/api/land/Api.js:99`).
- API 回傳的 `resultList` 僅含 `cardId`，因此 `ResultField` 會再次呼叫 `Api.getPostLandData()` 取回實際欄位資料，並依 `landName` 分群、照 `landSerialNumber` 排序 (`src/pages/pages/SearchPage/subComponents/ResultField.js:45`, `src/api/land/Api.js:119`, `src/pages/pages/SearchPage/common/index.js:12`).
- 查詢回傳時會同步記錄耗時 `durationSS` 以顯示使用者提示 (`src/pages/pages/SearchPage/subComponents/SearchInput.js:68`, `src/pages/pages/SearchPage/subComponents/ResultField.js:108`).
- 清空關鍵字會重設分頁到第 1 頁並清除暫存資料 (`src/pages/pages/SearchPage/subComponents/ResultField.js:30`, `src/store/reducers/searchPageReducer.js:4`).

## 全域狀態
- `search` slice 管理 `keyWord`, `searchColList`, `resultList`, `searchLoading`, `pageNumber`, `searchDuration` 等欄位，子元件透過 `StoreContext` 派發對應 action (`src/store/reducers/searchPageReducer.js:3`, `src/store/actions.js:36`).
- 分頁控制由 `CustomPagination` 更新 `pageNumber`，並依結果長度計算總頁數 (`src/Component/CustomPagination/CustomPagination.js:11`).

## 權限與導向
- 具有 `Edit` 權限的使用者會看到可連結至 `/Edit?landId=` 的結果標題，否則僅顯示文字 (`src/pages/pages/SearchPage/subComponents/ResultField.js:145`, `src/utils/permissions.js:22`, `src/pages/pages/SearchPage/common/index.js:3`).

## 樣式與響應式
- 側欄使用 `position: sticky` 固定於視窗，桌機寬度上限 300px、手機改為 100% 寬度，並提供收合樣式 (`src/pages/pages/SearchPage/SearchPage.scss:4`, `src/pages/pages/SearchPage/SearchPage.scss:18`, `src/pages/pages/SearchPage/SearchPage.scss:32`).
- 結果容器具 60vh 高度與捲動條客製，確保長清單時仍維持版面 (`src/pages/pages/SearchPage/SearchPage.scss:103`).

## 已知限制與建議
- 目前僅在使用者按 Enter 時才觸發查詢，調整欄位勾選或清除晶片不會自動重新查詢，可能需要額外提示或自動執行 (`src/pages/pages/SearchPage/subComponents/SearchInput.js:99`).
- `fetchPOSTData` 的錯誤訊息未在 UI 呈現，若 API 失敗使用者只會看到空結果，建議補上錯誤處理與提示 (`src/api/land/Api.js:330`, `src/pages/pages/SearchPage/subComponents/ResultField.js:114`).
- `isEmpty` 為泛用工具，對字串與陣列會以 `Object.keys` 判斷，對 `null` 以外的原生型別可能出現非預期結果；必要時可改用更精準的 helper (`src/utils/index.js:12`).
- ClearAll 僅清除關鍵字，不會重設欄位勾選，若使用者期望完全重置需另外使用面板的「重設」按鈕，可考慮在 UI 上明確說明 (`src/pages/pages/SearchPage/subComponents/ClearAll.js:13`, `src/pages/pages/SearchPage/subComponents/ClassifyItem.js:227`).
