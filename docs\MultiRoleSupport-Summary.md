# 多選角色支援說明

## 概述

此專案**已實作多選角色功能**，使用者的 `role` 欄位可以是：
- 單選：`"admin"`, `"editor"`, `"developer"`, `"reader"`, `"anonymous"`
- **多選**：`"admin,editor"`, `"reader,editor,developer"` （逗號分隔，無空格）

## 現有實作

### 1. 多選角色檢查範例

**檔案**：`src/pages/pages/EditPage/common/index.js`

```javascript
export const checkRole = (user, roleArr) => {
  const tmpRoles = user.role.split(",");  // 拆分多選角色

  let disabled = true;
  tmpRoles.forEach((tmpRole) => {
    if (roleArr.includes(tmpRole)) {
      disabled = false;  // 只要有一個角色符合，就允許
    }
  });

  return disabled;
};
```

**檔案**：`src/pages/pages/AuthorityPage/subComponents/ValidUser.js`

```javascript
// 檢查當前用戶是否為 developer
const isCurrentUserDeveloper = currentUser && currentUser.role && 
  currentUser.role.split(',').includes('developer');

// 處理多選角色的新增/移除
const handleClick = (user, role) => {
  const tmpRole = findUser.role.split(",");  // 拆分為陣列
  const normalizedRole = normalizeRoleKey(role);
  
  if (findUser.role.indexOf(normalizedRole) >= 0) {
    // 移除角色
    const tmpIndex = tmpRole.indexOf(normalizedRole);
    tmpRole.splice(tmpIndex, 1);
  } else {
    // 新增角色
    tmpRole.push(normalizedRole);
  }
  
  // 組合回字串
  const newRoleString = tmpRole.join(',');
};
```

---

## 雙角色機制 + 多選角色支援

### 2. 資料結構設計

**Firebase Realtime Database**：

```json
{
  "users": {
    "user001": {
      "uid": "user001",
      "email": "<EMAIL>",
      "displayName": "管理員",
      "role": "admin",                      // 正式站: 單選
      "roleDev": "admin"                    // 測試站: 單選
    },
    "user002": {
      "uid": "user002",
      "email": "<EMAIL>",
      "displayName": "多重角色使用者",
      "role": "reader,editor",              // 正式站: 多選
      "roleDev": "admin,editor,developer"   // 測試站: 多選（更高權限）
    },
    "user003": {
      "uid": "user003",
      "email": "<EMAIL>",
      "displayName": "開發者",
      "role": "developer,editor",           // 正式站: developer + editor
      "roleDev": "developer,editor"         // 測試站: developer + editor
    }
  }
}
```

### 3. 權限判斷邏輯

#### 優先順序規則

```
Admin > Developer (環境相關) > Editor > Reader > Anonymous
```

#### 判斷流程

1. **檢查是否包含 Admin**：
   ```javascript
   if (roles.includes('admin')) {
     return true;  // Admin 有完整權限，忽略其他角色
   }
   ```

2. **檢查是否包含 Developer（環境限制）**：
   ```javascript
   if (roles.includes('developer') && !roles.includes('admin')) {
     if (isProduction()) {
       return false;  // 正式站: developer 不可編輯
     } else {
       return true;   // 測試站: developer 可編輯
     }
   }
   ```

3. **檢查其他角色（取最高權限）**：
   ```javascript
   if (roles.includes('editor')) {
     return canEditOperation;  // Editor 可編輯
   }
   if (roles.includes('reader')) {
     return canViewOperation;  // Reader 僅可查看
   }
   ```

---

## 實作範例

### 4. 環境工具函數

**檔案**：`src/utils/environmentUtils.js`（待建立）

```javascript
/**
 * 根據環境取得使用者的角色陣列（支援多選角色）
 * @param {Object} user - 使用者物件
 * @returns {string[]} 角色陣列，如 ["admin", "editor"]
 */
export const getUserRolesArrayByEnvironment = (user) => {
  if (!user) return ['anonymous'];
  
  // 根據環境選擇 role 或 roleDev
  const roleString = isProduction() 
    ? (user.role || 'anonymous') 
    : (user.roleDev || user.role || 'anonymous');
  
  // 拆分多選角色
  return roleString.split(',').map(r => r.trim()).filter(r => r);
};

/**
 * 檢查使用者是否擁有特定角色（支援多選角色）
 * @param {Object} user - 使用者物件
 * @param {string} targetRole - 要檢查的角色名稱
 * @returns {boolean}
 */
export const hasRole = (user, targetRole) => {
  const roles = getUserRolesArrayByEnvironment(user);
  return roles.includes(targetRole);
};
```

### 5. 權限工具函數

**檔案**：`src/utils/permissionUtils.js`（待建立）

```javascript
import { getUserRolesArrayByEnvironment } from './environmentUtils';
import { isProduction } from './environmentUtils';
import role from '../config/App-role';

/**
 * 檢查使用者是否可以執行特定操作（支援多選角色）
 */
export const canPerformOperation = (user, operation) => {
  if (!user) return false;
  
  // 取得使用者的所有角色（陣列）
  const roles = getUserRolesArrayByEnvironment(user);
  const isProd = isProduction();
  
  // 1. Admin 最高優先
  if (roles.includes(role.admin)) {
    return true;  // Admin 有完整權限
  }
  
  // 2. Developer 環境限制
  if (roles.includes(role.developer)) {
    if (operation === OPERATION.EDIT || 
        operation === OPERATION.DELETE || 
        operation === OPERATION.IMPORT) {
      return !isProd;  // 正式站: false, 測試站: true
    }
    if (operation === OPERATION.VIEW || operation === OPERATION.EXPORT) {
      return true;  // 所有環境都可查看和匯出
    }
    return false;
  }
  
  // 3. Editor 權限
  if (roles.includes(role.editor)) {
    return operation === OPERATION.VIEW || operation === OPERATION.EDIT;
  }
  
  // 4. Reader 權限
  if (roles.includes(role.reader)) {
    return operation === OPERATION.VIEW;
  }
  
  return false;
};
```

---

## 測試案例

### 6. 多選角色權限測試

#### 測試案例 1：Developer + Editor 在正式站

```javascript
// 使用者資料
const user = {
  uid: "test-001",
  role: "developer,editor",       // 正式站: developer + editor
  roleDev: "developer,editor"
};

// 在正式站
process.env.REACT_APP_ENV = 'production';

// 測試結果
const roles = getUserRolesArrayByEnvironment(user);
// 結果: ["developer", "editor"]

const canEdit = canPerformOperation(user, OPERATION.EDIT);
// 結果: false （developer 限制優先於 editor）

const canView = canPerformOperation(user, OPERATION.VIEW);
// 結果: true
```

#### 測試案例 2：Developer + Editor 在測試站

```javascript
// 相同使用者
const user = {
  uid: "test-001",
  role: "developer,editor",
  roleDev: "developer,editor"
};

// 在測試站
process.env.REACT_APP_ENV = 'development';

// 測試結果
const canEdit = canPerformOperation(user, OPERATION.EDIT);
// 結果: true （測試站 developer 可編輯）
```

#### 測試案例 3：Admin + Developer

```javascript
const user = {
  uid: "test-002",
  role: "admin,developer,editor",  // 正式站: admin + developer + editor
  roleDev: "admin,developer"
};

// 在正式站
process.env.REACT_APP_ENV = 'production';

const canEdit = canPerformOperation(user, OPERATION.EDIT);
// 結果: true （admin 權限最高，忽略 developer 限制）

const canImport = canPerformOperation(user, OPERATION.IMPORT);
// 結果: true （admin 可匯入）

const canManageUsers = canPerformOperation(user, OPERATION.MANAGE_USERS);
// 結果: true （僅 admin 可管理使用者）
```

#### 測試案例 4：Reader + Editor

```javascript
const user = {
  uid: "test-003",
  role: "reader,editor",  // reader + editor
  roleDev: "reader,editor"
};

const canEdit = canPerformOperation(user, OPERATION.EDIT);
// 結果: true （有 editor 權限）

const canImport = canPerformOperation(user, OPERATION.IMPORT);
// 結果: false （reader + editor 都不可匯入）
```

---

## 權限矩陣

### 單選角色

| 操作 | admin | developer (正式) | developer (測試) | editor | reader |
|-----|-------|-----------------|-----------------|--------|--------|
| 查看 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 編輯 | ✅ | ❌ | ✅ | ✅ | ❌ |
| 刪除 | ✅ | ❌ | ✅ | ❌ | ❌ |
| 匯入 | ✅ | ❌ | ✅ | ❌ | ❌ |
| 匯出 | ✅ | ✅ | ✅ | ❌ | ❌ |
| 管理使用者 | ✅ | ❌ | ❌ | ❌ | ❌ |

### 多選角色組合

| 操作 | admin+dev | dev+editor (正式) | dev+editor (測試) | reader+editor |
|-----|-----------|------------------|------------------|---------------|
| 查看 | ✅ | ✅ | ✅ | ✅ |
| 編輯 | ✅ (admin優先) | ❌ (dev限制優先) | ✅ | ✅ (editor權限) |
| 刪除 | ✅ (admin優先) | ❌ (dev限制優先) | ✅ | ❌ |
| 匯入 | ✅ (admin優先) | ❌ (dev限制優先) | ✅ | ❌ |
| 匯出 | ✅ | ✅ (dev有匯出權) | ✅ | ❌ |
| 管理使用者 | ✅ (admin優先) | ❌ | ❌ | ❌ |

---

## 關鍵設計決策

### 7. 為什麼 Developer + Editor 在正式站不可編輯？

**原因**：安全優先原則

- Developer 角色在正式站有**明確的編輯限制**
- 即使同時擁有 Editor 角色，Developer 的限制仍然適用
- 避免開發者在正式站誤操作真實資料

**邏輯**：
```javascript
// Developer 限制優先於其他角色
if (roles.includes('developer') && !roles.includes('admin')) {
  // 正式站: developer 的限制適用
  if (isProduction() && operation === OPERATION.EDIT) {
    return false;  // 即使有 editor，仍不可編輯
  }
}
```

### 8. 為什麼 Admin + Developer 可以編輯？

**原因**：Admin 權限最高

- Admin 是最高權限角色，可以覆蓋所有限制
- Admin + Developer：Admin 權限優先

**邏輯**：
```javascript
// Admin 最高優先，忽略所有限制
if (roles.includes('admin')) {
  return true;  // 不檢查 developer 限制
}
```

---

## 實作檢查清單

### Phase 1：基礎架構

- [ ] 建立 `environmentUtils.js`，支援多選角色
  - [ ] `getUserRolesArrayByEnvironment(user)` - 回傳角色陣列
  - [ ] `hasRole(user, targetRole)` - 檢查是否有特定角色
  
- [ ] 建立 `permissionUtils.js`，支援多選角色
  - [ ] `canPerformOperation(user, operation)` - 支援多選角色權限判斷
  - [ ] 實作 Admin 最高優先邏輯
  - [ ] 實作 Developer 環境限制邏輯
  - [ ] 實作其他角色累加邏輯

- [ ] 資料遷移腳本
  - [ ] 自動保留多選角色格式（`"admin,editor"` → `roleDev: "admin,editor"`）

### Phase 4：測試

- [ ] 測試單選角色
- [ ] 測試多選角色權限判斷
  - [ ] Admin + Developer
  - [ ] Developer + Editor (正式站 vs 測試站)
  - [ ] Reader + Editor
- [ ] 測試權限優先順序

---

## 常見問題 (FAQ)

### Q1：為什麼要支援多選角色？

**A**：彈性與現實需求
- 使用者可能需要多種權限組合（如 Reader + Editor）
- 避免建立過多角色定義（如 ReaderEditor, ReaderDeveloper 等）
- 現有系統已實作，保持向後相容

### Q2：多選角色的順序重要嗎？

**A**：不重要
- `"admin,editor"` 與 `"editor,admin"` 效果相同
- 權限判斷邏輯會檢查所有角色，取最高權限

### Q3：如何避免權限衝突？

**A**：明確的優先順序
- Admin 最高優先
- Developer 有環境限制（正式站限制，測試站無限制）
- 其他角色取最高權限（Editor > Reader > Anonymous）

### Q4：Developer + Editor 在正式站為何不可編輯？

**A**：安全優先
- Developer 在正式站的限制是**明確的安全需求**
- 即使有 Editor 角色，Developer 限制仍適用
- 若需要正式站編輯權限，應移除 Developer 角色或提升為 Admin

### Q5：如何測試多選角色邏輯？

**A**：使用測試矩陣
- 參考本文件的「測試案例」章節
- 建立包含各種角色組合的測試使用者
- 在正式站與測試站分別測試

---

## 總結

✅ **現有系統已支援多選角色**  
✅ **使用逗號分隔字串格式**（如 `"admin,editor,developer"`）  
✅ **雙角色機制保留多選格式**（`role` + `roleDev` 都支援多選）  
✅ **權限判斷邏輯**：Admin > Developer（環境相關）> Editor > Reader > Anonymous  
✅ **向後相容**：現有的多選角色邏輯繼續運作  

**下一步**：實作 Phase 1 基礎架構，支援多選角色 + 環境感知權限控制
