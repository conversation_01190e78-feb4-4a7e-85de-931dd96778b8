import React, { useContext, useEffect, useState } from "react";
import { useHistory } from "react-router-dom";

// material ui
import Button from "@mui/material/Button";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../utils";
import Act from "../../../../../store/actions";
import getLandID from "../../../../../utils/getLandID";
import hisAddSearch from "../../../../../utils/hisAddSearch";

function PreButton() {
  const history = useHistory();
  const [state, dispatch] = useContext(StoreContext);
  const {
    landName,
    landSerialNumber,
    selctionIndex,
    LSNList,
    isLock,
    lockUser,
  } = state.edit;
  const { displayName } = state.user;
  const [disable, setDisable] = useState(true);

  useEffect(() => {
    if (
      !isEmpty(landName) &&
      !isEmpty(landSerialNumber) &&
      selctionIndex !== 0
    ) {
      setDisable(false);
    } else {
      setDisable(true);
    }
  }, [landName, landSerialNumber, selctionIndex]);

  return (
    <Button
      className={
        disable || (isLock && displayName === lockUser)
          ? "preBtnDisable"
          : "preBtn"
      }
      disabled={disable || (isLock && displayName === lockUser)}
      onClick={async () => {
        if (selctionIndex - 1 >= 0) {
          const preLSN = LSNList[selctionIndex - 1].landSerialNumber;
          const preID = await getLandID(landName, preLSN);
          hisAddSearch(history, { landId: preID });

          dispatch({
            type: Act.SET_SELECTIONINDEX,
            payload: selctionIndex - 1,
          });
          dispatch({
            type: Act.SET_LANDSERIALNUMBER,
            payload: preLSN,
          });
        }
      }}
    >
      上一筆
    </Button>
  );
}

export default PreButton;
