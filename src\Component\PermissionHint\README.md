# PermissionHint 組件

頁面級權限提示組件，用於在頁面頂部顯示使用者當前的權限狀態和限制。

## 功能特點

- ✅ **自動權限檢查**：使用 Hook 自動判斷使用者權限並生成提示
- ✅ **環境感知**：根據正式站/測試站顯示不同嚴重程度的提示
- ✅ **多種頁面類型**：支援編輯、匯入、管理、查看等不同頁面
- ✅ **Material-UI Alert**：使用 MUI Alert 組件，視覺一致
- ✅ **可自定義**：支援自定義標題、訊息、圖示和樣式

## 基本用法

### 1. 使用 Hook（推薦）

```javascript
import React, { useContext } from 'react';
import PermissionHint from '../../../Component/PermissionHint';
import usePermissionHint from '../../../Component/PermissionHint/usePermissionHint';
import { StoreContext } from '../../../store/StoreProvider';

function EditPage() {
  const [state] = useContext(StoreContext);
  const { user } = state;
  
  // 自動生成編輯頁面的權限提示
  const permissionHint = usePermissionHint(user, 'edit');

  return (
    <div>
      {/* 權限提示 */}
      <PermissionHint
        show={permissionHint.shouldShow}
        title={permissionHint.title}
        message={permissionHint.message}
        severity={permissionHint.severity}
      />
      
      {/* 頁面內容 */}
      <div>Your page content...</div>
    </div>
  );
}
```

### 2. 手動配置

```javascript
import PermissionHint from '../../../Component/PermissionHint';

function MyPage() {
  return (
    <div>
      <PermissionHint
        show={true}
        title="權限限制"
        message="您目前僅能查看資料，無法進行編輯操作。"
        severity="warning"
      />
      
      <div>Your page content...</div>
    </div>
  );
}
```

## Hook API

### usePermissionHint(user, pageType)

自動生成權限提示資訊。

**參數：**
- `user` (Object) - 使用者物件，需包含 `roleDev` 欄位
- `pageType` (String) - 頁面類型，可選值：
  - `'edit'` - 編輯頁面
  - `'import'` - 匯入資料頁面
  - `'manage'` - 使用者管理頁面
  - `'view'` - 查看頁面（預設）

**返回值：**
```javascript
{
  shouldShow: boolean,    // 是否應該顯示提示
  title: string,          // 提示標題
  message: string,        // 提示訊息
  severity: 'info' | 'warning' | 'error'  // 嚴重程度
}
```

### 頁面類型說明

| pageType | 檢查權限 | 適用頁面 |
|----------|---------|---------|
| `'edit'` | `canEdit()` | EditPage, GisPage |
| `'import'` | `canImport()` | ImportDataPage |
| `'manage'` | `canManageUsers()` | AuthorityPage |
| `'view'` | 無限制 | 其他查看頁面 |

## 組件 Props

| Prop | 類型 | 預設值 | 說明 |
|------|------|--------|------|
| `show` | boolean | `true` | 是否顯示提示 |
| `title` | string | `null` | 提示標題（可選） |
| `message` | string | - | 提示訊息內容（必填） |
| `severity` | 'info' \| 'warning' \| 'error' | `'info'` | 嚴重程度 |
| `icon` | ReactNode | `null` | 自定義圖示 |
| `sx` | object | `{}` | 自定義 MUI sx 樣式 |

## 使用範例

### 範例 1：EditPage 權限提示

```javascript
import React, { useContext } from 'react';
import PermissionHint from '../../../Component/PermissionHint';
import usePermissionHint from '../../../Component/PermissionHint/usePermissionHint';
import { StoreContext } from '../../../store/StoreProvider';

function EditPage() {
  const [state] = useContext(StoreContext);
  const { user } = state;
  const permissionHint = usePermissionHint(user, 'edit');

  return (
    <div className="EditPage">
      <PermissionHint
        show={permissionHint.shouldShow}
        title={permissionHint.title}
        message={permissionHint.message}
        severity={permissionHint.severity}
      />
      <ButtonArea />
      <InfoArea />
    </div>
  );
}
```

**顯示效果（Developer 在正式站）：**
```
⚠️ 正式站僅限查看
您的角色（開發者）在正式站僅能查看資料，無法進行編輯操作。如需編輯權限，請聯絡管理員。
```

### 範例 2：ImportDataPage 權限提示

```javascript
const permissionHint = usePermissionHint(user, 'import');

<PermissionHint
  show={permissionHint.shouldShow}
  title={permissionHint.title}
  message={permissionHint.message}
  severity={permissionHint.severity}
/>
```

**顯示效果（Editor 在測試站）：**
```
ℹ️ 權限提示
您的角色（編輯者）目前無法進行匯入資料操作。如需相關權限，請聯絡管理員。
```

### 範例 3：AuthorityPage 權限提示

```javascript
const permissionHint = usePermissionHint(user, 'manage');

<PermissionHint
  show={permissionHint.shouldShow}
  title={permissionHint.title}
  message={permissionHint.message}
  severity={permissionHint.severity}
/>
```

**顯示效果（Developer 在正式站）：**
```
⚠️ 正式站僅限查看
您的角色（開發者）在正式站僅能查看資料，無法進行管理使用者操作。如需編輯權限，請聯絡管理員。
```

### 範例 4：Admin 在正式站的提示

```javascript
const permissionHint = usePermissionHint(user, 'edit');
```

**顯示效果（Admin 在正式站，有權限但仍提示）：**
```
⚠️ 正式站環境
您目前在正式站環境中，擁有編輯權限。請謹慎操作，所有變更將影響實際資料。
```

### 範例 5：自定義樣式

```javascript
<PermissionHint
  show={true}
  title="特別提示"
  message="此頁面正在維護中，部分功能可能無法使用。"
  severity="warning"
  sx={{
    mb: 3,
    borderLeft: '4px solid #ff9800',
  }}
/>
```

### 範例 6：自定義圖示

```javascript
import LockIcon from '@mui/icons-material/Lock';

<PermissionHint
  show={true}
  title="功能鎖定"
  message="此功能需要額外授權才能使用。"
  severity="error"
  icon={<LockIcon fontSize="inherit" />}
/>
```

## 嚴重程度 (Severity) 說明

| Severity | 顏色 | 使用場景 |
|----------|------|---------|
| `info` | 藍色 | 一般提示、測試站權限限制 |
| `warning` | 橙色 | 警告訊息、正式站權限限制、謹慎操作提醒 |
| `error` | 紅色 | 嚴重限制、功能完全不可用 |

## 權限邏輯

### 1. 正式站環境
- **有權限**：顯示 warning 提示「請謹慎操作」
- **無權限**：顯示 warning 提示「僅限查看」

### 2. 測試站環境
- **有權限**：不顯示提示
- **無權限**：顯示 info 提示「無法操作」

### 3. 權限檢查對應

| 頁面類型 | 權限函數 | Admin | Developer | Editor |
|---------|---------|-------|-----------|--------|
| edit | `canEdit()` | ✅ | 測試站✅ 正式站❌ | ✅ |
| import | `canImport()` | ✅ | ✅ | ❌ |
| manage | `canManageUsers()` | ✅ | ❌ | ❌ |
| view | - | ✅ | ✅ | ✅ |

## 最佳實踐

1. **放置位置**：將 PermissionHint 放在頁面內容的最上方
2. **使用 Hook**：優先使用 `usePermissionHint` Hook 自動生成提示
3. **避免重複**：每個頁面只需要一個 PermissionHint
4. **配合 Snackbar**：PermissionHint 用於頁面級提示，Snackbar 用於操作級提示
5. **視覺層級**：
   - PermissionHint：持續顯示的頁面級提示
   - Snackbar：暫時顯示的操作反饋

## 注意事項

1. PermissionHint 僅顯示提示，不會阻止操作（實際權限檢查仍在按鈕 onClick 中）
2. 如果使用者沒有 `roleDev` 欄位，Hook 會返回不顯示
3. `view` 類型頁面通常不需要顯示 PermissionHint
4. 在正式站，即使有權限也會顯示謹慎操作提示（除了 view 類型）
