# 權限管控快速參考卡

**一頁掌握所有權限規則** 📋

---

## 🎯 三種角色

| 角色 | 代碼 | 說明 |
|------|------|------|
| 管理員 | `admin` | 最高權限，可管理所有功能 |
| 開發者 | `developer` | 測試站完整權限，正式站受限 |
| 編輯者 | `editor` | 內容編輯權限，不可匯入資料 |

**多角色格式**: `"admin,developer,editor"`（逗號分隔）

**優先級**: `admin > developer > editor`

---

## 🌍 兩種環境

| 環境 | 代碼 | Badge | 特性 |
|------|------|-------|------|
| 正式站 | `production` | 🔴 紅色 | 嚴格限制 |
| 測試站 | `development` | 🔵 藍色 | 寬鬆測試 |

**配置**: `.env.production` 或 `.env.development`
```bash
REACT_APP_ENV=production  # 或 development
```

---

## 📊 權限矩陣

| 頁面 | Admin | Developer 測試站 | Developer 正式站 | Editor |
|------|-------|----------------|----------------|--------|
| EditPage | ✅ | ✅ | ❌ | ✅ |
| GisPage | ✅ | ✅ | ❌ | ✅ |
| ImportDataPage | ✅ | ✅ | ✅ | ❌ |
| AuthorityPage | ✅ | ❌ | ❌ | ❌ |
| MonitorPage | ✅ | ✅ | ✅ | ✅ |

---

## 💻 快速實作

### 1. 基本權限檢查

```javascript
import { canEdit, getPermissionMessage, OPERATION } from '../../../utils/permissionUtils';
import { StoreContext } from '../../../store/StoreProvider';

function MyComponent() {
  const [state] = useContext(StoreContext);
  const { user } = state;

  const handleSave = () => {
    if (!canEdit(user)) {
      alert(getPermissionMessage(user, OPERATION.EDIT));
      return;
    }
    // 執行儲存
  };

  return <Button onClick={handleSave}>儲存</Button>;
}
```

### 2. 按鈕 Disabled 控制

```javascript
const hasEditPermission = canEdit(user);

<Button 
  disabled={!hasEditPermission}
  onClick={handleSave}
>
  儲存
</Button>
```

### 3. 使用 Snackbar（推薦）

```javascript
import PermissionSnackbar from '../../../Component/PermissionSnackbar';
import usePermissionSnackbar from '../../../Component/PermissionSnackbar/usePermissionSnackbar';

const { snackbarState, showError, hideSnackbar } = usePermissionSnackbar();

const handleSave = () => {
  if (!canEdit(user)) {
    showError(getPermissionMessage(user, OPERATION.EDIT));
    return;
  }
  // 執行儲存
};

<PermissionSnackbar
  open={snackbarState.open}
  onClose={hideSnackbar}
  message={snackbarState.message}
  severity={snackbarState.severity}
/>
```

---

## 📚 常用 API

### 權限檢查函數

```javascript
canEdit(user)           // 編輯權限（EditPage, GisPage）
canImport(user)         // 匯入權限（ImportDataPage）
canManageUsers(user)    // 管理權限（AuthorityPage）
```

### 環境工具

```javascript
getCurrentEnvironment()      // 'production' 或 'development'
getEnvironmentBadge()        // { label, color, bgColor }
getUserRoleDisplayText(user) // "管理員、開發者"
```

### 錯誤訊息

```javascript
getPermissionMessage(user, OPERATION.EDIT)
// "您的角色（開發者）在正式站環境下沒有編輯權限。"

OPERATION.EDIT          // "編輯"
OPERATION.IMPORT        // "匯入資料"
OPERATION.MANAGE_USERS  // "管理使用者"
```

---

## ✅ 實作檢查清單

新增權限控制時：

- [ ] Import `canEdit` 或其他權限函數
- [ ] 取得 `user` 物件
- [ ] 加入權限檢查邏輯
- [ ] 設定按鈕 `disabled` 狀態
- [ ] 顯示權限錯誤訊息（Snackbar 或 alert）
- [ ] 使用 `useMemo` 優化效能
- [ ] 測試不同角色和環境

---

## 🐛 常見錯誤

### ❌ 錯誤：只檢查 UI，不檢查邏輯
```javascript
// 只有 disabled，沒有 onClick 檢查
<Button disabled={!canEdit(user)} onClick={handleSave}>
  儲存
</Button>
```

### ✅ 正確：雙重防護
```javascript
const handleSave = () => {
  // 邏輯檢查（必須）
  if (!canEdit(user)) {
    alert('權限不足');
    return;
  }
  // 執行儲存
};

<Button 
  disabled={!canEdit(user)}  // UI 控制
  onClick={handleSave}
>
  儲存
</Button>
```

---

### ❌ 錯誤：重複檢查權限
```javascript
return (
  <div>
    {canEdit(user) && <Button1 />}
    {canEdit(user) && <Button2 />}
    {canEdit(user) && <Button3 />}
  </div>
);
```

### ✅ 正確：快取結果
```javascript
const hasEditPermission = useMemo(() => canEdit(user), [user]);

return (
  <div>
    {hasEditPermission && <Button1 />}
    {hasEditPermission && <Button2 />}
    {hasEditPermission && <Button3 />}
  </div>
);
```

---

## 🔍 除錯技巧

```javascript
// 檢查使用者資訊
console.log('User:', user);
console.log('Role Dev:', user.roleDev);

// 檢查環境
console.log('Environment:', getCurrentEnvironment());

// 檢查權限結果
console.log('Can edit:', canEdit(user));

// 檢查角色陣列
console.log('Roles:', user.roleDev?.split(','));
```

---

## 📖 完整文檔

詳細說明請參考：
- **[權限管控規則文件](./Permission-Control-Guide.md)** - 完整指南
- **[PermissionSnackbar README](../src/Component/PermissionSnackbar/README.md)** - Snackbar 組件
- **[PermissionHint README](../src/Component/PermissionHint/README.md)** - 權限提示組件

---

**版本**: 1.0.0 | **更新**: 2025-10-02
