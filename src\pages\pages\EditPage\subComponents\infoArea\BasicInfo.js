import React, { useContext, useEffect, useState, useMemo } from "react";
import { useHistory } from "react-router-dom";

// material ui
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../utils";
import { Api, readOntoData } from "../../../../../api/land/Api";
import Act from "../../../../../store/actions";
import { basicPlaceHolder } from "../../common/phDes";
import {
  disableEditFunction,
  disableFontColor,
  getColOrder,
  // landBasicRule,
  validateBasicInfoWithRules,
  setLNAndLSN,
} from "../../common";
import hisAddSearch from "../../../../../utils/hisAddSearch";
import {
  computedColumnKey,
  baseColumnKey,
  getCoordinateFromBasicInfo,
  isValidCoordinate,
  autoDetectPKM,
  actualCoordinateConversion,
  updateBasicInfoField,
  REGION_MAP,
} from "../../common/computedColumnValue";
// components
import CustomLoading from "../../../../../Component/CustomLoading/CustomLoading";

const excludeColumn = ["region"];

function BasicInfo() {
  const history = useHistory();
  const [state, dispatch] = useContext(StoreContext);
  const { edit } = state;
  const { basicInfo, infoLoading, landId, LSNList } = edit;
  const [otherLSNList, setOtherLSNList] = useState([]);

  useEffect(() => {
    if (!landId || isEmpty(LSNList)) {
      setOtherLSNList([]);
      return;
    }
    const tmpList = LSNList.filter((el) => el.landId !== landId);
    setOtherLSNList(tmpList);
  }, [LSNList, landId]);

  useEffect(() => {
    if (isEmpty(landId)) {
      dispatch({
        type: Act.SET_BASICINFO,
        payload: [],
      });
      dispatch({
        type: Act.SET_EXTRABASIC,
        payload: [],
      });
      return;
    }

    const apiStr = Api.getBasicInfoById(landId);

    // 打開Info Loading circle
    dispatch({
      type: Act.SET_INFOLOADING,
      payload: true,
    });

    readOntoData(apiStr).then(async (result) => {
      // console.log(result.data);
      if (!isEmpty(result.data)) {
        const tmpId = result.data[0]?.landId || "";
        // URL紀錄landId
        hisAddSearch(history, { landId: tmpId });
        // update landName and landSerialNumber
        setLNAndLSN(result.data, dispatch);
      }

      const tmpInfo = JSON.parse(JSON.stringify(result.data));

      const colOrder = await getColOrder("Basic");
      const { landId: resLandId } = tmpInfo.find((info) => info.landId);

      const tmpBasicInfo = colOrder
        ?.map((element) => {
          const findValue = tmpInfo.find(
            (info) => info.predicate === element.property
          );
          if (findValue) {
            return {
              landId: resLandId,
              object: element.label,
              value: findValue.value,
              predicate: findValue.predicate,
            };
          }
          return {
            landId: resLandId,
            object: element.label,
            value: "",
            predicate: element.property,
          };
        })
        .concat([
          {
            landId: resLandId,
            object: "區域",
            value: "",
            predicate: "region",
          },
        ]);

      dispatch({
        type: Act.SET_BASICINFO,
        payload: tmpBasicInfo,
      });

      // 其他基本資料
      const extraBasicPredicate = [
        { predicate: "collectionPlace", object: "原典藏地" },
        { predicate: "boxNumber", object: "箱號" },
        { predicate: "oldCadastralMapNumber", object: "舊圖號" },
        { predicate: "oldCategory", object: "舊地目" },
        { predicate: "operator", object: "作業者" },
      ];
      const tmpArr = [];
      extraBasicPredicate.forEach((info) => {
        const findObj = tmpInfo.find(
          (element) => element.predicate === info.predicate
        );
        if (findObj) {
          tmpArr.push(findObj);
        } else {
          tmpArr.push({
            landId: resLandId,
            predicate: info.predicate,
            value: "",
            object: info.object,
          });
        }
      });

      dispatch({
        type: Act.SET_EXTRABASIC,
        payload: tmpArr,
      });

      // 檢查編輯狀態，取得編輯者資料
      const findLockUser = result.data.find(
        (element) => element.predicate === "lockUser"
      );
      dispatch({
        type: Act.SET_ISLOCK,
        payload: !!findLockUser,
      });

      dispatch({
        type: Act.SET_LOCKERUSER,
        payload: findLockUser ? findLockUser.value : "",
      });

      // 關閉Info Loading circle
      dispatch({
        type: Act.SET_INFOLOADING,
        payload: false,
      });
    });
  }, [landId]);

  const handleChange = (event, item) => {
    const tmpBasicInfo = JSON.parse(JSON.stringify(basicInfo));

    // 更新原始值
    const findCol = tmpBasicInfo.find(
      (element) => element.predicate === item.predicate
    );
    if (findCol) {
      findCol.value = event.target.value;

      // 如果是基礎座標欄位，嘗試進行轉換
      if (baseColumnKey[item.predicate]) {
        // 取得目前的 X、Y 值
        const currentX =
          item.predicate === baseColumnKey.twd97_x
            ? event.target.value
            : getCoordinateFromBasicInfo(tmpBasicInfo, baseColumnKey.twd97_x);

        const currentY =
          item.predicate === baseColumnKey.twd97_y
            ? event.target.value
            : getCoordinateFromBasicInfo(tmpBasicInfo, baseColumnKey.twd97_y);

        // 如果 X、Y 都有有效值，進行座標轉換
        if (isValidCoordinate(currentX) && isValidCoordinate(currentY)) {
          // 自動判斷是否為澎金馬地區
          const isPKM = autoDetectPKM(currentX, currentY);

          // 進行座標轉換
          const conversionResult = actualCoordinateConversion(
            currentX,
            currentY,
            isPKM
          );

          if (conversionResult) {
            // 同時更新兩個計算欄位
            updateBasicInfoField(
              tmpBasicInfo,
              computedColumnKey.wgs84_lat,
              conversionResult.lat
            );
            updateBasicInfoField(
              tmpBasicInfo,
              computedColumnKey.wgs84_long,
              conversionResult.long
            );
            // 因應未來可能有外島區域，預設為台灣本島
            updateBasicInfoField(tmpBasicInfo, "region", REGION_MAP[121]);
          }
        }
        // 如果一個沒有值，則將計算欄位清空
        if (!isValidCoordinate(currentX) || !isValidCoordinate(currentY)) {
          updateBasicInfoField(tmpBasicInfo, computedColumnKey.wgs84_lat, "");
          updateBasicInfoField(tmpBasicInfo, computedColumnKey.wgs84_long, "");
          updateBasicInfoField(tmpBasicInfo, "region", "");
        }
      }

      dispatch({
        type: Act.SET_BASICINFO,
        payload: tmpBasicInfo,
      });
    }
  };

  const errors = useMemo(
    () => validateBasicInfoWithRules(basicInfo, otherLSNList),
    [basicInfo, otherLSNList]
  );

  return (
    <Grid item className="BaiscInfo">
      <Grid item container className="BasicItem">
        <Grid item xs={1} className="colName">
          基本資料
        </Grid>
        {infoLoading ? (
          <Grid container>
            <CustomLoading />
          </Grid>
        ) : (
          <Grid item container direction="column" className="BasicinfoArea">
            {basicInfo
              .filter((item) => !excludeColumn.includes(item.predicate))
              .map((item, index) => (
                <Grid item container key={index}>
                  <Grid item xs={2} className="labelName">
                    {item.object}
                  </Grid>
                  <Grid item xs={8}>
                    <TextField
                      disabled={
                        disableEditFunction(state) ||
                        Boolean(computedColumnKey[item.predicate])
                      }
                      variant="standard"
                      fullWidth
                      value={item.value}
                      placeholder={basicPlaceHolder[item.predicate]}
                      onChange={(e) => handleChange(e, item)}
                      multiline
                      inputProps={disableFontColor}
                      error={errors?.[item.predicate]?.check}
                      helperText={errors?.[item.predicate]?.helpText}
                    />
                  </Grid>
                </Grid>
              ))}
          </Grid>
        )}
      </Grid>
    </Grid>
  );
}

export default BasicInfo;
