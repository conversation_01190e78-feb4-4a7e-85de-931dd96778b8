<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Gis" time="145.386" tests="6" failures="2" errors="0">
   <testsuite name="Gis" tests="6" failures="2" errors="0" time="145.386" skipped="0" timestamp="2025-08-19T09:00:29.014Z" hostname="saw63 - host.docker.internal" id="Test Suites/Mandarin/v1/Gis">
      <properties>
         <property name="deviceName" value=""/>
         <property name="devicePlatform"/>
         <property name="logFolder" value="C:\\project\\land-web\\katalon\\Reports\\20250819_170025\\Mandarin\\v1\\Gis\\20250819_170025"/>
         <property name="logFiles" value="C:\\project\\land-web\\katalon\\Reports\\20250819_170025\\Mandarin\\v1\\Gis\\20250819_170025\\console0.log, C:\\project\\land-web\\katalon\\Reports\\20250819_170025\\Mandarin\\v1\\Gis\\20250819_170025\\execution0.log"/>
         <property name="attachments" value="C:\\project\\land-web\\katalon\\Reports\\20250819_170025\\Mandarin\\v1\\Gis\\20250819_170025\\1755594140960.png, C:\\project\\land-web\\katalon\\Reports\\20250819_170025\\Mandarin\\v1\\Gis\\20250819_170025\\1755594174237.png"/>
         <property name="hostName" value="saw63 - host.docker.internal"/>
         <property name="os" value="Windows 11 64bit"/>
         <property name="katalonVersion" value="********"/>
         <property name="browser" value="Chrome 138.0.7204.185"/>
         <property name="userFullName" value="Lin Kai Yee"/>
         <property name="hostAddress" value="************"/>
         <property name="sessionId" value="f4889763a3d355dde04b3373e74a525e"/>
         <property name="projectName" value="katalon"/>
         <property name="seleniumVersion" value="4.28.1"/>
         <property name="proxyInformation" value="ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }"/>
         <property name="platform" value="Windows 11"/>
      </properties>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/GoToGis" time="1.991" classname="Test Cases/Mandarin/v1_web/Gis/GoToGis" status="PASSED">
         <system-out><![CDATA[19-08-2025T17:00:31 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/GoToGis: Test Cases/Mandarin/v1_web/Gis/GoToGis

19-08-2025T17:00:32 - [TEST_STEP][PASSED] - openBrowser(""): Browser is opened with url: ''

19-08-2025T17:00:32 - [MESSAGE][WARNING] - A browser is already opened. Closing browser and opening a new one

19-08-2025T17:00:32 - [MESSAGE][INFO] - Starting 'Chrome' driver

19-08-2025T17:00:32 - [MESSAGE][INFO] - Action delay is set to 0 milliseconds

19-08-2025T17:00:33 - [MESSAGE][PASSED] - Browser is opened with url: ''

19-08-2025T17:00:33 - [TEST_STEP][PASSED] - navigateToUrl("https://land2.daoyidh.com/Gis"): Navigate to 'https://land2.daoyidh.com/Gis' successfully

19-08-2025T17:00:33 - [MESSAGE][PASSED] - Navigate to 'https://land2.daoyidh.com/Gis' successfully]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/LoginWithEmail" time="4.543" classname="Test Cases/Mandarin/v1_web/Gis/LoginWithEmail" status="PASSED">
         <system-out><![CDATA[19-08-2025T17:00:33 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/LoginWithEmail: Test Cases/Mandarin/v1_web/Gis/LoginWithEmail

19-08-2025T17:00:34 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_' is clicked on

19-08-2025T17:00:35 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_' is clicked on

19-08-2025T17:00:35 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail' is clicked on

19-08-2025T17:00:36 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail' is clicked on

19-08-2025T17:00:36 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Email_email"), "<EMAIL>"): Text '<EMAIL>' is set on object 'Object Repository/Mandarin/v1_web/Gis/input_Email_email'

19-08-2025T17:00:36 - [MESSAGE][PASSED] - Text '<EMAIL>' is set on object 'Object Repository/Mandarin/v1_web/Gis/input_Email_email'

19-08-2025T17:00:36 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Next")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_Next' is clicked on

19-08-2025T17:00:37 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_Next' is clicked on

19-08-2025T17:00:37 - [TEST_STEP][PASSED] - setEncryptedText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Password_password"), "0D41p6Ct1VJ35FCiyaw3Gw=="): Text ****** has been set on object 'Object Repository/Mandarin/v1_web/Gis/input_Password_password'

19-08-2025T17:00:38 - [MESSAGE][PASSED] - Text ****** has been set on object 'Object Repository/Mandarin/v1_web/Gis/input_Password_password'

19-08-2025T17:00:38 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign In")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign In' is clicked on

19-08-2025T17:00:38 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign In' is clicked on]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/SearchTextInGis" time="3.46" classname="Test Cases/Mandarin/v1_web/Gis/SearchTextInGis" status="PASSED">
         <system-out><![CDATA[19-08-2025T17:00:38 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/SearchTextInGis: Test Cases/Mandarin/v1_web/Gis/SearchTextInGis

19-08-2025T17:00:38 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button__1")): Object: 'Object Repository/Mandarin/v1_web/Gis/button__1' is clicked on

19-08-2025T17:00:41 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button__1' is clicked on

19-08-2025T17:00:41 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input__mui-3"), "新北勢"): Text '新北勢' is set on object 'Object Repository/Mandarin/v1_web/Gis/input__mui-3'

19-08-2025T17:00:41 - [MESSAGE][PASSED] - Text '新北勢' is set on object 'Object Repository/Mandarin/v1_web/Gis/input__mui-3']]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/ChoosePerPage" time="62.375" classname="Test Cases/Mandarin/v1_web/Gis/ChoosePerPage" status="PASSED">
         <system-out><![CDATA[19-08-2025T17:00:41 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/ChoosePerPage: Test Cases/Mandarin/v1_web/Gis/ChoosePerPage

19-08-2025T17:00:42 - [TEST_STEP][PASSED] - try: null

19-08-2025T17:00:42 - [TEST_STEP][PASSED] - waitForPageLoad(10): Wait for page load successfully

19-08-2025T17:00:42 - [MESSAGE][PASSED] - Wait for page load successfully

19-08-2025T17:00:42 - [TEST_STEP][PASSED] - dropdownButton = new com.kms.katalon.core.testobject.TestObject(): null

19-08-2025T17:00:42 - [TEST_STEP][PASSED] - selectors = ["div[role="combobox"]", "[role="combobox"]", "div[aria-haspopup="listbox"]", ".MuiSelect-root", "div.MuiInputBase-root[role="combobox"]"]: null

19-08-2025T17:00:42 - [TEST_STEP][PASSED] - dropdownFound = false: null

19-08-2025T17:00:42 - [TEST_STEP][PASSED] - for (String selector : selectors): Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:00:42 - [TEST_STEP][PASSED] - try: Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:00:42 - [TEST_STEP][PASSED] - dropdownButton.addProperty("css", EQUALS, selector): null

19-08-2025T17:00:42 - [TEST_STEP][WARNING] - if (waitForElementVisible(dropdownButton, 10)): Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:00:53 - [MESSAGE][INFO] - Unable to find the element located by 'By.cssSelector: div[role="combobox"]'. Please recheck the objects properties to make sure the desired element is located. 

19-08-2025T17:00:53 - [MESSAGE][WARNING] - [SELF-HEALING] Failed to find element with id ''. Try using Self-healing.

19-08-2025T17:00:53 - [MESSAGE][WARNING] - [SELF-HEALING] Index 0 out of bounds for length 0

19-08-2025T17:00:53 - [MESSAGE][WARNING] - [SELF-HEALING] Cannot find elements when the selector is null

19-08-2025T17:00:53 - [MESSAGE][WARNING] - Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:00:53 - [TEST_STEP][PASSED] - try: Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:00:53 - [TEST_STEP][PASSED] - dropdownButton.addProperty("css", EQUALS, selector): null

19-08-2025T17:00:53 - [TEST_STEP][WARNING] - if (waitForElementVisible(dropdownButton, 10)): Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:01:04 - [MESSAGE][INFO] - Unable to find the element located by 'By.cssSelector: div[role="combobox"]'. Please recheck the objects properties to make sure the desired element is located. 

19-08-2025T17:01:04 - [MESSAGE][WARNING] - [SELF-HEALING] Failed to find element with id ''. Try using Self-healing.

19-08-2025T17:01:04 - [MESSAGE][WARNING] - [SELF-HEALING] Index 0 out of bounds for length 0

19-08-2025T17:01:04 - [MESSAGE][WARNING] - [SELF-HEALING] Cannot find elements when the selector is null

19-08-2025T17:01:04 - [MESSAGE][WARNING] - Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:01:04 - [TEST_STEP][PASSED] - try: Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:01:04 - [TEST_STEP][PASSED] - dropdownButton.addProperty("css", EQUALS, selector): null

19-08-2025T17:01:04 - [TEST_STEP][WARNING] - if (waitForElementVisible(dropdownButton, 10)): Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:01:15 - [MESSAGE][INFO] - Unable to find the element located by 'By.cssSelector: div[role="combobox"]'. Please recheck the objects properties to make sure the desired element is located. 

19-08-2025T17:01:15 - [MESSAGE][WARNING] - [SELF-HEALING] Failed to find element with id ''. Try using Self-healing.

19-08-2025T17:01:15 - [MESSAGE][WARNING] - [SELF-HEALING] Index 0 out of bounds for length 0

19-08-2025T17:01:15 - [MESSAGE][WARNING] - [SELF-HEALING] Cannot find elements when the selector is null

19-08-2025T17:01:15 - [MESSAGE][WARNING] - Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:01:15 - [TEST_STEP][PASSED] - try: Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:01:15 - [TEST_STEP][PASSED] - dropdownButton.addProperty("css", EQUALS, selector): null

19-08-2025T17:01:15 - [TEST_STEP][WARNING] - if (waitForElementVisible(dropdownButton, 10)): Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:01:26 - [MESSAGE][INFO] - Unable to find the element located by 'By.cssSelector: div[role="combobox"]'. Please recheck the objects properties to make sure the desired element is located. 

19-08-2025T17:01:26 - [MESSAGE][WARNING] - [SELF-HEALING] Failed to find element with id ''. Try using Self-healing.

19-08-2025T17:01:26 - [MESSAGE][WARNING] - [SELF-HEALING] Index 0 out of bounds for length 0

19-08-2025T17:01:26 - [MESSAGE][WARNING] - [SELF-HEALING] Cannot find elements when the selector is null

19-08-2025T17:01:26 - [MESSAGE][WARNING] - Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:01:26 - [TEST_STEP][PASSED] - try: Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:01:26 - [TEST_STEP][PASSED] - dropdownButton.addProperty("css", EQUALS, selector): null

19-08-2025T17:01:26 - [TEST_STEP][WARNING] - if (waitForElementVisible(dropdownButton, 10)): Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:01:38 - [MESSAGE][INFO] - Unable to find the element located by 'By.cssSelector: div[role="combobox"]'. Please recheck the objects properties to make sure the desired element is located. 

19-08-2025T17:01:38 - [MESSAGE][WARNING] - [SELF-HEALING] Failed to find element with id ''. Try using Self-healing.

19-08-2025T17:01:38 - [MESSAGE][WARNING] - [SELF-HEALING] Index 0 out of bounds for length 0

19-08-2025T17:01:38 - [MESSAGE][WARNING] - [SELF-HEALING] Cannot find elements when the selector is null

19-08-2025T17:01:38 - [MESSAGE][WARNING] - Web element with id: '' located by 'div[role="combobox"]' not found

19-08-2025T17:01:38 - [TEST_STEP][PASSED] - if (!(dropdownFound)): null

19-08-2025T17:01:38 - [TEST_STEP][PASSED] - throw new java.lang.Exception(無法找到下拉選單按鈕): null

19-08-2025T17:01:38 - [TEST_STEP][PASSED] - catch (Exception e): Web element with id: '' located by '[role="combobox"]' not found

19-08-2025T17:01:38 - [TEST_STEP][PASSED] - println(選擇每頁50筆失敗: $e.getMessage()): null

19-08-2025T17:01:38 - [TEST_STEP][PASSED] - try: Web element with id: '' located by '[role="combobox"]' not found

19-08-2025T17:01:38 - [TEST_STEP][PASSED] - println("嘗試備用方案..."): null

19-08-2025T17:01:38 - [TEST_STEP][PASSED] - dropdownBackup = new com.kms.katalon.core.testobject.TestObject(): null

19-08-2025T17:01:38 - [TEST_STEP][PASSED] - dropdownBackup.addProperty("css", EQUALS, "[role="combobox"]"): null

19-08-2025T17:01:38 - [TEST_STEP][WARNING] - if (waitForElementVisible(dropdownBackup, 5)): Web element with id: '' located by '[role="combobox"]' not found

19-08-2025T17:01:44 - [MESSAGE][INFO] - Unable to find the element located by 'By.cssSelector: [role="combobox"]'. Please recheck the objects properties to make sure the desired element is located. 

19-08-2025T17:01:44 - [MESSAGE][WARNING] - [SELF-HEALING] Failed to find element with id ''. Try using Self-healing.

19-08-2025T17:01:44 - [MESSAGE][WARNING] - [SELF-HEALING] Index 0 out of bounds for length 0

19-08-2025T17:01:44 - [MESSAGE][WARNING] - [SELF-HEALING] Cannot find elements when the selector is null

19-08-2025T17:01:44 - [MESSAGE][WARNING] - Web element with id: '' located by '[role="combobox"]' not found]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/EditCoordinates" time="36.826" classname="Test Cases/Mandarin/v1_web/Gis/EditCoordinates" status="FAILED">
         <failure type="FAILED" message="Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.&#xa;Reason:&#xa;com.kms.katalon.core.exception.StepFailedException: Unable to set text '148485' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'&#xa;	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)&#xa;	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)&#xa;	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)&#xa;	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)&#xa;	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)&#xa;	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)&#xa;	at EditCoordinates.run(EditCoordinates:21)&#xa;	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)&#xa;	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)&#xa;	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)&#xa;	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)&#xa;	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)&#xa;	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)&#xa;	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)&#xa;	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)&#xa;	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)&#xa;	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)&#xa;Caused by: com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x' located by 'internal:role=row[name=&quot;新北勢 119-1&quot;i] >> internal:attr=[placeholder=&quot;請輸入經度 (TWD97)&quot;i]' not found&#xa;	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)&#xa;	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)&#xa;	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:26)&#xa;	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.doCall(SetTextKeyword.groovy:53)&#xa;	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.call(SetTextKeyword.groovy)&#xa;	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)&#xa;	... 20 more&#xa;"/>
         <system-out><![CDATA[19-08-2025T17:01:44 - [TEST_CASE][FAILED] - Test Cases/Mandarin/v1_web/Gis/EditCoordinates: Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.
Reason:
com.kms.katalon.core.exception.StepFailedException: Unable to set text '148485' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at EditCoordinates.run(EditCoordinates:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x' located by 'internal:role=row[name="新北勢 119-1"i] >> internal:attr=[placeholder="請輸入經度 (TWD97)"i]' not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:26)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.doCall(SetTextKeyword.groovy:53)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.call(SetTextKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	... 20 more


19-08-2025T17:01:44 - [TEST_STEP][FAILED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "148485"): Unable to set text '148485' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x' (Root cause: com.kms.katalon.core.exception.StepFailedException: Unable to set text '148485' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at EditCoordinates.run(EditCoordinates:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x' located by 'internal:role=row[name="新北勢 119-1"i] >> internal:attr=[placeholder="請輸入經度 (TWD97)"i]' not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:26)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.doCall(SetTextKeyword.groovy:53)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.call(SetTextKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at Script1755487233288.run(Script1755487233288.groovy:21)
	... 15 more
)

19-08-2025T17:02:15 - [MESSAGE][WARNING] - [SELF-HEALING] Failed to find element with id 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'. Try using Self-healing.

19-08-2025T17:02:16 - [MESSAGE][INFO] - Unable to find the element located by 'By.xpath: //input[@id='mui-359']'. Please recheck the objects properties to make sure the desired element is located. 

19-08-2025T17:02:17 - [MESSAGE][INFO] - [SELF-HEALING] Failed Locator: '//input[@id='mui-359']'.

19-08-2025T17:02:18 - [MESSAGE][INFO] - [SELF-HEALING] Failed Locator: '//div[@id='root']/div/div[2]/div/div[3]/div/table/tbody/tr[49]/td[3]/div/div/input'.

19-08-2025T17:02:18 - [MESSAGE][INFO] - [SELF-HEALING] Failed Locator: '//tr[49]/td[3]/div/div/input'.

19-08-2025T17:02:18 - [MESSAGE][INFO] - [SELF-HEALING] Failed Locator: '//input[@placeholder = '請輸入經度 (TWD97)' and @type = 'number' and @id = 'mui-359']'.

19-08-2025T17:02:19 - [MESSAGE][INFO] - Unable to find the element located by 'By.xpath: //*[@placeholder = '請輸入經度 (TWD97)' and @type = 'number' and @id = 'mui-359']'. Please recheck the objects properties to make sure the desired element is located. 

19-08-2025T17:02:20 - [MESSAGE][INFO] - Unable to find the element located by 'By.cssSelector: #mui-359'. Please recheck the objects properties to make sure the desired element is located. 

19-08-2025T17:02:21 - [MESSAGE][FAILED] - Unable to set text '148485' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x' (Root cause: com.kms.katalon.core.exception.StepFailedException: Unable to set text '148485' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at EditCoordinates.run(EditCoordinates:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x' located by 'internal:role=row[name="新北勢 119-1"i] >> internal:attr=[placeholder="請輸入經度 (TWD97)"i]' not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:26)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.doCall(SetTextKeyword.groovy:53)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.call(SetTextKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at Script1755487233288.run(Script1755487233288.groovy:21)
	... 15 more
)
[[ATTACHMENT|C:\\project\\land-web\\katalon\\Reports\\20250819_170025\\Mandarin\\v1\\Gis\\20250819_170025\\1755594140960.png]]]]></system-out>
         <system-err><![CDATA[19-08-2025T17:01:44 - [TEST_CASE][FAILED] - Test Cases/Mandarin/v1_web/Gis/EditCoordinates: Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.
Reason:
com.kms.katalon.core.exception.StepFailedException: Unable to set text '148485' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at EditCoordinates.run(EditCoordinates:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x' located by 'internal:role=row[name="新北勢 119-1"i] >> internal:attr=[placeholder="請輸入經度 (TWD97)"i]' not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:26)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.doCall(SetTextKeyword.groovy:53)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.call(SetTextKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	... 20 more]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/SaveAndVerify" time="33.196" classname="Test Cases/Mandarin/v1_web/Gis/SaveAndVerify" status="FAILED">
         <failure type="FAILED" message="Test Cases/Mandarin/v1_web/Gis/SaveAndVerify FAILED.&#xa;Reason:&#xa;com.kms.katalon.core.exception.StepFailedException: Unable to click on object 'Object Repository/Mandarin/v1_web/Gis/button_save'&#xa;	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)&#xa;	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)&#xa;	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)&#xa;	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)&#xa;	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)&#xa;	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)&#xa;	at SaveAndVerify.run(SaveAndVerify:21)&#xa;	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)&#xa;	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)&#xa;	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)&#xa;	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)&#xa;	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)&#xa;	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)&#xa;	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)&#xa;	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)&#xa;	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)&#xa;	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)&#xa;Caused by: org.openqa.selenium.ElementClickInterceptedException: element click intercepted: Element &lt;button class=&quot;MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium css-1hw9j7s&quot; tabindex=&quot;-1&quot; type=&quot;button&quot; disabled=&quot;&quot;>...&lt;/button> is not clickable at point (851, 831). Other element would receive the click: &lt;div class=&quot;MuiBox-root css-e82o1n&quot;>...&lt;/div>&#xa;  (Session info: chrome=138.0.7204.185)&#xa;Build info: version: '4.28.1', revision: '73f5ad48a2'&#xa;System info: os.name: 'Windows 11', os.arch: 'amd64', os.version: '10.0', java.version: '17.0.14'&#xa;Driver info: com.kms.katalon.selenium.driver.CChromeDriver&#xa;Command: [f4889763a3d355dde04b3373e74a525e, clickElement {id=f.D87F838160F4ECCFD7F37128432F48DC.d.7DC46C5CDC26DD15DDDFBBF23A96792A.e.129}]&#xa;Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.185, chrome: {chromedriverVersion: 138.0.7204.92 (f079b9bc781e..., userDataDir: C:\Users\<USER>\AppData\Loca...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49244}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: windows, proxy: Proxy(), se:cdp: ws://localhost:49244/devtoo..., se:cdpVersion: 138.0.7204.185, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: ignore, webSocketUrl: ws://localhost:34242/sessio..., webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}&#xa;Element: [[CChromeDriver: chrome on windows (f4889763a3d355dde04b3373e74a525e)] -> xpath: (//button[@type='button'])[12]]&#xa;Session ID: f4889763a3d355dde04b3373e74a525e&#xa;	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)&#xa;	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)&#xa;	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)&#xa;	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)&#xa;	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)&#xa;	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)&#xa;	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:215)&#xa;	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:216)&#xa;	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:174)&#xa;	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)&#xa;	at org.openqa.selenium.remote.RemoteWebElement.execute(RemoteWebElement.java:223)&#xa;	at org.openqa.selenium.remote.RemoteWebElement.click(RemoteWebElement.java:76)&#xa;	at jdk.internal.reflect.GeneratedMethodAccessor26.invoke(Unknown Source)&#xa;	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)&#xa;	at org.openqa.selenium.support.decorators.WebDriverDecorator.call(WebDriverDecorator.java:315)&#xa;	at org.openqa.selenium.support.decorators.DefaultDecorated.call(DefaultDecorated.java:48)&#xa;	at org.openqa.selenium.support.decorators.WebDriverDecorator.lambda$createProxyFactory$3(WebDriverDecorator.java:405)&#xa;	at net.bytebuddy.renamed.java.lang.Object$ByteBuddy$mlaGwFki.click(Unknown Source)&#xa;	at org.openqa.selenium.WebElement$click.call(Unknown Source)&#xa;	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy:82)&#xa;	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy)&#xa;	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)&#xa;	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)&#xa;	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)&#xa;	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:68)&#xa;	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)&#xa;	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)&#xa;	... 20 more&#xa;"/>
         <system-out><![CDATA[19-08-2025T17:02:21 - [TEST_CASE][FAILED] - Test Cases/Mandarin/v1_web/Gis/SaveAndVerify: Test Cases/Mandarin/v1_web/Gis/SaveAndVerify FAILED.
Reason:
com.kms.katalon.core.exception.StepFailedException: Unable to click on object 'Object Repository/Mandarin/v1_web/Gis/button_save'
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at SaveAndVerify.run(SaveAndVerify:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: org.openqa.selenium.ElementClickInterceptedException: element click intercepted: Element <button class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium css-1hw9j7s" tabindex="-1" type="button" disabled="">...</button> is not clickable at point (851, 831). Other element would receive the click: <div class="MuiBox-root css-e82o1n">...</div>
  (Session info: chrome=138.0.7204.185)
Build info: version: '4.28.1', revision: '73f5ad48a2'
System info: os.name: 'Windows 11', os.arch: 'amd64', os.version: '10.0', java.version: '17.0.14'
Driver info: com.kms.katalon.selenium.driver.CChromeDriver
Command: [f4889763a3d355dde04b3373e74a525e, clickElement {id=f.D87F838160F4ECCFD7F37128432F48DC.d.7DC46C5CDC26DD15DDDFBBF23A96792A.e.129}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.185, chrome: {chromedriverVersion: 138.0.7204.92 (f079b9bc781e..., userDataDir: C:\Users\<USER>\AppData\Loca...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49244}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: windows, proxy: Proxy(), se:cdp: ws://localhost:49244/devtoo..., se:cdpVersion: 138.0.7204.185, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: ignore, webSocketUrl: ws://localhost:34242/sessio..., webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[CChromeDriver: chrome on windows (f4889763a3d355dde04b3373e74a525e)] -> xpath: (//button[@type='button'])[12]]
Session ID: f4889763a3d355dde04b3373e74a525e
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:215)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:216)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:174)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)
	at org.openqa.selenium.remote.RemoteWebElement.execute(RemoteWebElement.java:223)
	at org.openqa.selenium.remote.RemoteWebElement.click(RemoteWebElement.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor26.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.call(WebDriverDecorator.java:315)
	at org.openqa.selenium.support.decorators.DefaultDecorated.call(DefaultDecorated.java:48)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.lambda$createProxyFactory$3(WebDriverDecorator.java:405)
	at net.bytebuddy.renamed.java.lang.Object$ByteBuddy$mlaGwFki.click(Unknown Source)
	at org.openqa.selenium.WebElement$click.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy:82)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:68)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	... 20 more


19-08-2025T17:02:21 - [TEST_STEP][FAILED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_save")): Unable to click on object 'Object Repository/Mandarin/v1_web/Gis/button_save' (Root cause: com.kms.katalon.core.exception.StepFailedException: Unable to click on object 'Object Repository/Mandarin/v1_web/Gis/button_save'
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at SaveAndVerify.run(SaveAndVerify:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: org.openqa.selenium.ElementClickInterceptedException: element click intercepted: Element <button class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium css-1hw9j7s" tabindex="-1" type="button" disabled="">...</button> is not clickable at point (851, 831). Other element would receive the click: <div class="MuiBox-root css-e82o1n">...</div>
  (Session info: chrome=138.0.7204.185)
Build info: version: '4.28.1', revision: '73f5ad48a2'
System info: os.name: 'Windows 11', os.arch: 'amd64', os.version: '10.0', java.version: '17.0.14'
Driver info: com.kms.katalon.selenium.driver.CChromeDriver
Command: [f4889763a3d355dde04b3373e74a525e, clickElement {id=f.D87F838160F4ECCFD7F37128432F48DC.d.7DC46C5CDC26DD15DDDFBBF23A96792A.e.129}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.185, chrome: {chromedriverVersion: 138.0.7204.92 (f079b9bc781e..., userDataDir: C:\Users\<USER>\AppData\Loca...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49244}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: windows, proxy: Proxy(), se:cdp: ws://localhost:49244/devtoo..., se:cdpVersion: 138.0.7204.185, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: ignore, webSocketUrl: ws://localhost:34242/sessio..., webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[CChromeDriver: chrome on windows (f4889763a3d355dde04b3373e74a525e)] -> xpath: (//button[@type='button'])[12]]
Session ID: f4889763a3d355dde04b3373e74a525e
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:215)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:216)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:174)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)
	at org.openqa.selenium.remote.RemoteWebElement.execute(RemoteWebElement.java:223)
	at org.openqa.selenium.remote.RemoteWebElement.click(RemoteWebElement.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor26.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.call(WebDriverDecorator.java:315)
	at org.openqa.selenium.support.decorators.DefaultDecorated.call(DefaultDecorated.java:48)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.lambda$createProxyFactory$3(WebDriverDecorator.java:405)
	at net.bytebuddy.renamed.java.lang.Object$ByteBuddy$mlaGwFki.click(Unknown Source)
	at org.openqa.selenium.WebElement$click.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy:82)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:68)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at Script1755487289260.run(Script1755487289260.groovy:21)
	... 15 more
)

19-08-2025T17:02:54 - [MESSAGE][FAILED] - Unable to click on object 'Object Repository/Mandarin/v1_web/Gis/button_save' (Root cause: com.kms.katalon.core.exception.StepFailedException: Unable to click on object 'Object Repository/Mandarin/v1_web/Gis/button_save'
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at SaveAndVerify.run(SaveAndVerify:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: org.openqa.selenium.ElementClickInterceptedException: element click intercepted: Element <button class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium css-1hw9j7s" tabindex="-1" type="button" disabled="">...</button> is not clickable at point (851, 831). Other element would receive the click: <div class="MuiBox-root css-e82o1n">...</div>
  (Session info: chrome=138.0.7204.185)
Build info: version: '4.28.1', revision: '73f5ad48a2'
System info: os.name: 'Windows 11', os.arch: 'amd64', os.version: '10.0', java.version: '17.0.14'
Driver info: com.kms.katalon.selenium.driver.CChromeDriver
Command: [f4889763a3d355dde04b3373e74a525e, clickElement {id=f.D87F838160F4ECCFD7F37128432F48DC.d.7DC46C5CDC26DD15DDDFBBF23A96792A.e.129}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.185, chrome: {chromedriverVersion: 138.0.7204.92 (f079b9bc781e..., userDataDir: C:\Users\<USER>\AppData\Loca...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49244}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: windows, proxy: Proxy(), se:cdp: ws://localhost:49244/devtoo..., se:cdpVersion: 138.0.7204.185, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: ignore, webSocketUrl: ws://localhost:34242/sessio..., webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[CChromeDriver: chrome on windows (f4889763a3d355dde04b3373e74a525e)] -> xpath: (//button[@type='button'])[12]]
Session ID: f4889763a3d355dde04b3373e74a525e
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:215)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:216)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:174)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)
	at org.openqa.selenium.remote.RemoteWebElement.execute(RemoteWebElement.java:223)
	at org.openqa.selenium.remote.RemoteWebElement.click(RemoteWebElement.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor26.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.call(WebDriverDecorator.java:315)
	at org.openqa.selenium.support.decorators.DefaultDecorated.call(DefaultDecorated.java:48)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.lambda$createProxyFactory$3(WebDriverDecorator.java:405)
	at net.bytebuddy.renamed.java.lang.Object$ByteBuddy$mlaGwFki.click(Unknown Source)
	at org.openqa.selenium.WebElement$click.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy:82)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:68)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at Script1755487289260.run(Script1755487289260.groovy:21)
	... 15 more
)
[[ATTACHMENT|C:\\project\\land-web\\katalon\\Reports\\20250819_170025\\Mandarin\\v1\\Gis\\20250819_170025\\1755594174237.png]]]]></system-out>
         <system-err><![CDATA[19-08-2025T17:02:21 - [TEST_CASE][FAILED] - Test Cases/Mandarin/v1_web/Gis/SaveAndVerify: Test Cases/Mandarin/v1_web/Gis/SaveAndVerify FAILED.
Reason:
com.kms.katalon.core.exception.StepFailedException: Unable to click on object 'Object Repository/Mandarin/v1_web/Gis/button_save'
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at SaveAndVerify.run(SaveAndVerify:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: org.openqa.selenium.ElementClickInterceptedException: element click intercepted: Element <button class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium css-1hw9j7s" tabindex="-1" type="button" disabled="">...</button> is not clickable at point (851, 831). Other element would receive the click: <div class="MuiBox-root css-e82o1n">...</div>
  (Session info: chrome=138.0.7204.185)
Build info: version: '4.28.1', revision: '73f5ad48a2'
System info: os.name: 'Windows 11', os.arch: 'amd64', os.version: '10.0', java.version: '17.0.14'
Driver info: com.kms.katalon.selenium.driver.CChromeDriver
Command: [f4889763a3d355dde04b3373e74a525e, clickElement {id=f.D87F838160F4ECCFD7F37128432F48DC.d.7DC46C5CDC26DD15DDDFBBF23A96792A.e.129}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.185, chrome: {chromedriverVersion: 138.0.7204.92 (f079b9bc781e..., userDataDir: C:\Users\<USER>\AppData\Loca...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49244}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: windows, proxy: Proxy(), se:cdp: ws://localhost:49244/devtoo..., se:cdpVersion: 138.0.7204.185, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: ignore, webSocketUrl: ws://localhost:34242/sessio..., webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[CChromeDriver: chrome on windows (f4889763a3d355dde04b3373e74a525e)] -> xpath: (//button[@type='button'])[12]]
Session ID: f4889763a3d355dde04b3373e74a525e
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:215)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:216)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:174)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)
	at org.openqa.selenium.remote.RemoteWebElement.execute(RemoteWebElement.java:223)
	at org.openqa.selenium.remote.RemoteWebElement.click(RemoteWebElement.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor26.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.call(WebDriverDecorator.java:315)
	at org.openqa.selenium.support.decorators.DefaultDecorated.call(DefaultDecorated.java:48)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.lambda$createProxyFactory$3(WebDriverDecorator.java:405)
	at net.bytebuddy.renamed.java.lang.Object$ByteBuddy$mlaGwFki.click(Unknown Source)
	at org.openqa.selenium.WebElement$click.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy:82)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:68)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	... 20 more]]></system-err>
      </testcase>
      <system-out><![CDATA[19-08-2025T17:00:29 - [TEST_STEP][PASSED] - Start listener action : beforeSuite: Current window maximized

19-08-2025T17:00:29 - [TEST_STEP][PASSED] - openBrowser(""): Browser is opened with url: ''

19-08-2025T17:00:29 - [MESSAGE][INFO] - Starting 'Chrome' driver

19-08-2025T17:00:29 - [MESSAGE][INFO] - Action delay is set to 0 milliseconds

19-08-2025T17:00:31 - [MESSAGE][PASSED] - Browser is opened with url: ''

19-08-2025T17:00:31 - [TEST_STEP][PASSED] - maximizeWindow(): Current window maximized

19-08-2025T17:00:31 - [MESSAGE][PASSED] - Current window maximized

19-08-2025T17:00:29 - [TEST_SUITE][FAILED] - Gis: null

19-08-2025T17:02:54 - [TEST_STEP][PASSED] - Start listener action : afterSuite: Browser is closed

19-08-2025T17:02:54 - [TEST_STEP][PASSED] - closeBrowser(): Browser is closed

19-08-2025T17:02:54 - [MESSAGE][PASSED] - Browser is closed]]></system-out>
      <system-err><![CDATA[19-08-2025T17:00:29 - [TEST_SUITE][FAILED] - Gis: null]]></system-err>
   </testsuite>
</testsuites>
