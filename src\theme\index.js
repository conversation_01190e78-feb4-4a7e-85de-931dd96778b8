import { createTheme } from '@mui/material/styles';

// 從 common.scss 導入的顏色常數
const colors = {
  primary: '#008cba',
  primaryHover: '#0076a5',
  accent: '#1abc9c',
  danger: '#f44336',
  surface: '#ffffff',
  surfaceAlt: '#f5f7fa',
  background: '#eef3f7',
  border: '#d5e3eb',
  borderStrong: '#b7c9d6',
  textPrimary: '#1f2a37',
  textSecondary: '#4b5563',
  textMuted: '#6b7280',
  primaryTint: '#e6f4fb',
  accentTint: '#d9f3ec',
};

// 創建自定義主題
const theme = createTheme({
  // 調色盤配置
  palette: {
    // 主要顏色 - 使用 common.scss 中的藍色系
    primary: {
      main: colors.primary,
      light: colors.primaryTint,
      dark: colors.primaryHover,
      contrastText: '#ffffff',
    },
    // 次要顏色 - 使用 accent 顏色而非預設紫色
    secondary: {
      main: colors.accent,
      light: colors.accentTint,
      dark: '#16a085', // accent 的深色版本
      contrastText: '#ffffff',
    },
    // 錯誤顏色
    error: {
      main: colors.danger,
      light: '#ffebee',
      dark: '#c62828',
      contrastText: '#ffffff',
    },
    // 背景顏色
    background: {
      default: colors.background,
      paper: colors.surface,
    },
    // 文字顏色
    text: {
      primary: colors.textPrimary,
      secondary: colors.textSecondary,
      disabled: colors.textMuted,
    },
    // 分隔線顏色
    divider: colors.border,
    // 動作顏色
    action: {
      hover: 'rgba(0, 0, 0, 0.04)',
      selected: 'rgba(0, 140, 186, 0.08)',
      disabled: colors.textMuted,
      disabledBackground: 'rgba(0, 0, 0, 0.12)',
    },
  },
  
  // typography 配置
  typography: {
    fontFamily: '"Noto Sans TC", sans-serif',
    h1: {
      fontWeight: 700,
      color: colors.textPrimary,
    },
    h2: {
      fontWeight: 600,
      color: colors.textPrimary,
    },
    h3: {
      fontWeight: 600,
      color: colors.textPrimary,
    },
    h4: {
      fontWeight: 600,
      color: colors.textPrimary,
    },
    h5: {
      fontWeight: 600,
      color: colors.textPrimary,
    },
    h6: {
      fontWeight: 600,
      color: colors.textPrimary,
    },
    body1: {
      color: colors.textPrimary,
    },
    body2: {
      color: colors.textSecondary,
    },
    caption: {
      color: colors.textSecondary,
    },
  },
  
  // 形狀配置
  shape: {
    borderRadius: 8,
  },
  
  // 陰影配置
  shadows: [
    'none',
    '0 16px 32px rgba(15, 23, 42, 0.12)', // surface shadow
    '0 2px 8px rgba(0, 0, 0, 0.12)', // hover shadow
    '0 4px 16px rgba(0, 0, 0, 0.15)',
    '0 8px 24px rgba(0, 0, 0, 0.18)',
    '0 12px 32px rgba(0, 0, 0, 0.20)',
    // ... 其他陰影層級使用 MUI 預設值
    ...createTheme().shadows.slice(6),
  ],
  
  // 元件樣式覆蓋
  components: {
    // Button 組件
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none', // 不轉換為大寫
          borderRadius: 8,
          fontWeight: 600,
          padding: '8px 16px',
        },
        containedPrimary: {
          backgroundColor: colors.primary,
          color: '#ffffff',
          '&:hover': {
            backgroundColor: colors.primaryHover,
          },
        },
        containedSecondary: {
          backgroundColor: colors.accent,
          color: '#ffffff',
          '&:hover': {
            backgroundColor: '#16a085',
          },
        },
        outlinedPrimary: {
          borderColor: colors.primary,
          color: colors.primary,
          '&:hover': {
            borderColor: colors.primaryHover,
            backgroundColor: colors.primaryTint,
          },
        },
        outlinedSecondary: {
          borderColor: colors.accent,
          color: colors.accent,
          '&:hover': {
            borderColor: '#16a085',
            backgroundColor: colors.accentTint,
          },
        },
        textSecondary: {
          color: colors.accent, // 使用 accent 而非預設紫色
          '&:hover': {
            backgroundColor: colors.accentTint,
          },
        },
      },
    },
    
    // Chip 組件
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 6,
        },
        outlinedPrimary: {
          color: colors.primary,
          borderColor: colors.primary,
        },
        outlinedSecondary: {
          color: colors.accent,
          borderColor: colors.accent,
        },
      },
    },
    
    // Paper 組件
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundColor: colors.surface,
          borderRadius: 12,
        },
      },
    },
    
    // Accordion 組件
    MuiAccordion: {
      styleOverrides: {
        root: {
          borderRadius: '8px !important',
          border: `1px solid ${colors.border}`,
          '&:before': {
            display: 'none',
          },
          '&.Mui-expanded': {
            marginBottom: '8px !important',
          },
        },
      },
    },
    
    // TextField 組件
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
          },
        },
      },
    },
    
    // Drawer 組件
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: colors.surface,
        },
      },
    },
  },
});

export default theme;