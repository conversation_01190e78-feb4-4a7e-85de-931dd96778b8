import React, { useContext, useEffect, useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';

// material ui
import { Box, Grid, Typography, Paper, Stack, Divider, Link as MuiLink } from '@mui/material';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import AssessmentIcon from '@mui/icons-material/Assessment';
import SearchIcon from '@mui/icons-material/Search';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import VisibilityIcon from '@mui/icons-material/Visibility';
import DescriptionIcon from '@mui/icons-material/Description';
import LandscapeIcon from '@mui/icons-material/Landscape';
import ArticleIcon from '@mui/icons-material/Article';

// utils
import { StoreContext } from '../../../../store/StoreProvider';
import { isEmpty } from '../../../../utils';
import { Api, fetchPOSTData } from '../../../../api/land/Api';
import { returnHref, sortingNumberWithBar } from '../common';
import { canAccessEdit } from '../../../../utils/permissions';

// component
import CustomLoading from '../../../../Component/CustomLoading/CustomLoading';
import CustomPagination from '../../../../Component/CustomPagination/CustomPagination';
import Act from '../../../../store/actions';

function ResultField() {
  const [state, dispatch] = useContext(StoreContext);
  const { resultList, searchLoading, pageNumber, keyWord, searchDuration } = state.search;
  const { user } = state;
  const { role } = user;
  const canEdit = canAccessEdit(role);
  const pageShowCount = 5;
  const [allData, setAllData] = useState([]);

  useEffect(() => {
    if (isEmpty(keyWord)) {
      setAllData([]);
      dispatch({
        type: Act.SET_PAGENUMBER,
        payload: 1,
      });
    }
  }, [keyWord]);

  useEffect(() => {
    if (isEmpty(resultList)) {
      setAllData([]);
      return;
    }
    const tmpList = resultList.map((element) => element.cardId);
    const apiStr = Api.getPostLandData();
    fetchPOSTData({
      apiStr,
      entry: {
        ids: tmpList.join(),
      },
    }).then((result) => {
      const sourceData = Array.isArray(result.data) ? result.data : [];
      // 根據resultList的土地編號順序取出資料顯示
      const mainCol = ['landName', 'landSerialNumber'];
      const tmpResult = tmpList.map((id) => {
        let tmpData = sourceData.filter((element) => element.landId === id);
        tmpData = tmpData.reduce((cur, next) => {
          if (mainCol.includes(next.predicate)) {
            return { ...cur, [next.predicate]: next.value };
          }
          if (cur.data) {
            return {
              ...cur,
              data: [...cur.data, { value: next.value, object: next.object }],
            };
          }
          return {
            ...cur,
            data: [{ value: next.value, object: next.object }],
          };
        }, {});
        const ensuredData = tmpData.data ? tmpData.data : [];
        return { ...tmpData, data: ensuredData, landId: id };
      });

      // 根據landName整理一起
      const allLandName = tmpResult
        .map((element) => element.landName)
        .filter((element, pos) => tmpResult.findIndex((item) => item.landName === element) === pos);

      let tmpAllData = [];
      allLandName.forEach((LMName) => {
        let tmpArr = tmpResult.filter((element) => element.landName === LMName);
        // 依照landSerialNumber排列
        tmpArr = tmpArr.sort((cur, next) => sortingNumberWithBar(cur, next));
        tmpAllData = [...tmpAllData, ...tmpArr];
      });
      setAllData(tmpAllData);
    });
  }, [resultList]);

  const offset = pageShowCount * (pageNumber - 1);
  const pagedData = allData.filter(
    (element, index) => index >= offset && index < pageShowCount * pageNumber,
  );

  const trimmedKeyword = (keyWord || '').trim();
  const hasKeyword = !isEmpty(trimmedKeyword);
  const hasResults = !isEmpty(resultList) && pagedData.length > 0;
  const showEmptyState = hasKeyword && !searchLoading && isEmpty(resultList);
  const showNoDataForQuery = hasKeyword && !searchLoading && !isEmpty(resultList) && pagedData.length === 0;
  const showWelcomeState = !hasKeyword && !searchLoading && isEmpty(resultList);

  return (
    <Grid item xs={12} sx={{ padding: 2 }}>
      {hasKeyword && !searchLoading && !isEmpty(resultList) && (
        <Box 
          sx={{ 
            ml: 0, 
            mb: 2,
            p: 2,
            backgroundColor: 'var(--color-surface-variant)',
            borderRadius: 2,
            border: '1px solid var(--color-border-light)'
          }}
        >
          <Typography 
            color="var(--color-text-secondary)"
            sx={{ 
              display: 'flex', 
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: { xs: 'flex-start', sm: 'center' }, 
              gap: { xs: 0.5, sm: 1 },
              fontSize: { xs: '0.85rem', sm: '0.95rem' },
              fontWeight: 500,
              lineHeight: 1.4
            }}
          >
            <Box component="span" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <AssessmentIcon sx={{ fontSize: '1.2em', color: '#2196f3' }} color="inherit" />
              關鍵字「<strong style={{ color: 'var(--color-primary)' }}>{trimmedKeyword}</strong>」
            </Box>
            <Box component="span" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              查得 <strong style={{ color: 'var(--color-primary)' }}>{resultList.length}</strong> 筆資料
              {searchDuration && (
                <span style={{ color: 'var(--color-text-muted)', fontSize: '0.85em' }}>
                  （耗時 {Number(searchDuration).toFixed(2)} 秒）
                </span>
              )}
            </Box>
          </Typography>
        </Box>
      )}

      {searchLoading && (
        <Box sx={{ py: 6 }}>
          <CustomLoading />
        </Box>
      )}

      {!searchLoading && hasResults && (
        <Stack spacing={2} sx={{ mt: 2 }}>
          {pagedData.map((element, index) => (
            <Paper
              key={`${element.landId || index}-${index}`}
              sx={{
                border: '1px solid var(--color-border)',
                borderRadius: '12px',
                boxShadow: 'var(--shadow-surface)',
                backgroundColor: 'var(--color-surface)',
                p: { xs: 2, sm: 2.5, md: 3 },
                mx: { xs: 1, md: 0 },
              }}
            >
              <Stack spacing={2}>
                <Stack
                  direction={{ xs: 'column', md: 'row' }}
                  spacing={1.5}
                  alignItems={{ xs: 'flex-start', md: 'center' }}
                  justifyContent="space-between"
                >
                  <Stack direction="row" spacing={1.5} alignItems="center">
                    <Typography variant="caption" color="var(--color-text-muted)">
                      #{offset + index + 1}
                    </Typography>
                    {canEdit ? (
                      <MuiLink
                        component={RouterLink}
                        to={returnHref(element)}
                        target="_blank"
                        rel="noopener noreferrer"
                        underline="hover"
                        sx={{ display: 'inline-flex', alignItems: 'center' }}
                        color="primary"
                      >
                        <Typography variant="h6" color="primary" sx={{ mr: 0.5 }}>
                          {`[${element.landName}] - [${element.landSerialNumber}]`}
                        </Typography>
                        <OpenInNewIcon fontSize="small" />
                      </MuiLink>
                    ) : (
                      <Typography variant="h6" color="var(--color-text-muted)">
                        {`[${element.landName}] - [${element.landSerialNumber}]`}
                      </Typography>
                    )}
                  </Stack>
                </Stack>

                <Divider />

                <Grid container spacing={1.5} sx={{ mt: 1 }}>
                  {(element.data || []).map((item, dataIndex) => (
                    <Grid
                      item
                      xs={12}
                      sm={6}
                      md={4}
                      key={`${element.landId || index}-${dataIndex}`}
                    >
                      <Box
                        sx={{
                          p: 1.5,
                          backgroundColor: { xs: 'transparent', sm: 'var(--color-surface-variant)' },
                          borderRadius: { xs: 0, sm: 1 },
                          border: { xs: 'none', sm: '1px solid var(--color-border-light)' },
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          minHeight: '60px',
                        }}
                      >
                        <Typography 
                          variant="subtitle2" 
                          color="var(--color-text-secondary)"
                          sx={{ 
                            fontWeight: 600,
                            fontSize: '0.875rem',
                            mb: 0.5,
                            lineHeight: 1.2,
                          }}
                        >
                          {item.object}
                        </Typography>
                        <Typography
                          variant="body1"
                          color="var(--color-text-primary)"
                          sx={{ 
                            fontSize: '0.95rem',
                            fontWeight: 500,
                            flex: 1,
                            wordBreak: 'break-word',
                            lineHeight: 1.4,
                          }}
                        >
                          {item.value || '--'}
                        </Typography>
                      </Box>
                    </Grid>
                  ))}
                  {isEmpty(element.data) && (
                    <Grid item xs={12}>
                      <Typography 
                        color="var(--color-text-muted)" 
                        sx={{ textAlign: 'center', py: 2, fontStyle: 'italic' }}
                      >
                        尚無可顯示的欄位資料
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </Stack>
            </Paper>
          ))}
        </Stack>
      )}

      {/* 搜尋無結果 */}
      {showEmptyState && (
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            justifyContent: 'center', 
            py: 8, 
            px: 4,
            textAlign: 'center'
          }}
        >
          <Typography 
            variant="h5" 
            color="var(--color-text-muted)" 
            sx={{ 
              mb: 2, 
              fontWeight: 500,
              fontSize: { xs: '1.25rem', sm: '1.5rem' }
            }}
          >
            <Box component="span" sx={{ display: 'flex', alignItems: 'center', gap: 1, justifyContent: 'center' }}>
              <SearchIcon sx={{ fontSize: '1.5rem', color: '#6b7280' }} color="inherit" />
              找不到相關資料
            </Box>
          </Typography>
          <Typography 
            variant="body1" 
            color="var(--color-text-secondary)" 
            sx={{ 
              mb: 3, 
              maxWidth: 400, 
              lineHeight: 1.6,
              fontSize: { xs: '0.9rem', sm: '1rem' },
              px: { xs: 1, sm: 0 }
            }}
          >
            <Box component="span" sx={{ display: { xs: 'block', sm: 'inline' } }}>
              關鍵字「<strong>{trimmedKeyword}</strong>」
            </Box>
            <Box component="span" sx={{ display: { xs: 'block', sm: 'inline' } }}>
              沒有找到符合的土地資料
            </Box>
          </Typography>
          <Box sx={{ textAlign: 'left', maxWidth: { xs: 300, sm: 400 } }}>
            <Typography 
              variant="body2" 
              color="var(--color-text-muted)" 
              sx={{ 
                mb: 1,
                fontSize: { xs: '0.85rem', sm: '0.9rem' },
                fontWeight: 500
              }}
            >
              <Box component="span" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <LightbulbIcon sx={{ fontSize: '1.1rem', color: '#ff9800' }} color="inherit" />
                建議您：
              </Box>
            </Typography>
            <Typography 
              variant="body2" 
              color="var(--color-text-secondary)" 
              sx={{ 
                ml: { xs: 1, sm: 2 }, 
                mb: 0.5,
                fontSize: { xs: '0.8rem', sm: '0.875rem' },
                lineHeight: 1.4
              }}
            >
              • 檢查拼寫是否正確
            </Typography>
            <Typography 
              variant="body2" 
              color="var(--color-text-secondary)" 
              sx={{ 
                ml: { xs: 1, sm: 2 }, 
                mb: 0.5,
                fontSize: { xs: '0.8rem', sm: '0.875rem' },
                lineHeight: 1.4
              }}
            >
              • 使用更通用的詞彙
            </Typography>
            <Typography 
              variant="body2" 
              color="var(--color-text-secondary)" 
              sx={{ 
                ml: { xs: 1, sm: 2 }, 
                mb: 0.5,
                fontSize: { xs: '0.8rem', sm: '0.875rem' },
                lineHeight: 1.4
              }}
            >
              • 檢查搜尋欄位選擇
            </Typography>
          </Box>
        </Box>
      )}

      {/* 當頁無資料但總體有結果 */}
      {showNoDataForQuery && (
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            justifyContent: 'center', 
            py: 6, 
            px: 4,
            textAlign: 'center'
          }}
        >
          <Typography 
            variant="h6" 
            color="var(--color-text-muted)" 
            sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1, justifyContent: 'center' }}
          >
            <ArticleIcon sx={{ fontSize: '1.5rem', color: '#4b5563' }} color="inherit" />
            此頁面暫無資料
          </Typography>
          <Typography 
            variant="body1" 
            color="var(--color-text-secondary)" 
          >
            請切換到其他頁面查看搜尋結果
          </Typography>
        </Box>
      )}

      {/* 歡迎使用者開始搜尋 */}
      {showWelcomeState && (
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            justifyContent: 'center', 
            py: 10, 
            px: 4,
            textAlign: 'center',
            background: 'linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-variant) 100%)',
            borderRadius: 3,
            border: '2px dashed var(--color-border-light)',
            margin: '20px 0'
          }}
        >
          <Typography 
            variant="h4" 
            sx={{ 
              mb: 3, 
              fontWeight: 600,
              fontSize: { xs: '1.75rem', md: '2.125rem' },
              display: 'flex',
              alignItems: 'center',
              gap: 1.5,
              justifyContent: 'center'
            }}
          >
            <LandscapeIcon sx={{ fontSize: '2.5rem', color: '#4caf50' }} color="inherit" />
            <Box component="span" sx={{
              background: 'linear-gradient(45deg, var(--color-primary), #42a5f5)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              color: 'transparent'
            }}>
              土地資料搜尋
            </Box>
          </Typography>
          
          <Typography 
            variant="h6" 
            color="var(--color-text-secondary)" 
            sx={{ 
              mb: 4, 
              fontWeight: 500, 
              maxWidth: 600, 
              lineHeight: 1.6,
              fontSize: { xs: '1rem', sm: '1.125rem', md: '1.25rem' },
              px: { xs: 1, sm: 2 }
            }}
          >
            <Box component="span" sx={{ display: { xs: 'block', sm: 'inline' } }}>
              歡迎使用土地資料查詢系統！
            </Box>
            <Box component="span" sx={{ display: { xs: 'block', sm: 'inline' }, mt: { xs: 1, sm: 0 } }}>
              請在上方搜尋框輸入關鍵字開始搜尋
            </Box>
          </Typography>

          <Box sx={{ 
            display: 'grid', 
            gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' },
            gap: 3, 
            maxWidth: 800, 
            width: '100%' 
          }}>
            <Box sx={{ 
              p: 3, 
              backgroundColor: 'var(--color-surface)',
              borderRadius: 2,
              border: '1px solid var(--color-border-light)',
              textAlign: 'center',
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: 'var(--shadow-hover)'
              }
            }}>
              <Typography variant="h6" sx={{ mb: 1, fontSize: { xs: '1rem', md: '1.125rem' }, display: 'flex', alignItems: 'center', gap: 0.75, color: 'var(--color-primary)' }}>
                <SearchIcon sx={{ fontSize: '1.3rem', color: '#1abc9c' }} color="inherit" />
                關鍵字搜尋
              </Typography>
              <Typography 
                variant="body2" 
                color="var(--color-text-secondary)"
                sx={{ 
                  fontSize: { xs: '0.8rem', sm: '0.875rem' },
                  lineHeight: { xs: 1.4, sm: 1.5 }
                }}
              >
                <Box component="span" sx={{ display: { xs: 'block', sm: 'inline' } }}>
                  輸入地名、地號
                </Box>
                <Box component="span" sx={{ display: { xs: 'block', sm: 'inline' } }}>
                  或相關資訊搜尋
                </Box>
              </Typography>
            </Box>

            <Box sx={{ 
              p: 3, 
              backgroundColor: 'var(--color-surface)',
              borderRadius: 2,
              border: '1px solid var(--color-border-light)',
              textAlign: 'center',
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: 'var(--shadow-hover)'
              }
            }}>
              <Typography variant="h6" sx={{ mb: 1, fontSize: { xs: '1rem', md: '1.125rem' }, display: 'flex', alignItems: 'center', gap: 0.75, color: 'var(--color-primary)' }}>
                <DescriptionIcon sx={{ fontSize: '1.3rem', color: '#2196f3' }} color="inherit" />
                欄位篩選
              </Typography>
              <Typography 
                variant="body2" 
                color="var(--color-text-secondary)"
                sx={{ 
                  fontSize: { xs: '0.8rem', sm: '0.875rem' },
                  lineHeight: { xs: 1.4, sm: 1.5 }
                }}
              >
                <Box component="span" sx={{ display: { xs: 'block', sm: 'inline' } }}>
                  左側面板選擇
                </Box>
                <Box component="span" sx={{ display: { xs: 'block', sm: 'inline' } }}>
                  特定搜尋欄位
                </Box>
              </Typography>
            </Box>

            <Box sx={{ 
              p: 3, 
              backgroundColor: 'var(--color-surface)',
              borderRadius: 2,
              border: '1px solid var(--color-border-light)',
              textAlign: 'center',
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: 'var(--shadow-hover)'
              },
              gridColumn: { xs: '1', sm: '1 / -1', md: 'auto' }
            }}>
              <Typography variant="h6" sx={{ mb: 1, fontSize: { xs: '1rem', md: '1.125rem' }, display: 'flex', alignItems: 'center', gap: 0.75, color: 'var(--color-primary)' }}>
                <VisibilityIcon sx={{ fontSize: '1.3rem', color: '#008cba' }} color="inherit" />
                快速檢視
              </Typography>
              <Typography 
                variant="body2" 
                color="var(--color-text-secondary)"
                sx={{ 
                  fontSize: { xs: '0.8rem', sm: '0.875rem' },
                  lineHeight: { xs: 1.4, sm: 1.5 }
                }}
              >
                <Box component="span" sx={{ display: { xs: 'block', sm: 'inline' } }}>
                  分頁瀏覽結果
                </Box>
                <Box component="span" sx={{ display: { xs: 'block', sm: 'inline' } }}>
                  檢視詳細資訊
                </Box>
              </Typography>
            </Box>
          </Box>

          <Box sx={{ 
            mt: 4, 
            p: { xs: 1.5, sm: 2 }, 
            backgroundColor: 'rgba(var(--color-primary-rgb), 0.1)', 
            borderRadius: 2,
            mx: { xs: 1, sm: 0 }
          }}>
            <Typography 
              variant="body2" 
              color="var(--color-text-muted)" 
              sx={{ 
                fontStyle: 'italic',
                fontSize: { xs: '0.8rem', sm: '0.875rem' },
                textAlign: 'center',
                lineHeight: { xs: 1.4, sm: 1.5 },
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                flexWrap: 'wrap',
                justifyContent: 'center'
              }}
            >
              <LightbulbIcon sx={{ fontSize: '1.1rem', color: '#ff9800' }} color="inherit" />
              <Box component="span">
                小提示：搜尋前請確保已選擇相關欄位，這樣可以獲得更精確的結果
              </Box>
            </Typography>
          </Box>
        </Box>
      )}

      {!searchLoading && resultList.length > pageShowCount && (
        <CustomPagination pageShowCount={pageShowCount} />
      )}
    </Grid>
  );
}

export default ResultField;
