# Phase 1 測試指南

## 測試目標

驗證基礎架構（雙角色機制 + 多選角色支援）正常運作。

---

## 測試前準備

### 1. 檢查環境變數

**開發環境** (`.env.development`):
```bash
REACT_APP_ENV=development
REACT_APP_IMPORT=true
REACT_APP_API_NODE=https://api2.daoyidh.com/land2
```

**正式環境** (`.env.production`):
```bash
REACT_APP_ENV=production
REACT_APP_IMPORT=false
REACT_APP_API_NODE=https://api.daoyidh.com/land
```

### 2. 檢查新增的檔案

- ✅ `src/utils/environmentUtils.js` - 環境工具函數
- ✅ `src/utils/permissionUtils.js` - 權限工具函數
- ✅ `scripts/migrateUserRoles.js` - 資料遷移腳本
- ✅ `src/Component/Authenticate/firebase/AuthListener.js` - 已更新支援雙角色

---

## 測試 1：環境變數載入

### 目標
確認環境變數正確載入

### 步驟

1. 啟動開發伺服器：
```bash
npm start
```

2. 打開瀏覽器開發者工具 Console

3. 測試環境函數：
```javascript
// 匯入環境工具
import { getCurrentEnvironment, isProduction, isDevelopment, getEnvironmentLabel } from './utils/environmentUtils';

// 測試
console.log('Current Environment:', getCurrentEnvironment());
console.log('Is Production:', isProduction());
console.log('Is Development:', isDevelopment());
console.log('Environment Label:', getEnvironmentLabel());
```

### 預期結果

**開發環境**：
```
Current Environment: development
Is Production: false
Is Development: true
Environment Label: 測試站
```

**正式環境（build 後）**：
```
Current Environment: production
Is Production: true
Is Development: false
Environment Label: 正式站
```

---

## 測試 2：環境工具函數

### 目標
測試多選角色解析功能

### 測試資料

```javascript
const testUsers = [
  {
    name: "單選角色使用者",
    uid: "test-001",
    role: "admin",
    roleDev: "admin"
  },
  {
    name: "多選角色使用者",
    uid: "test-002",
    role: "reader,editor",
    roleDev: "admin,editor,developer"
  },
  {
    name: "舊版使用者（無 roleDev）",
    uid: "test-003",
    role: "developer"
    // 沒有 roleDev 欄位
  }
];
```

### 測試程式碼

建立測試檔案 `src/utils/__tests__/environmentUtils.test.js`：

```javascript
import {
  getUserRoleByEnvironment,
  getUserRolesArrayByEnvironment,
  hasRole
} from '../environmentUtils';

describe('environmentUtils', () => {
  // 測試單選角色
  test('單選角色 - 開發環境', () => {
    const user = { role: "admin", roleDev: "admin" };
    process.env.REACT_APP_ENV = 'development';
    
    expect(getUserRoleByEnvironment(user)).toBe("admin");
    expect(getUserRolesArrayByEnvironment(user)).toEqual(["admin"]);
    expect(hasRole(user, "admin")).toBe(true);
    expect(hasRole(user, "editor")).toBe(false);
  });
  
  // 測試多選角色
  test('多選角色 - 開發環境', () => {
    const user = {
      role: "reader,editor",
      roleDev: "admin,editor,developer"
    };
    process.env.REACT_APP_ENV = 'development';
    
    const roles = getUserRolesArrayByEnvironment(user);
    expect(roles).toEqual(["admin", "editor", "developer"]);
    expect(hasRole(user, "admin")).toBe(true);
    expect(hasRole(user, "developer")).toBe(true);
  });
  
  // 測試正式環境
  test('多選角色 - 正式環境', () => {
    const user = {
      role: "reader,editor",
      roleDev: "admin,editor,developer"
    };
    process.env.REACT_APP_ENV = 'production';
    
    const roles = getUserRolesArrayByEnvironment(user);
    expect(roles).toEqual(["reader", "editor"]);
    expect(hasRole(user, "admin")).toBe(false);
    expect(hasRole(user, "reader")).toBe(true);
  });
  
  // 測試舊版使用者（無 roleDev）
  test('舊版使用者（無 roleDev）', () => {
    const user = { role: "developer" };
    process.env.REACT_APP_ENV = 'development';
    
    // 無 roleDev，應回退到 role
    expect(getUserRoleByEnvironment(user)).toBe("developer");
  });
});
```

### 手動測試

在瀏覽器 Console：

```javascript
import { getUserRolesArrayByEnvironment, hasRole } from './utils/environmentUtils';

const user = { role: "reader,editor", roleDev: "admin,editor,developer" };

// 測試站
console.log('Roles:', getUserRolesArrayByEnvironment(user));
console.log('Has admin?', hasRole(user, 'admin'));
console.log('Has developer?', hasRole(user, 'developer'));
```

---

## 測試 3：權限判斷邏輯

### 目標
驗證多選角色的權限判斷

### 測試資料

```javascript
const testCases = [
  {
    name: "Admin + Developer",
    user: { role: "admin,developer", roleDev: "admin,developer" },
    env: "production",
    expectations: {
      canEdit: true,      // admin 優先
      canDelete: true,
      canImport: true,
      canManageUsers: true
    }
  },
  {
    name: "Developer + Editor (正式站)",
    user: { role: "developer,editor", roleDev: "developer,editor" },
    env: "production",
    expectations: {
      canEdit: false,     // developer 限制優先
      canDelete: false,
      canImport: false,
      canManageUsers: false
    }
  },
  {
    name: "Developer + Editor (測試站)",
    user: { role: "developer,editor", roleDev: "developer,editor" },
    env: "development",
    expectations: {
      canEdit: true,      // 測試站無限制
      canDelete: true,
      canImport: true,
      canManageUsers: false  // 只有 admin 可以
    }
  },
  {
    name: "Reader + Editor",
    user: { role: "reader,editor", roleDev: "reader,editor" },
    env: "production",
    expectations: {
      canEdit: true,      // editor 權限
      canDelete: false,
      canImport: false,
      canManageUsers: false
    }
  }
];
```

### 測試程式碼

```javascript
import { canEdit, canDelete, canImport, canManageUsers } from '../permissionUtils';

describe('permissionUtils - 多選角色', () => {
  test('Admin + Developer (正式站) - admin 優先', () => {
    const user = { role: "admin,developer", roleDev: "admin,developer" };
    process.env.REACT_APP_ENV = 'production';
    
    expect(canEdit(user)).toBe(true);
    expect(canDelete(user)).toBe(true);
    expect(canImport(user)).toBe(true);
    expect(canManageUsers(user)).toBe(true);
  });
  
  test('Developer + Editor (正式站) - developer 限制優先', () => {
    const user = { role: "developer,editor", roleDev: "developer,editor" };
    process.env.REACT_APP_ENV = 'production';
    
    expect(canEdit(user)).toBe(false);
    expect(canDelete(user)).toBe(false);
    expect(canImport(user)).toBe(false);
  });
  
  test('Developer + Editor (測試站) - 無限制', () => {
    const user = { role: "developer,editor", roleDev: "developer,editor" };
    process.env.REACT_APP_ENV = 'development';
    
    expect(canEdit(user)).toBe(true);
    expect(canDelete(user)).toBe(true);
    expect(canImport(user)).toBe(true);
  });
  
  test('Reader + Editor - editor 權限', () => {
    const user = { role: "reader,editor", roleDev: "reader,editor" };
    
    expect(canEdit(user)).toBe(true);
    expect(canDelete(user)).toBe(false);
    expect(canImport(user)).toBe(false);
  });
});
```

---

## 測試 4：資料遷移腳本

### ⚠️ 重要警告

**執行前必須**：
1. ✅ 備份 Firebase Realtime Database
2. ✅ 確認在正確的 Firebase 專案
3. ✅ 確認有足夠的讀寫權限

### 測試步驟

1. **執行遷移腳本**：
```bash
node scripts/migrateUserRoles.js
```

2. **檢查輸出**：
```
========================================
🚀 開始使用者角色資料遷移
========================================

📡 正在從 Firebase Realtime Database 讀取使用者資料...

✅ 成功讀取 5 個使用者

========================================
開始分析使用者資料...
========================================

✓ [1] 使用者: <EMAIL>
  - UID: user123
  - role: admin
  - roleDev: admin (新增)

✓ [2] 使用者: <EMAIL>
  - UID: user456
  - role: reader,editor
  - roleDev: reader,editor (新增)
  - 角色清單: [reader, editor]

========================================
分析結果
========================================
總使用者數: 5
需要更新: 5 個
跳過: 0 個（已有 roleDev）
多選角色: 2 個

========================================
🔄 開始更新資料庫...
========================================

========================================
✅ 遷移完成！
========================================
成功為 5 個使用者新增 roleDev 欄位
其中 2 個使用者使用多選角色格式
```

3. **驗證 Firebase 資料**：

前往 Firebase Console > Realtime Database，檢查 `/users` 路徑：

```json
{
  "users": {
    "user123": {
      "uid": "user123",
      "email": "<EMAIL>",
      "role": "admin",
      "roleDev": "admin"  ✅
    },
    "user456": {
      "uid": "user456",
      "email": "<EMAIL>",
      "role": "reader,editor",
      "roleDev": "reader,editor"  ✅
    }
  }
}
```

4. **重複執行測試**：

再次執行應顯示「跳過」訊息：

```bash
node scripts/migrateUserRoles.js
```

預期輸出：
```
- [跳過] 使用者: <EMAIL>
  已有 roleDev 欄位 (role=admin, roleDev=admin)
```

---

## 測試 5：AuthListener 整合測試

### 目標
驗證登入時正確載入雙角色

### 測試步驟

1. **開發環境登入**：

```bash
npm start
```

2. **打開瀏覽器開發者工具**

3. **登入系統**

4. **檢查 Redux State**：

在 Console 執行：
```javascript
// 取得 Redux state
const state = store.getState();
console.log('User:', state.user);
```

預期輸出：
```javascript
{
  uid: "user123",
  email: "<EMAIL>",
  role: "admin",              // 原始欄位
  roleDev: "admin",           // 新增欄位
  currentRole: "admin",       // 當前有效角色（字串）
  currentRoles: ["admin"]     // 當前有效角色（陣列）
}
```

5. **測試多選角色**：

若使用者為 `role: "reader,editor", roleDev: "admin,developer"`：

**開發環境**：
```javascript
{
  role: "reader,editor",
  roleDev: "admin,developer",
  currentRole: "admin,developer",
  currentRoles: ["admin", "developer"]
}
```

**正式環境**：
```javascript
{
  role: "reader,editor",
  roleDev: "admin,developer",
  currentRole: "reader,editor",
  currentRoles: ["reader", "editor"]
}
```

---

## 測試 6：跨環境測試

### 目標
驗證 build 後的正式環境設定

### 步驟

1. **Build 正式版本**：
```bash
npm run build
```

2. **啟動本地伺服器**：
```bash
# 安裝 serve（如果還沒安裝）
npm install -g serve

# 啟動伺服器
serve -s build
```

3. **訪問本地伺服器**（通常是 http://localhost:3000 或 5000）

4. **檢查環境變數**：

在 Console：
```javascript
console.log('REACT_APP_ENV:', process.env.REACT_APP_ENV);
// 應為: "production"
```

5. **測試權限**：

以 developer 角色登入：
```javascript
import { canEdit } from './utils/permissionUtils';

const user = { role: "developer", roleDev: "developer" };
console.log('Can edit in production:', canEdit(user));
// 應為: false
```

---

## 驗收標準

### ✅ 必須通過

1. **環境變數**
   - ✅ 開發環境：`REACT_APP_ENV = 'development'`
   - ✅ 正式環境：`REACT_APP_ENV = 'production'`

2. **環境工具函數**
   - ✅ 正確解析單選角色
   - ✅ 正確解析多選角色（如 "admin,editor"）
   - ✅ 正確處理舊版使用者（無 roleDev）

3. **權限判斷**
   - ✅ Admin 最高優先（忽略其他角色）
   - ✅ Developer 在正式站不可編輯
   - ✅ Developer 在測試站可編輯
   - ✅ Developer + Editor 在正式站：不可編輯
   - ✅ Admin + Developer 在正式站：可編輯

4. **資料遷移**
   - ✅ 所有使用者都有 roleDev 欄位
   - ✅ 多選角色格式保留（如 "admin,editor"）
   - ✅ 預設值 = role 值

5. **AuthListener**
   - ✅ 新使用者初始化時同時設定 role 和 roleDev
   - ✅ 登入時正確載入 currentRole 和 currentRoles
   - ✅ 根據環境選擇對應角色

---

## 問題排查

### 問題 1：環境變數未載入

**症狀**：`getCurrentEnvironment()` 回傳 `undefined`

**解決方法**：
1. 確認 `.env.development` 和 `.env.production` 檔案存在
2. 重新啟動開發伺服器（`npm start`）
3. 確認環境變數名稱以 `REACT_APP_` 開頭

### 問題 2：多選角色解析錯誤

**症狀**：`getUserRolesArrayByEnvironment()` 回傳空陣列或錯誤格式

**解決方法**：
1. 檢查 role 字串格式（逗號分隔，無空格）
2. 使用除錯函數：`logEnvironmentInfo(user)`

### 問題 3：權限判斷錯誤

**症狀**：Developer 在正式站可以編輯

**解決方法**：
1. 確認環境變數正確載入（`isProduction()` 回傳 true）
2. 檢查使用者是否有 admin 角色（admin 權限最高）
3. 檢查 `canPerformOperation()` 邏輯

### 問題 4：資料遷移失敗

**症狀**：執行腳本後出現錯誤

**解決方法**：
1. 檢查網路連線
2. 確認 Firebase 配置正確
3. 確認有 Realtime Database 讀寫權限
4. 檢查 Firebase 規則

---

## 下一步

Phase 1 完成後，進入 **Phase 2：頁面權限控制**

需要更新的頁面：
1. EditPage - 編輯功能權限檢查
2. GisPage - 經緯度編輯權限檢查
3. ImportDataPage - 匯入功能權限檢查
4. AuthorityPage - 使用者管理權限檢查

**準備工作**：
- 熟悉 `canEdit()`, `canImport()`, `canManageUsers()` 函數
- 了解各頁面的編輯功能位置
- 準備權限提示訊息的 UI 組件
