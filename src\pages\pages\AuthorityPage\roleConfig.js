import authority from "../../../config/App-authority";
import role from "../../../config/App-role";
import pathConfig from "../../../config/route-path";

export const normalizeRoleKey = (value = "") => value.trim().toLowerCase();

export const ROLE_METADATA = {
  [role.admin]: {
    key: role.admin,
    label: "Admin",
    description: "可管理帳號、審核權限與調整系統設定",
  },
  [role.editor]: {
    key: role.editor,
    label: "Editor",
    description: "可維護土地台帳資料並執行資料匯入",
  },
  [role.reader]: {
    key: role.reader,
    label: "Reader",
    description: "可檢視台帳資訊與查詢結果",
  },
  [role.developer]: {
    key: role.developer,
    label: "Developer",
    description: "維運用戶，擁有所有 Dashboard 功能",
  },
  [role.anonymous]: {
    key: role.anonymous,
    label: "Anonymous",
    description: "未登入訪客，可使用公開瀏覽功能",
  },
};

const DEFAULT_PAGE_META = {
  Home: {
    key: "Home",
    fallbackLabel: "首頁",
    description: "登入後的首頁總覽",
  },
  Search: {
    key: "Search",
    routeKey: "search",
    fallbackLabel: "土地查詢",
    description: "查詢並檢視土地台帳資料",
  },
  Edit: {
    key: "Edit",
    routeKey: "edit",
    fallbackLabel: "資料編輯",
    description: "新增或更新土地台帳欄位",
  },
  Download: {
    key: "Download",
    routeKey: "download",
    fallbackLabel: "資料下載",
    description: "匯出查詢結果或報表",
  },
  Authority: {
    key: "Authority",
    routeKey: "auth",
    fallbackLabel: "權限管理",
    description: "核發 Dashboard 與前台權限",
  },
  Admin: {
    key: "Admin",
    fallbackLabel: "帳號管理",
    description: "審核帳號申請與維護使用者資料",
  },
  ImportData: {
    key: "ImportData",
    routeKey: "importData",
    fallbackLabel: "資料匯入",
    description: "批次上傳與檢核地籍資料",
  },
  History: {
    key: "History",
    routeKey: "history",
    fallbackLabel: "歷程稽核",
    description: "追蹤資料異動紀錄",
  },
  Statistics: {
    key: "Statistics",
    routeKey: "statistics",
    fallbackLabel: "統計分析",
    description: "查看土地台帳統計資訊",
  },
  Gis: {
    key: "Gis",
    routeKey: "gis",
    fallbackLabel: "GIS 工具",
    description: "地圖操作與空間分析",
  },
};

const EXCLUDED_PAGES = new Set(["SignIn", "SignOut"]);

const resolvePageMeta = (pageKey) => {
  const defaults = DEFAULT_PAGE_META[pageKey] || {
    key: pageKey,
    fallbackLabel: pageKey,
    description: "",
  };

  const routeKey = defaults.routeKey;
  const routeMeta = routeKey ? pathConfig[routeKey] : undefined;

  return {
    key: defaults.key,
    label: routeMeta?.label || defaults.fallbackLabel || pageKey,
    description: defaults.description,
  };
};

const buildRoleToPagesMap = () => {
  const result = new Map();

  Object.entries(authority).forEach(([pageKey, allowedRoles]) => {
    if (EXCLUDED_PAGES.has(pageKey)) {
      return;
    }

    if (!allowedRoles || allowedRoles.length === 0) {
      return;
    }

    const meta = resolvePageMeta(pageKey);

    allowedRoles
      .map((roleValue) => normalizeRoleKey(roleValue))
      .forEach((roleKey) => {
        if (!result.has(roleKey)) {
          result.set(roleKey, new Map());
        }
        const pages = result.get(roleKey);
        pages.set(meta.key, meta);
      });
  });

  return result;
};

const ROLE_TO_PAGES = buildRoleToPagesMap();

export const getRoleMetadata = (roleValue) => {
  const normalized = normalizeRoleKey(roleValue);
  return (
    ROLE_METADATA[normalized] || {
      key: normalized,
      label: roleValue,
      description: "",
    }
  );
};

export const collectPagesByRoles = (roleValues) => {
  const pageMap = new Map();

  roleValues
    .map((roleValue) => normalizeRoleKey(roleValue))
    .filter(Boolean)
    .forEach((roleKey) => {
      const pages = ROLE_TO_PAGES.get(roleKey);
      if (!pages) {
        return;
      }

      pages.forEach((pageMeta, pageKey) => {
        const existing = pageMap.get(pageKey);
        if (existing) {
          existing.via.add(roleKey);
        } else {
          pageMap.set(pageKey, {
            ...pageMeta,
            via: new Set([roleKey]),
          });
        }
      });
    });

  return Array.from(pageMap.values())
    .map((page) => ({
      ...page,
      via: Array.from(page.via).map(
        (roleKey) => getRoleMetadata(roleKey).label || roleKey
      ),
    }))
    .sort((a, b) => a.label.localeCompare(b.label, "zh-Hant-u-nu-hanidec"));
};

export default resolvePageMeta;
