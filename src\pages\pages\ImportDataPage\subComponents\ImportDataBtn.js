import React, { useContext, useState } from "react";
import PropTypes from "prop-types";
import axios from "axios";

// material ui
import Button from "@mui/material/Button";
import CircularProgress from "@mui/material/CircularProgress";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import { Api, dataSet } from "../../../../api/land/Api";

// config
import { ENABLE_API_CALL } from "../config";

// utils
import { isEmpty } from "../../../../utils";

function ImportDataBtn({ setCheckMsg }) {
  const [state] = useContext(StoreContext);
  const { fileData } = state.importData;
  const { displayName, email } = state.user;
  const [isImporting, setIsImporting] = useState(false);

  const startImport = async () => {
    if (!ENABLE_API_CALL) {
      // 測試模式：模擬 API 呼叫
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ success: true, message: "測試模式：模擬匯入成功" });
        }, 2000);
      });
    }

    // 正式模式：真實 API 呼叫
    const apiStr = Api.importToDB.replace("{graph}", dataSet);
    const formData = new FormData();

    // ※formData 要放的是 file 物件不是字串
    formData.append("file", fileData);
    formData.append("userName", displayName);
    formData.append("userEmail", email);

    // start send to api
    return axios.post(apiStr, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
  };

  const handleClick = async () => {
    setIsImporting(true);
    
    const msg = ENABLE_API_CALL
      ? "開始匯入。您可以關閉此視窗，資料匯入完成後將寄發通知信至您的信箱。"
      : "【測試模式】模擬匯入中，不會真的寫入資料庫...";
    
    setCheckMsg(msg);

    try {
      await startImport();
      
      if (!ENABLE_API_CALL) {
        setCheckMsg("【測試模式】模擬匯入完成！實際上未寫入任何資料。");
      }
    } catch (error) {
      setCheckMsg(`匯入失敗：${error.message}`);
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <Button
      variant="contained"
      size="medium"
      onClick={handleClick}
      color="error"
      disabled={isEmpty(fileData) || isImporting}
      startIcon={isImporting ? <CircularProgress size={20} color="inherit" /> : null}
    >
      {isImporting ? "匯入中..." : "匯入資料"}
    </Button>
  );
}

ImportDataBtn.propTypes = {
  /** 設定顯示開始匯入訊息callback */
  setCheckMsg: PropTypes.func,
};

ImportDataBtn.defaultProps = {
  /** 設定顯示開始匯入訊息callback */
  setCheckMsg: () => "",
};

export default ImportDataBtn;
