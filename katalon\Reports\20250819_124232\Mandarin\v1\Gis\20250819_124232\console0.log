Warning: NLS unused message: QTEST in: com.kms.katalon.constants.globalMessages
Warning: NLS missing message: QTEST_NAME in: com.kms.katalon.constants.globalMessages
2025-08-19 12:42:36.043 INFO  c.k.katalon.core.main.TestSuiteExecutor  - START Test Suites/Mandarin/v1/Gis
2025-08-19 12:42:36.146 INFO  c.k.katalon.core.main.TestSuiteExecutor  - userFullName = Lin Kai Yee
2025-08-19 12:42:36.148 INFO  c.k.katalon.core.main.TestSuiteExecutor  - projectName = katalon
2025-08-19 12:42:36.149 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostName = saw63 - ************
2025-08-19 12:42:36.149 INFO  c.k.katalon.core.main.TestSuiteExecutor  - os = Windows 11 64bit
2025-08-19 12:42:36.149 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostAddress = ************
2025-08-19 12:42:36.150 INFO  c.k.katalon.core.main.TestSuiteExecutor  - katalonVersion = ********
2025-08-19 12:42:36.463 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-19 12:42:36.556 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 19, 2025 12:42:37 下午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-19 12:42:38.394 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 58b9db6878e23d023d7d9eac8e711437
2025-08-19 12:42:38.395 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-19 12:42:38.396 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-19 12:42:38.396 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-19 12:42:38.407 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-19 12:42:38.910 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 12:42:38.910 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-19 12:42:38.967 DEBUG testcase.GoToGis                         - 1: openBrowser("")
2025-08-19 12:42:38.972 WARN  c.k.k.core.webui.driver.DriverFactory    - A browser is already opened. Closing browser and opening a new one
2025-08-19 12:42:39.140 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-19 12:42:39.144 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 19, 2025 12:42:40 下午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-19 12:42:40.086 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 499d3100755f74aa5cfb63b89bdb2b7b
2025-08-19 12:42:40.087 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-19 12:42:40.088 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-19 12:42:40.088 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-19 12:42:40.089 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-19 12:42:40.111 DEBUG testcase.GoToGis                         - 2: navigateToUrl("https://land2.daoyidh.com/Gis")
2025-08-19 12:42:40.413 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-19 12:42:40.420 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 12:42:40.420 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-19 12:42:40.497 DEBUG testcase.LoginWithEmail                  - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_"))
2025-08-19 12:42:41.872 DEBUG testcase.LoginWithEmail                  - 2: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail"))
2025-08-19 12:42:42.380 DEBUG testcase.LoginWithEmail                  - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Email_email"), "<EMAIL>")
2025-08-19 12:42:43.177 DEBUG testcase.LoginWithEmail                  - 4: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Next"))
2025-08-19 12:42:43.487 DEBUG testcase.LoginWithEmail                  - 5: setEncryptedText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Password_password"), "0D41p6Ct1VJ35FCiyaw3Gw==")
2025-08-19 12:42:44.443 DEBUG testcase.LoginWithEmail                  - 6: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign In"))
2025-08-19 12:42:44.737 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-19 12:42:44.741 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 12:42:44.741 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-19 12:42:44.788 DEBUG testcase.SearchTextInGis                 - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button__1"))
2025-08-19 12:42:47.293 DEBUG testcase.SearchTextInGis                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input__mui-3"), "新北勢")
2025-08-19 12:42:48.169 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-19 12:42:48.173 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 12:42:48.174 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-19 12:42:48.248 DEBUG testcase.ChoosePerPage                   - 1: dropdownButton = new com.kms.katalon.core.testobject.TestObject()
2025-08-19 12:42:48.256 DEBUG testcase.ChoosePerPage                   - 2: dropdownButton.addProperty("css", EQUALS, "div[role="combobox"]")
2025-08-19 12:42:48.262 DEBUG testcase.ChoosePerPage                   - 3: click(dropdownButton)
2025-08-19 12:43:19.280 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - Unable to find the element located by 'By.cssSelector: div[role="combobox"]'. Please recheck the objects properties to make sure the desired element is located. 
2025-08-19 12:43:19.281 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Failed to find element with id ''. Try using Self-healing.
2025-08-19 12:43:19.295 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Index 0 out of bounds for length 0
2025-08-19 12:43:19.297 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Cannot find elements when the selector is null
2025-08-19 12:43:19.401 ERROR c.k.k.core.keyword.internal.KeywordMain  - ❌ Unable to click on object '' (Root cause: com.kms.katalon.core.exception.StepFailedException: Unable to click on object ''
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at ChoosePerPage.run(ChoosePerPage:26)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755578552784.run(TempTestSuite1755578552784.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: '' located by 'div[role="combobox"]' not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:66)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at Script1755487429981.run(Script1755487429981.groovy:26)
	... 15 more
)
2025-08-19 12:43:19.405 ERROR c.k.katalon.core.main.TestCaseExecutor   - ❌ Test Cases/Mandarin/v1_web/Gis/ChoosePerPage FAILED.
Reason:
com.kms.katalon.core.exception.StepFailedException: Unable to click on object ''
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at ChoosePerPage.run(ChoosePerPage:26)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755578552784.run(TempTestSuite1755578552784.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: '' located by 'div[role="combobox"]' not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:66)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	... 20 more

2025-08-19 12:43:19.408 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-19 12:43:19.412 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 12:43:19.412 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-19 12:43:19.465 DEBUG testcase.EditCoordinates                 - 1: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "148485")
8月 19, 2025 12:43:25 下午 org.openqa.selenium.remote.http.WebSocket$Listener onError
警告: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at java.net.http/jdk.internal.net.http.SocketTube.readAvailable(SocketTube.java:1170)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.read(SocketTube.java:833)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowTask.run(SocketTube.java:181)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.signalReadable(SocketTube.java:774)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$ReadEvent.signalEvent(SocketTube.java:957)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowEvent.handle(SocketTube.java:253)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.handleEvent(HttpClientImpl.java:984)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.lambda$run$3(HttpClientImpl.java:939)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:939)

