import React, { useState, useEffect, useContext, useMemo } from "react";

// material ui
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import Drawer from "@mui/material/Drawer";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemText from "@mui/material/ListItemText";
import Divider from "@mui/material/Divider";
import Typography from "@mui/material/Typography";
import Chip from "@mui/material/Chip";
import Tooltip from "@mui/material/Tooltip";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import PersonIcon from "@mui/icons-material/Person";

// store
import { Link, useLocation } from "react-router-dom";
import { StoreContext } from "../../../store/StoreProvider";
import {
  getVisibleLeftMenus,
  getVisibleRightMenus,
} from "../../../utils/headerPermissions";

// permission utils
import { getEnvironmentBadge } from "../../../utils/environmentUtils";
import { getUserRoleDisplayText } from "../../../utils/permissionUtils";

// css
import "./header.scss";

const Header = () => {
  const [state] = useContext(StoreContext);
  const { user } = state;
  const { role } = user;
  const isLogin = JSON.parse(localStorage.getItem("land-isLogin"));
  const location = useLocation();
  const [activePage, setActivePage] = useState("");
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const theme = useTheme();
  const isTabletDown = useMediaQuery(theme.breakpoints.down("md"));

  // 取得環境 Badge 資訊
  const environmentBadge = useMemo(() => getEnvironmentBadge(), []);
  
  // 取得使用者角色顯示文字
  const roleDisplayText = useMemo(() => getUserRoleDisplayText(user), [user]);
  
  // 建立使用者資訊 Tooltip 內容
  const userInfoTooltip = useMemo(() => {
    const parts = [];
    if (user.displayName) parts.push(`使用者：${user.displayName}`);
    if (user.email) parts.push(`信箱：${user.email}`);
    if (roleDisplayText) parts.push(`角色：${roleDisplayText}`);
    return parts.join('\n');
  }, [user, roleDisplayText]);

  const visibleLeftMenus = useMemo(() => getVisibleLeftMenus(role), [role]);
  const visibleRightMenus = useMemo(
    () => getVisibleRightMenus(role, isLogin),
    [role, isLogin]
  );

  useEffect(() => {
    const findPage = visibleLeftMenus.find(
      (menu) => menu.path === location.pathname
    );
    if (findPage) {
      setActivePage(findPage.id);
    }
  }, [visibleLeftMenus, location.pathname]);

  const handleActivatePage = (menuId) => {
    setActivePage(menuId);
    if (isTabletDown) {
      setIsDrawerOpen(false);
    }
  };

  const renderNavButton = (item) => {
    const isActive = activePage === item.id;
    return (
      <div className={`HeaderItem${isActive ? " is-active" : ""}`} key={item.id}>
        <Button
          variant="text"
          component={Link}
          to={item.path}
          onClick={() => handleActivatePage(item.id)}
          color={isActive ? "primary" : "inherit"}
        >
          {item.name}
        </Button>
        {isActive ? <div className="hrLine" /> : null}
      </div>
    );
  };

  const drawerContent = (
    <Box className="Header__drawer" role="presentation">
      <Box className="Header__drawerHeader">
        <Typography variant="h6">功能選單</Typography>
        <IconButton onClick={() => setIsDrawerOpen(false)} aria-label="關閉導覽選單">
          <CloseIcon />
        </IconButton>
      </Box>
      <Divider />
      <List>
        {visibleLeftMenus.map((item) => (
          <ListItem disablePadding key={item.id}>
            <ListItemButton
              component={Link}
              to={item.path}
              selected={activePage === item.id}
              onClick={() => handleActivatePage(item.id)}
            >
              <ListItemText primary={item.name} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
      {visibleRightMenus.length > 0 ? (
        <>
          <Divider sx={{ my: 1 }} />
          <List>
            {visibleRightMenus.map((item) => (
              <ListItem disablePadding key={item.id}>
                <ListItemButton
                  component={Link}
                  to={item.path}
                  onClick={() => setIsDrawerOpen(false)}
                >
                  <ListItemText primary={item.name} />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        </>
      ) : null}
    </Box>
  );

  return (
    <div className="Header">
      {isTabletDown ? (
        <Box className="Header__section Header__left" sx={{ flex: 1 }}>
          <IconButton
            className="Header__menuToggle"
            onClick={() => setIsDrawerOpen(true)}
            aria-label="開啟導覽選單"
          >
            <MenuIcon />
          </IconButton>
          <Drawer
            anchor="left"
            open={isDrawerOpen}
            onClose={() => setIsDrawerOpen(false)}
            ModalProps={{ keepMounted: true }}
          >
            {drawerContent}
          </Drawer>
        </Box>
      ) : (
        <Box className="Header__section Header__left" sx={{ flex: 1 }}>
          {visibleLeftMenus.map((item) => renderNavButton(item))}
        </Box>
      )}

      {isTabletDown ? (
        <Box className="Header__section Header__actions" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* 環境 Badge - 行動版 */}
          <Chip 
            label={environmentBadge.label}
            size="small"
            sx={{ 
              backgroundColor: environmentBadge.bgColor,
              color: environmentBadge.color,
              fontWeight: 'bold',
              fontSize: '0.7rem',
              height: '24px'
            }}
          />
          
          {/* 使用者資訊 - 行動版 */}
          {isLogin && (
            <Tooltip 
              title={<div style={{ whiteSpace: 'pre-line' }}>{userInfoTooltip}</div>}
              arrow
              placement="bottom"
            >
              <IconButton size="small">
                <PersonIcon fontSize="small" color="primary" />
              </IconButton>
            </Tooltip>
          )}
          
          {visibleRightMenus.map((item) => (
            <Button
              key={item.id}
              size="small"
              variant="outlined"
              component={Link}
              to={item.path}
              onClick={() => setIsDrawerOpen(false)}
            >
              {item.name}
            </Button>
          ))}
        </Box>
      ) : (
        <Box className="Header__section Header__right" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* 環境 Badge - 桌面版 */}
          <Chip 
            label={environmentBadge.label}
            size="small"
            sx={{ 
              backgroundColor: environmentBadge.bgColor,
              color: environmentBadge.color,
              fontWeight: 'bold',
              mr: 1
            }}
          />
          
          {/* 使用者資訊 - 桌面版 */}
          {isLogin && (
            <Tooltip 
              title={<div style={{ whiteSpace: 'pre-line' }}>{userInfoTooltip}</div>}
              arrow
              placement="bottom"
            >
              <IconButton size="small" sx={{ mr: 1 }}>
                <PersonIcon color="primary" />
              </IconButton>
            </Tooltip>
          )}
          
          {visibleRightMenus.map((item) => (
            <div className="HeaderItem" key={item.id}>
              <Button
                variant="text"
                component={Link}
                to={item.path}
                onClick={() => setActivePage("")}
              >
                {item.name}
              </Button>
            </div>
          ))}
        </Box>
      )}
    </div>
  );
};

export default Header;
