import React, { useContext, useState } from "react";

// material ui
import Autocomplete from "@mui/material/Autocomplete";
import Chip from "@mui/material/Chip";
import TextField from "@mui/material/TextField";

// utils
import {
  disableEditFunction,
  disableFontColor,
  getOptions,
} from "../../../common";
import { landMarkPlaceHolder } from "../../../common/phDes";
import Act from "../../../../../../store/actions";
import { StoreContext } from "../../../../../../store/StoreProvider";
import evtType from "../../../../../../utils/evtType";

function CauseAutocomplete({ info, handleChange, index }) {
  const [state, dispatch] = useContext(StoreContext);
  const [options, setOptions] = useState([]); // cause的下拉選單選項

  const defaultReason = [
    "1919地租改正",
    "1935地租改正",
    "1944地租改訂",
    "地目變換",
    "地目變換除租",
    "地目變換地租修正",
    "測量誤謬訂正",
    "地域變更",
    "地域變更地租更正",
    "荒地成免租",
    "再荒免租",
    "免租期限滿了地目變換",
    "新規賦租",
    "復舊",
    "開墾",
    "查定",
    "地目等則調整（戰後）",
    "登錄（戰後）",
    "分割出",
    "合併",
  ];

  return (
    <Autocomplete
      multiple
      disabled={disableEditFunction(state)}
      options={options}
      renderInput={(params) => (
        <TextField
          {...params}
          variant="standard"
          placeholder={
            info.value.length === 0 ? landMarkPlaceHolder[info.predicate] : ""
          }
          onClick={async () => {
            const optionList = await getOptions(
              evtType.LandMark,
              info.predicate
            );
            let tmpOption = [];
            optionList.forEach((element) => {
              const tmpValue = element.label.split("，");
              tmpValue.forEach((value) => {
                if (!tmpOption.includes(value)) {
                  tmpOption.push(value);
                }
              });
            });

            // 不顯示目前資料庫抓到有"分割出"、"合併"這兩種開頭的原因字串
            tmpOption = tmpOption.filter(
              (str) =>
                !str.indexOf("分割出") === 0 || !str.indexOf("合併") === 0
            );

            // defaultReason和tmpOption不重複顯示相同原因
            let tmpDefaultReason = JSON.parse(JSON.stringify(defaultReason));
            tmpDefaultReason = tmpDefaultReason.filter((element) => {
              const tmpArr = tmpOption.filter((option) => option === element);
              return tmpArr.length === 0;
            });
            setOptions([...tmpDefaultReason, ...tmpOption].sort());
          }}
          inputProps={{
            ...params.inputProps,
            ...disableFontColor,
          }}
          multiline
        />
      )}
      onChange={(event, value) => {
        const specificCol = ["分割出", "合併"];
        const findValue = value.find((item) => specificCol.includes(item));
        const newData = { ...info, index };
        if (findValue) {
          dispatch({
            type: Act.SET_ADDSTRDIALOGPARAM,
            payload: {
              open: true,
              type: findValue,
              newData,
            },
          });
        } else {
          handleChange(value, newData);
        }
      }}
      freeSolo
      value={info.value}
      renderTags={(value, getTagProps) =>
        value.map((option, index) => (
          <Chip
            variant="outlined"
            label={option}
            {...getTagProps({ index })}
            style={{ opacity: 1 }}
          />
        ))
      }
    />
  );
}

export default CauseAutocomplete;
