import React, { useContext, useState } from "react";

// material ui
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";

// utils
import {
  disableEditFunction,
  disableFontColor,
  eachRuleRequiredCheck,
  getOptions,
} from "../../../common";
import { landMarkPlaceHolder } from "../../../common/phDes";
import { StoreContext } from "../../../../../../store/StoreProvider";
import evtType from "../../../../../../utils/evtType";

function CateAutocomplete({ info, handleChange, index }) {
  const [state] = useContext(StoreContext);
  const [landCategoryOptions, setLandCategoryOptions] = useState([]); // landCategory的下拉選單選項
  const defaultLandGrades = [
    "田",
    "旱田",
    "建物",
    "山林",
    "原野",
    "墳墓地",
    "用惡水路",
    "雜種地",
  ];

  return (
    <Autocomplete
      disabled={disableEditFunction(state)}
      options={landCategoryOptions}
      renderInput={(params) => (
        <TextField
          {...params}
          variant="standard"
          placeholder={
            info.value.length === 0 ? landMarkPlaceHolder[info.predicate] : ""
          }
          onClick={async () => {
            const optionList = await getOptions(
              evtType.LandMark,
              info.predicate
            );
            const tmpOptionList = optionList
              .filter((el) => !defaultLandGrades.includes(el.label))
              .filter((el) => el.label !== "")
              .map((el) => el.label);
            setLandCategoryOptions(
              [...defaultLandGrades, ...tmpOptionList].sort()
            );
          }}
          error={eachRuleRequiredCheck(state, info, index).check}
          helperText={eachRuleRequiredCheck(state, info, index).helpText}
          inputProps={{
            ...params.inputProps,
            ...disableFontColor,
          }}
          multiline
        />
      )}
      onChange={(e, value) => {
        const tmpValue = value || "";
        const newData = { ...info, index };
        handleChange(tmpValue, newData);
      }}
      freeSolo
      value={info.value}
    />
  );
}

export default CateAutocomplete;
