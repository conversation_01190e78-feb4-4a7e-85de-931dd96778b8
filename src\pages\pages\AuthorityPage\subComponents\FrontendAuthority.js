import React, { useContext } from 'react';
import { Box, Typography } from '@mui/material';
import { StoreContext } from '../../../../store/StoreProvider';

function FrontendAuthority() {
  const [state, dispatch] = useContext(StoreContext);
  const { clientUserInfo } = state.authority;
  console.log('clientUserInfo', clientUserInfo);
  return (
    <Box className="AuthorityPage__placeholder">
      <Box className="AuthorityPage__placeholderContent">
        <Typography variant="h6" className="AuthorityPage__placeholderTitle">
          前台權限管理規劃中
        </Typography>
        <Typography color="text.secondary">
          目前僅提供 Dashboard
          權限管理。前台的角色與授權流程仍在規劃，待需求確認後會於此處開放設定。
        </Typography>
      </Box>
    </Box>
  );
}

export default FrontendAuthority;
