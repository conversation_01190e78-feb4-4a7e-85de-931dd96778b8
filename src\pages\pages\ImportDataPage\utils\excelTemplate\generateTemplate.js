/**
 * Excel 範本生成器
 * 根據 Firebase excelHeader 配置生成標準範本
 */
import * as Excel from "exceljs";

/**
 * 預設表頭配置（根據 API 文件：docs/EXCEL_IMPORT_FORMAT.md）
 * 當 Firebase 無法載入時使用
 */
export const DEFAULT_HEADERS = [
  { id: "Land-->collectionPlace", label: "典藏地", order: 1, required: false },
  { id: "Land-->operator", label: "經辦者", order: 2, required: false },
  { id: "Land-->boxNumber", label: "箱號", order: 3, required: false },
  { id: "Land-->number", label: "號數", order: 4, required: false },
  { id: "Land-->landName", label: "土地名稱", order: 5, required: true }, // 必填（與 landSerialNumber 同時存在）
  { id: "Land-->landSerialNumber", label: "地號", order: 6, required: true }, // 必填（與 landName 同時存在）
  { id: "Land-->abstract", label: "摘要", order: 7, required: false },
  { id: "Land-->source", label: "來源", order: 8, required: false },
  { id: "Land-->pawnRight", label: "典權", order: 9, required: false },
  { id: "Land-->plowingRight", label: "耕作權", order: 10, required: false },
  { id: "Land-->hasEvent-->LandMark-->landMarkNumber", label: "地籍編號", order: 11, required: false }, // 條件必填
  { id: "Land-->hasEvent-->LandMark-->cause", label: "地籍原因", order: 12, required: false },
  { id: "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent", label: "地籍開始日期", order: 13, required: false, format: "year" }, // 4位數年份
  { id: "Land-->hasEvent-->LandMark-->landGrades", label: "地等", order: 14, required: false },
  { id: "Land-->hasEvent-->LandMark-->landCategory", label: "地目", order: 15, required: false },
  { id: "Land-->hasEvent-->LandMark-->landArea", label: "土地面積", order: 16, required: false, format: "number" },
  { id: "Land-->hasEvent-->LandMark-->landRent", label: "租金", order: 17, required: false, format: "number" },
  { id: "Land-->hasEvent-->LandRights-->landRightsNumber", label: "地權編號", order: 18, required: false }, // 條件必填
  { id: "Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent", label: "地權開始日期", order: 19, required: false, format: "year" }, // 4位數年份
  { id: "Land-->hasEvent-->LandRights-->cause", label: "地權原因", order: 20, required: false },
  { id: "Land-->hasEvent-->LandRights-->hasOwner-->Owner", label: "業主", order: 21, required: false, format: "multiple" }, // 多值（逗號分隔）
];

/**
 * 生成空白範本
 * @param {Array} headers - 表頭配置陣列（從 Firebase 載入或使用預設）
 * @returns {Promise<Blob>} Excel 檔案 Blob
 */
export const generateTemplate = async (headers = DEFAULT_HEADERS) => {
  const workbook = new Excel.Workbook();
  const worksheet = workbook.addWorksheet("土地資料範本");

  // 設定欄寬
  worksheet.columns = headers.map(() => ({ width: 20 }));

  // 第 1 列: 欄位 ID
  const row1 = worksheet.getRow(1);
  headers.forEach((header, index) => {
    row1.getCell(index + 1).value = header.id;
  });
  row1.font = { bold: true, color: { argb: "FF0066CC" } };
  row1.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE7F3FF" },
  };
  row1.alignment = { vertical: "middle", horizontal: "left" };
  row1.height = 20;

  // 第 2 列: 範例資料 1（根據 API 規格）
  const row2 = worksheet.getRow(2);
  const exampleRow2 = {
    "Land-->collectionPlace": "國家檔案局",
    "Land-->operator": "張三",
    "Land-->boxNumber": "Box001",
    "Land-->number": "A001",
    "Land-->landName": "板橋林家",
    "Land-->landSerialNumber": "001",
    "Land-->abstract": "板橋林家土地契約書",
    "Land-->source": "1",
    "Land-->pawnRight": "Y",
    "Land-->plowingRight": "N",
    "Land-->hasEvent-->LandMark-->landMarkNumber": "LM001",
    "Land-->hasEvent-->LandMark-->cause": "購入",
    "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent": "1920",
    "Land-->hasEvent-->LandMark-->landGrades": "3",
    "Land-->hasEvent-->LandMark-->landCategory": "水田",
    "Land-->hasEvent-->LandMark-->landArea": "12.5",
    "Land-->hasEvent-->LandMark-->landRent": "150.75",
    "Land-->hasEvent-->LandRights-->landRightsNumber": "LR001",
    "Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent": "1920",
    "Land-->hasEvent-->LandRights-->cause": "買賣",
    "Land-->hasEvent-->LandRights-->hasOwner-->Owner": "林維源,林爾嘉",
  };
  headers.forEach((header, index) => {
    row2.getCell(index + 1).value = exampleRow2[header.id] || "";
  });
  row2.font = { italic: true, color: { argb: "FF808080" } };
  row2.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFFFFFF0" },
  };
  row2.alignment = { vertical: "middle", horizontal: "left" };
  row2.height = 20;

  // 第 3 列: 範例資料 2（根據 API 規格）
  const row3 = worksheet.getRow(3);
  const exampleRow3 = {
    "Land-->collectionPlace": "國史館臺灣文獻館",
    "Land-->operator": "李四",
    "Land-->boxNumber": "Box002",
    "Land-->number": "B001",
    "Land-->landName": "霧峰林家",
    "Land-->landSerialNumber": "002",
    "Land-->abstract": "霧峰林家土地契約書",
    "Land-->source": "2",
    "Land-->pawnRight": "N",
    "Land-->plowingRight": "Y",
    "Land-->hasEvent-->LandMark-->landMarkNumber": "LM002",
    "Land-->hasEvent-->LandMark-->cause": "繼承",
    "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent": "1925",
    "Land-->hasEvent-->LandMark-->landGrades": "2",
    "Land-->hasEvent-->LandMark-->landCategory": "旱田",
    "Land-->hasEvent-->LandMark-->landArea": "8.3",
    "Land-->hasEvent-->LandMark-->landRent": "98.50",
    "Land-->hasEvent-->LandRights-->landRightsNumber": "LR002",
    "Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent": "1925",
    "Land-->hasEvent-->LandRights-->cause": "繼承",
    "Land-->hasEvent-->LandRights-->hasOwner-->Owner": "林朝棟",
  };
  headers.forEach((header, index) => {
    row3.getCell(index + 1).value = exampleRow3[header.id] || "";
  });
  row3.font = { italic: true, color: { argb: "FF808080" } };
  row3.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFFFFFF0" },
  };
  row3.alignment = { vertical: "middle", horizontal: "left" };
  row3.height = 20;

  // 第 4 列起: 實際資料區域（加入 10 列空白供使用者填寫）
  for (let i = 4; i <= 13; i += 1) {
    const row = worksheet.getRow(i);
    row.height = 18;
    headers.forEach((_, index) => {
      const cell = row.getCell(index + 1);
      cell.border = {
        top: { style: "thin", color: { argb: "FFD0D0D0" } },
        left: { style: "thin", color: { argb: "FFD0D0D0" } },
        bottom: { style: "thin", color: { argb: "FFD0D0D0" } },
        right: { style: "thin", color: { argb: "FFD0D0D0" } },
      };
    });
  }

  // 凍結前 1 列（只凍結標題列）
  worksheet.views = [{ state: "frozen", ySplit: 1 }];

  // 生成 Buffer
  const buffer = await workbook.xlsx.writeBuffer();
  return new Blob([buffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });
};

/**
 * 生成範例檔案（包含範例資料）
 * @param {Array} headers - 表頭配置陣列
 * @returns {Promise<Blob>} Excel 檔案 Blob
 */
export const generateExample = async (headers = DEFAULT_HEADERS) => {
  const workbook = new Excel.Workbook();
  const worksheet = workbook.addWorksheet("土地資料範例");

  // 範例資料（根據 API 規格：Row 2-3 為範例，Row 4+ 為實際資料）
  const exampleData = [
    {
      "Land-->collectionPlace": "國家檔案局",
      "Land-->operator": "張三",
      "Land-->boxNumber": "Box001",
      "Land-->number": "A001",
      "Land-->landName": "板橋林家",
      "Land-->landSerialNumber": "001",
      "Land-->abstract": "板橋林家土地契約書",
      "Land-->source": "1",
      "Land-->pawnRight": "Y",
      "Land-->plowingRight": "N",
      "Land-->hasEvent-->LandMark-->landMarkNumber": "LM001",
      "Land-->hasEvent-->LandMark-->cause": "購入",
      "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent": "1920",
      "Land-->hasEvent-->LandMark-->landGrades": "3",
      "Land-->hasEvent-->LandMark-->landCategory": "水田",
      "Land-->hasEvent-->LandMark-->landArea": "12.5",
      "Land-->hasEvent-->LandMark-->landRent": "150.75",
      "Land-->hasEvent-->LandRights-->landRightsNumber": "LR001",
      "Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent": "1920",
      "Land-->hasEvent-->LandRights-->cause": "買賣",
      "Land-->hasEvent-->LandRights-->hasOwner-->Owner": "林維源,林爾嘉",
    },
    {
      "Land-->collectionPlace": "國史館臺灣文獻館",
      "Land-->operator": "李四",
      "Land-->boxNumber": "Box002",
      "Land-->number": "B001",
      "Land-->landName": "霧峰林家",
      "Land-->landSerialNumber": "002",
      "Land-->abstract": "霧峰林家土地契約書",
      "Land-->source": "2",
      "Land-->pawnRight": "N",
      "Land-->plowingRight": "Y",
      "Land-->hasEvent-->LandMark-->landMarkNumber": "LM002",
      "Land-->hasEvent-->LandMark-->cause": "繼承",
      "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent": "1925",
      "Land-->hasEvent-->LandMark-->landGrades": "2",
      "Land-->hasEvent-->LandMark-->landCategory": "旱田",
      "Land-->hasEvent-->LandMark-->landArea": "8.3",
      "Land-->hasEvent-->LandMark-->landRent": "98.50",
      "Land-->hasEvent-->LandRights-->landRightsNumber": "LR002",
      "Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent": "1925",
      "Land-->hasEvent-->LandRights-->cause": "繼承",
      "Land-->hasEvent-->LandRights-->hasOwner-->Owner": "林朝棟",
    },
    {
      "Land-->collectionPlace": "國史館臺灣文獻館",
      "Land-->operator": "王五",
      "Land-->boxNumber": "Box002",
      "Land-->number": "B002",
      "Land-->landName": "鹿港辜家",
      "Land-->landSerialNumber": "003",
      "Land-->abstract": "鹿港辜家土地契約書",
      "Land-->source": "0",
      "Land-->pawnRight": "Y",
      "Land-->plowingRight": "Y",
      "Land-->hasEvent-->LandMark-->landMarkNumber": "LM003",
      "Land-->hasEvent-->LandMark-->cause": "開墾",
      "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent": "1930",
      "Land-->hasEvent-->LandMark-->landGrades": "1",
      "Land-->hasEvent-->LandMark-->landCategory": "水田",
      "Land-->hasEvent-->LandMark-->landArea": "25.8",
      "Land-->hasEvent-->LandMark-->landRent": "320.00",
      "Land-->hasEvent-->LandRights-->landRightsNumber": "LR003",
      "Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent": "1930",
      "Land-->hasEvent-->LandRights-->cause": "開墾",
      "Land-->hasEvent-->LandRights-->hasOwner-->Owner": "辜顯榮,辜寬敏",
    },
  ];

  // 設定欄寬
  worksheet.columns = headers.map(() => ({ width: 20 }));

  // 第 1 列: 欄位 ID
  const row1 = worksheet.getRow(1);
  headers.forEach((header, index) => {
    row1.getCell(index + 1).value = header.id;
  });
  row1.font = { bold: true, color: { argb: "FF0066CC" } };
  row1.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE7F3FF" },
  };
  row1.alignment = { vertical: "middle", horizontal: "left" };
  row1.height = 20;

  // 第 2 列: 範例資料 1（固定範例數據，根據 API 規格）
  const row2 = worksheet.getRow(2);
  headers.forEach((header, index) => {
    row2.getCell(index + 1).value = exampleData[0][header.id] || "";
  });
  row2.font = { italic: true, color: { argb: "FF808080" } };
  row2.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFFFFFF0" },
  };
  row2.alignment = { vertical: "middle", horizontal: "left" };
  row2.height = 20;

  // 第 3 列: 範例資料 2（固定範例數據，根據 API 規格）
  const row3 = worksheet.getRow(3);
  headers.forEach((header, index) => {
    row3.getCell(index + 1).value = exampleData[1][header.id] || "";
  });
  row3.font = { italic: true, color: { argb: "FF808080" } };
  row3.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFFFFFF0" },
  };
  row3.alignment = { vertical: "middle", horizontal: "left" };
  row3.height = 20;

  // 填入額外範例資料（從第 4 列開始，給使用者更多範例）
  exampleData.forEach((data, rowIndex) => {
    const row = worksheet.getRow(4 + rowIndex);
    headers.forEach((header, colIndex) => {
      const cell = row.getCell(colIndex + 1);
      cell.value = data[header.id] || "";
      cell.border = {
        top: { style: "thin", color: { argb: "FFD0D0D0" } },
        left: { style: "thin", color: { argb: "FFD0D0D0" } },
        bottom: { style: "thin", color: { argb: "FFD0D0D0" } },
        right: { style: "thin", color: { argb: "FFD0D0D0" } },
      };
      
      // 根據資料類型設定對齊方式
      if (typeof cell.value === "number") {
        cell.alignment = { horizontal: "right" };
      } else {
        cell.alignment = { horizontal: "left" };
      }
    });
    row.height = 18;
  });

  // 凍結前 3 列
  worksheet.views = [{ state: "frozen", ySplit: 3 }];

  // 生成 Buffer
  const buffer = await workbook.xlsx.writeBuffer();
  return new Blob([buffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });
};

/**
 * 觸發檔案下載
 * @param {Blob} blob - 檔案 Blob
 * @param {string} filename - 檔案名稱
 */
export const downloadFile = (blob, filename) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};
