export const jsonExcel = (json, delimiter = ",") => {
  let output = "";
  if (Array.isArray(json) && json.length > 0) {
    const keys = Object.keys(json[0]);
    output += `${keys.toString()}\n`;
    json.forEach((item) => {
      keys.forEach((key) => {
        output += `${item[key]}${delimiter}`;
      });
      output += "\n";
    });
  } else {
    const keys = Object.keys(json);
    output += `${keys.toString()}\n`;
    keys.forEach((key) => {
      output += `${json[key]}${delimiter}`;
    });
    output += "\n";
  }
  return output;
};

export const jsonTxt = (json) => {
  let output = "";
  if (Array.isArray(json) && json.length > 0) {
    const keys = Object.keys(json[0]);
    json.forEach((item, index) => {
      output += `(${index + 1})\n`;
      keys.forEach((key) => {
        output += `${key}: ${item[key]}\n`;
      });
      output += "\n";
    });
  } else {
    const keys = Object.keys(json);
    keys.forEach((key) => {
      output += `${key}: ${json[key]}\n`;
    });
  }
  return output;
};

export const snaToExcel = (data) => {
  const { links, nodes } = data || {};
  if (links && links.length > 0) {
    const result = links.map((link) => ({
      From: nodes[link.source].name,
      To: nodes[link.target].name,
    }));
    return result;
  }
  return [];
};
