export const checkRes = {
  success: "success",
  failed: "failed",
};

export const splitTag = "，";

// 特定列
export const specRow = {
  idIdx: 1, // 匯入表格第一列
  labelIdx: 2, // 匯入表格第二列
  startRow: 4, // 匯入表格第四列
};

/**
 * 測試模式配置
 * 設為 false 時不會真的呼叫 API，只在前端顯示效果
 */
export const ENABLE_API_CALL = false; // true: 啟用 API 呼叫，false: 測試模式

/**
 * 驗證階段定義
 */
export const VALIDATION_STAGES = {
  IDLE: "idle",
  CHECKING_HEADER: "checking_header",
  CHECKING_CONTENT: "checking_content",
  COMPLETED: "completed",
};

/**
 * 驗證階段顯示文字
 */
export const VALIDATION_STAGE_TEXT = {
  [VALIDATION_STAGES.IDLE]: "準備驗證",
  [VALIDATION_STAGES.CHECKING_HEADER]: "檢查表頭格式",
  [VALIDATION_STAGES.CHECKING_CONTENT]: "檢查資料內容",
  [VALIDATION_STAGES.COMPLETED]: "驗證完成",
};
