import { checkLandRightsInfo, getOwners, landNameChange } from "./index";
import { isEmpty } from "../../../../utils";
import { Api, createOntoData, updateOntoData } from "../../../../api/land/Api";
import { historyType, recordHistory } from "./produceHistory";
import evtType from "../../../../utils/evtType";

const deleteLandRightsInfo = (
  allDeleteLandRightsID,
  landId,
  latestLandRightsInfo
) => {
  allDeleteLandRightsID.forEach((id) => {
    let srcValue = latestLandRightsInfo.filter(
      (item) => item.landRightsId === id
    );
    srcValue = srcValue.reduce(
      (cur, next) => ({ ...cur, [next.predicate]: next.value }),
      {}
    );

    const entrySrc = {
      graph: "south",
      classType: "Land",
      srcId: landId,
      value: {
        hasEvent: {
          srcId: id,
          classType: evtType.LandRights,
          value: srcValue,
        },
      },
    };

    const entryDst = {
      graph: "south",
      classType: "Land",
      srcId: landId,
    };
    // console.log(entrySrc, entryDst);
    updateOntoData(Api.restfulCRUD(), entrySrc, entryDst, -1, 0);
  });
};

// 處理Owner
const processOwnerInfo = async (
  tmpPreValue,
  tmpModifyValue,
  state,
  dispatch
) => {
  const latestOwnerList = await getOwners();
  const hasOwnerPre = tmpPreValue.find(
    (element) => element.predicate === "hasOwner"
  );
  const hasOwnerMod = tmpModifyValue.find(
    (element) => element.predicate === "hasOwner"
  );

  if (!hasOwnerPre && !hasOwnerMod) return; // hasOwner欄位沒有變動

  // Owner Name換成Owner label ID
  const srcOwners = !isEmpty(hasOwnerPre)
    ? hasOwnerPre.value.map(
        (name) => latestOwnerList.find((obj) => obj.value === name).label
      )
    : [];
  const dstOwners = hasOwnerMod.value.map(
    (name) => latestOwnerList.find((obj) => obj.value === name).label
  );

  // updateOwner
  if (!(isEmpty(srcOwners) && isEmpty(dstOwners))) {
    const entrySrc = {
      graph: "south",
      classType: evtType.LandRights,
      srcId: hasOwnerMod.landRightsId,
      value: {
        hasOwner: srcOwners,
      },
    };

    const entryDst = {
      graph: "south",
      classType: evtType.LandRights,
      srcId: hasOwnerMod.landRightsId,
      value: {
        hasOwner: dstOwners,
      },
    };

    // console.log(entrySrc, entryDst);
    updateOntoData(Api.restfulCRUD(), entrySrc, entryDst, -1, 0).then(
      (result) => {
        if (result) {
          // 更新local 的 資料
          landNameChange(state, dispatch);
        }
      }
    );
  }
};

const createLandRightsInfo = (newData, landId, edit, dispatch) => {
  if (isEmpty(newData)) return;
  newData.forEach((data, index) => {
    const srcValue = data
      .filter((item) => item.value.length !== 0)
      .filter((item) => item.predicate !== "hasOwner")
      .reduce((cur, next) => {
        if (next.predicate === "cause") {
          return { ...cur, [next.predicate]: next.value.join("，") };
        }
        return { ...cur, [next.predicate]: next.value };
      }, {});

    // create LandRightsEvent
    const date = new Date();
    const tmpLabel = `${landId}-${Date.parse(date)}-${index}`;
    const entry = {
      graph: "south",
      classType: "Land",
      srcId: landId,
      value: {
        hasEvent: {
          classType: evtType.LandRights,
          srcId: "",
          value: { ...srcValue, label: tmpLabel },
        },
      },
    };

    // console.log("entry ", entry);
    createOntoData(Api.restfulCRUD(), entry).then(async (result) => {
      if (result) {
        const latestLandRightsInfo = await checkLandRightsInfo(edit);
        // 從landRightNumber反查ID
        const dataLandRightsNumber = data.find(
          (element) => element.predicate === "landRightsNumber"
        ).value;
        const allLandRightsNum = latestLandRightsInfo.filter(
          (element) => element.predicate === "landRightsNumber"
        );
        const findLatestLandRightsID = allLandRightsNum.find(
          (element) => element.value === dataLandRightsNumber
        ).landRightsId;
        let tmpData = JSON.parse(JSON.stringify(data));
        tmpData = tmpData.map((element) => ({
          ...element,
          landRightsId: findLatestLandRightsID,
        }));
        processOwnerInfo([], tmpData, edit, dispatch);
      }
    });
  });
};

const updateLandRightsInfo = (preValue, modifyValue, state, dispatch) => {
  if (isEmpty(preValue) || isEmpty(modifyValue)) return;

  // 找出所有landRights ID
  let LRIDs = preValue.map((element) => element.landRightsId);
  LRIDs = LRIDs.filter((item, pos) => LRIDs.indexOf(item) === pos);

  LRIDs.forEach((LRId) => {
    // 從preValue和modifyValue找出相對應的landRightsId
    const tmpPreValue = preValue.filter(
      (element) => element.landRightsId === LRId
    );
    const tmpModifyValue = modifyValue.filter(
      (element) => element.landRightsId === LRId
    );

    // hasOwner需要增加、修改的人
    processOwnerInfo(tmpPreValue, tmpModifyValue, state, dispatch);

    const srcValue = tmpPreValue
      .filter((item) => item.value.length !== 0)
      .filter((item) => item.predicate !== "hasOwner")
      .reduce((cur, next) => {
        if (next.predicate === "cause") {
          return { ...cur, [next.predicate]: next.value.join("，") };
        }
        return { ...cur, [next.predicate]: next.value };
      }, {});
    const dstValue = tmpModifyValue
      .filter((item) => item.value.length !== 0)
      .filter((item) => item.predicate !== "hasOwner")
      .reduce((cur, next) => {
        if (next.predicate === "cause") {
          return { ...cur, [next.predicate]: next.value.join("，") };
        }
        return { ...cur, [next.predicate]: next.value };
      }, {});

    if (!(isEmpty(srcValue) && isEmpty(dstValue))) {
      const entrySrc = {
        graph: "south",
        classType: evtType.LandRights,
        srcId: LRId,
        value: srcValue,
      };

      const entryDst = {
        graph: "south",
        classType: evtType.LandRights,
        srcId: LRId,
        value: dstValue,
      };
      // console.log(entrySrc, entryDst);
      updateOntoData(Api.restfulCRUD(), entrySrc, entryDst, -1, 0);
    }
  });
};

export const compareLandRightsInfo = async (edit, dispatch) => {
  const { landRightInfo, basicInfo } = edit;
  const autoCompleteCol = ["cause", "hasOwner"]; // 會用到AutoComplete的欄位
  let landRightsInfoRecord = "";

  const { landId } = basicInfo.find((element) => element.landId);
  const latestLandRightsInfo = await checkLandRightsInfo(edit);
  // console.log("latestLandRightsInfo ", latestLandRightsInfo);
  // console.log("landRightsInfo ", landRightInfo);
  // 找出create item
  const latestLandRightsNumber = latestLandRightsInfo.filter(
    (item) => item.predicate === "landRightsNumber"
  );
  const allCreateData = landRightInfo.filter((element) => {
    // 找每個element的landRightsNumber value
    const findLandRightsId = element.find(
      (item) => item.predicate === "landRightsNumber"
    ).landRightsId;
    return !latestLandRightsNumber.find(
      (item) => item.landRightsId === findLandRightsId
    );
  });

  // console.log("landRights ", allCreateData);
  if (!isEmpty(allCreateData)) {
    // 清空新增資料的landRightsId
    const newData = allCreateData.map((info) =>
      info.map((data) => ({ ...data, landRightsId: "" }))
    );

    let tmpRecord = "";
    tmpRecord = recordHistory(newData, [], historyType.createLandRightInfo);
    if (tmpRecord) {
      landRightsInfoRecord += tmpRecord;
      createLandRightsInfo(newData, landId, edit, dispatch);
    }
  }

  // 找出delete item
  const latestLRIDs = latestLandRightsInfo.reduce((acc, { landRightsId }) => {
    acc[landRightsId] = landRightsId;
    return acc;
  }, {});

  const allLatestEvtId = Object.keys(latestLRIDs);
  const landRightsNumbers = landRightInfo.reduce((acc, curArr) => {
    const findObj = curArr.find((item) => item.landRightsId);
    if (findObj) {
      const { landRightsId } = findObj;
      acc[landRightsId] = landRightsId;
    }
    return acc;
  }, {});

  const allDeleteLandRightsID = allLatestEvtId.filter(
    (key) => !Object.keys(landRightsNumbers).includes(key)
  );

  // console.log("landRights ", allDeleteLandRightsID);
  if (!isEmpty(allDeleteLandRightsID)) {
    let tmpRecord = "";
    tmpRecord = recordHistory(
      allDeleteLandRightsID,
      [],
      historyType.deleteLandRightInfo,
      latestLandRightsInfo
    );
    if (tmpRecord) {
      landRightsInfoRecord += tmpRecord;
      deleteLandRightsInfo(allDeleteLandRightsID, landId, latestLandRightsInfo);
    }
  }

  // 找出現在畫面上與資料庫皆存在的landRights
  const allUpdateID = allLatestEvtId.filter((key) =>
    Object.keys(landRightsNumbers).includes(key)
  );
  const allUpdateData = landRightInfo.filter((arrEl) => {
    const findObj = arrEl.find((item) => item.landRightsId);
    if (findObj) {
      const { landRightsId } = findObj;
      return allUpdateID.includes(landRightsId);
    }
    return false;
  });

  // console.log("landRights ", allUpdateData);
  if (!isEmpty(allUpdateData)) {
    const preColumn = [];
    const modifyColumn = [];
    allUpdateData.forEach((info) => {
      info.forEach((data) => {
        const findObj = latestLandRightsInfo.find(
          (element) =>
            element.landRightsId === data.landRightsId &&
            element.predicate === data.predicate
        );
        if (findObj) {
          let preValue = data.value; // 畫面資料
          let modValue = findObj.value; // 資料庫最新資料
          if (autoCompleteCol.includes(data.predicate)) {
            preValue = data.value.join("，");
            modValue = findObj.value.join("，");
          }
          if (preValue !== modValue) {
            preColumn.push(findObj);
            modifyColumn.push(data);
          }
        } else if (autoCompleteCol.includes(data.predicate)) {
          if (data.value.length !== 0) {
            const emptyData = { ...data };
            emptyData.value = [];
            preColumn.push(emptyData);
            modifyColumn.push(data);
          }
        } else if (data.value !== "") {
          const emptyData = { ...data };
          emptyData.value = "";
          preColumn.push(emptyData);
          modifyColumn.push(data);
        }
      });
    });

    // console.log(preColumn, modifyColumn);
    let tmpRecord = "";
    tmpRecord += recordHistory(
      preColumn,
      modifyColumn,
      historyType.updateLandRightInfo,
      allUpdateData
    );
    if (tmpRecord) {
      landRightsInfoRecord += tmpRecord;
      updateLandRightsInfo(preColumn, modifyColumn, edit, dispatch);
    }
  }

  return landRightsInfoRecord;
};
