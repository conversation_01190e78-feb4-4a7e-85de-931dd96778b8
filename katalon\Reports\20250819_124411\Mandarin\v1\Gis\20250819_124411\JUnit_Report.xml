<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Gis" time="29.578" tests="6" failures="0" errors="0">
   <testsuite name="Gis" tests="6" failures="0" errors="0" time="29.578" skipped="0" timestamp="2025-08-19T04:44:14.585Z" hostname="saw63 - ************" id="Test Suites/Mandarin/v1/Gis">
      <properties>
         <property name="deviceName" value=""/>
         <property name="devicePlatform"/>
         <property name="logFolder" value="C:\\project\\land-web\\katalon\\Reports\\20250819_124411\\Mandarin\\v1\\Gis\\20250819_124411"/>
         <property name="logFiles" value="C:\\project\\land-web\\katalon\\Reports\\20250819_124411\\Mandarin\\v1\\Gis\\20250819_124411\\console0.log, C:\\project\\land-web\\katalon\\Reports\\20250819_124411\\Mandarin\\v1\\Gis\\20250819_124411\\execution0.log"/>
         <property name="attachments" value=""/>
         <property name="hostName" value="saw63 - ************"/>
         <property name="os" value="Windows 11 64bit"/>
         <property name="katalonVersion" value="********"/>
         <property name="browser" value="Chrome 138.0.7204.185"/>
         <property name="userFullName" value="Lin Kai Yee"/>
         <property name="hostAddress" value="************"/>
         <property name="sessionId" value="51f75141b54369cc80422e8f4548a289"/>
         <property name="projectName" value="katalon"/>
         <property name="seleniumVersion" value="4.28.1"/>
         <property name="proxyInformation" value="ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }"/>
         <property name="platform" value="Windows 11"/>
      </properties>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/GoToGis" time="2.295" classname="Test Cases/Mandarin/v1_web/Gis/GoToGis" status="PASSED">
         <system-out><![CDATA[19-08-2025T12:44:17 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/GoToGis: Test Cases/Mandarin/v1_web/Gis/GoToGis

19-08-2025T12:44:17 - [TEST_STEP][PASSED] - openBrowser(""): Browser is opened with url: ''

19-08-2025T12:44:17 - [MESSAGE][WARNING] - A browser is already opened. Closing browser and opening a new one

19-08-2025T12:44:17 - [MESSAGE][INFO] - Starting 'Chrome' driver

19-08-2025T12:44:17 - [MESSAGE][INFO] - Action delay is set to 0 milliseconds

19-08-2025T12:44:18 - [MESSAGE][PASSED] - Browser is opened with url: ''

19-08-2025T12:44:18 - [TEST_STEP][PASSED] - navigateToUrl("http://localhost:3000/Gis"): Navigate to 'http://localhost:3000/Gis' successfully

19-08-2025T12:44:19 - [MESSAGE][PASSED] - Navigate to 'http://localhost:3000/Gis' successfully]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/LoginWithEmail" time="3.897" classname="Test Cases/Mandarin/v1_web/Gis/LoginWithEmail" status="PASSED">
         <system-out><![CDATA[19-08-2025T12:44:19 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/LoginWithEmail: Test Cases/Mandarin/v1_web/Gis/LoginWithEmail

19-08-2025T12:44:19 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_' is clicked on

19-08-2025T12:44:20 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_' is clicked on

19-08-2025T12:44:20 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail' is clicked on

19-08-2025T12:44:20 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail' is clicked on

19-08-2025T12:44:20 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Email_email"), "<EMAIL>"): Text '<EMAIL>' is set on object 'Object Repository/Mandarin/v1_web/Gis/input_Email_email'

19-08-2025T12:44:21 - [MESSAGE][PASSED] - Text '<EMAIL>' is set on object 'Object Repository/Mandarin/v1_web/Gis/input_Email_email'

19-08-2025T12:44:21 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Next")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_Next' is clicked on

19-08-2025T12:44:22 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_Next' is clicked on

19-08-2025T12:44:22 - [TEST_STEP][PASSED] - setEncryptedText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Password_password"), "0D41p6Ct1VJ35FCiyaw3Gw=="): Text ****** has been set on object 'Object Repository/Mandarin/v1_web/Gis/input_Password_password'

19-08-2025T12:44:23 - [MESSAGE][PASSED] - Text ****** has been set on object 'Object Repository/Mandarin/v1_web/Gis/input_Password_password'

19-08-2025T12:44:23 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign In")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign In' is clicked on

19-08-2025T12:44:23 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign In' is clicked on]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/SearchTextInGis" time="3.588" classname="Test Cases/Mandarin/v1_web/Gis/SearchTextInGis" status="PASSED">
         <system-out><![CDATA[19-08-2025T12:44:23 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/SearchTextInGis: Test Cases/Mandarin/v1_web/Gis/SearchTextInGis

19-08-2025T12:44:23 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button__1")): Object: 'Object Repository/Mandarin/v1_web/Gis/button__1' is clicked on

19-08-2025T12:44:25 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button__1' is clicked on

19-08-2025T12:44:25 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input__mui-3"), "新北勢"): Text '新北勢' is set on object 'Object Repository/Mandarin/v1_web/Gis/input__mui-3'

19-08-2025T12:44:26 - [MESSAGE][PASSED] - Text '新北勢' is set on object 'Object Repository/Mandarin/v1_web/Gis/input__mui-3']]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/ChoosePerPage" time="1.556" classname="Test Cases/Mandarin/v1_web/Gis/ChoosePerPage" status="PASSED">
         <system-out><![CDATA[19-08-2025T12:44:26 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/ChoosePerPage: Test Cases/Mandarin/v1_web/Gis/ChoosePerPage

19-08-2025T12:44:27 - [TEST_STEP][PASSED] - dropdownButton = new com.kms.katalon.core.testobject.TestObject(): null

19-08-2025T12:44:27 - [TEST_STEP][PASSED] - dropdownButton.addProperty("css", EQUALS, "div[role="combobox"]"): null

19-08-2025T12:44:27 - [TEST_STEP][PASSED] - click(dropdownButton): Object: '' is clicked on

19-08-2025T12:44:27 - [MESSAGE][PASSED] - Object: '' is clicked on

19-08-2025T12:44:27 - [TEST_STEP][PASSED] - option50 = new com.kms.katalon.core.testobject.TestObject(): null

19-08-2025T12:44:27 - [TEST_STEP][PASSED] - option50.addProperty("css", EQUALS, "li.MuiMenuItem-root[data-value="50"]"): null

19-08-2025T12:44:27 - [TEST_STEP][PASSED] - waitForElementVisible(option50, 5): Object '' is visible

19-08-2025T12:44:27 - [MESSAGE][PASSED] - Object '' is visible

19-08-2025T12:44:27 - [TEST_STEP][PASSED] - click(option50): Object: '' is clicked on

19-08-2025T12:44:28 - [MESSAGE][PASSED] - Object: '' is clicked on]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/EditCoordinates" time="7.948" classname="Test Cases/Mandarin/v1_web/Gis/EditCoordinates" status="PASSED">
         <system-out><![CDATA[19-08-2025T12:44:28 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/EditCoordinates: Test Cases/Mandarin/v1_web/Gis/EditCoordinates

19-08-2025T12:44:28 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "148485"): Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'

19-08-2025T12:44:29 - [MESSAGE][PASSED] - Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'

19-08-2025T12:44:29 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "2406567 "): Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y'

19-08-2025T12:44:30 - [MESSAGE][PASSED] - Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y'

19-08-2025T12:44:30 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "148485"): Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x'

19-08-2025T12:44:31 - [MESSAGE][PASSED] - Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x'

19-08-2025T12:44:31 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "2406567 "): Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y'

19-08-2025T12:44:32 - [MESSAGE][PASSED] - Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y'

19-08-2025T12:44:32 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination")): Object: 'Object Repository/Mandarin/v1_web/Gis/pagination' is clicked on

19-08-2025T12:44:33 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/pagination' is clicked on

19-08-2025T12:44:33 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "148485"): Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x'

19-08-2025T12:44:34 - [MESSAGE][PASSED] - Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x'

19-08-2025T12:44:34 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "2406567 "): Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y'

19-08-2025T12:44:35 - [MESSAGE][PASSED] - Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y'

19-08-2025T12:44:35 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination_1")): Object: 'Object Repository/Mandarin/v1_web/Gis/pagination_1' is clicked on

19-08-2025T12:44:36 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/pagination_1' is clicked on]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/SaveAndVerify" time="7.684" classname="Test Cases/Mandarin/v1_web/Gis/SaveAndVerify" status="PASSED">
         <system-out><![CDATA[19-08-2025T12:44:36 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/SaveAndVerify: Test Cases/Mandarin/v1_web/Gis/SaveAndVerify

19-08-2025T12:44:36 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_save")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_save' is clicked on

19-08-2025T12:44:36 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_save' is clicked on

19-08-2025T12:44:36 - [TEST_STEP][PASSED] - waitForElementVisible(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button"), 5): Object 'Object Repository/Mandarin/v1_web/Gis/save_modal_save_button' is visible

19-08-2025T12:44:37 - [MESSAGE][PASSED] - Object 'Object Repository/Mandarin/v1_web/Gis/save_modal_save_button' is visible

19-08-2025T12:44:37 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button")): Object: 'Object Repository/Mandarin/v1_web/Gis/save_modal_save_button' is clicked on

19-08-2025T12:44:37 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/save_modal_save_button' is clicked on

19-08-2025T12:44:37 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "value"), "148485"): Actual object '148485' and expected object '148485' are equal

19-08-2025T12:44:43 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x' is: '148485'

19-08-2025T12:44:43 - [MESSAGE][PASSED] - Actual object '148485' and expected object '148485' are equal

19-08-2025T12:44:43 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "value"), "2406567"): Actual object '2406567' and expected object '2406567' are equal

19-08-2025T12:44:43 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y' is: '2406567'

19-08-2025T12:44:43 - [MESSAGE][PASSED] - Actual object '2406567' and expected object '2406567' are equal

19-08-2025T12:44:43 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "value"), "148485"): Actual object '148485' and expected object '148485' are equal

19-08-2025T12:44:43 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x' is: '148485'

19-08-2025T12:44:43 - [MESSAGE][PASSED] - Actual object '148485' and expected object '148485' are equal

19-08-2025T12:44:43 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "value"), "2406567"): Actual object '2406567' and expected object '2406567' are equal

19-08-2025T12:44:43 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y' is: '2406567'

19-08-2025T12:44:43 - [MESSAGE][PASSED] - Actual object '2406567' and expected object '2406567' are equal

19-08-2025T12:44:43 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination")): Object: 'Object Repository/Mandarin/v1_web/Gis/pagination' is clicked on

19-08-2025T12:44:44 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/pagination' is clicked on

19-08-2025T12:44:44 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "value"), "148485"): Actual object '148485' and expected object '148485' are equal

19-08-2025T12:44:44 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x' is: '148485'

19-08-2025T12:44:44 - [MESSAGE][PASSED] - Actual object '148485' and expected object '148485' are equal

19-08-2025T12:44:44 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "value"), "2406567"): Actual object '2406567' and expected object '2406567' are equal

19-08-2025T12:44:44 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y' is: '2406567'

19-08-2025T12:44:44 - [MESSAGE][PASSED] - Actual object '2406567' and expected object '2406567' are equal]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <system-out><![CDATA[19-08-2025T12:44:14 - [TEST_STEP][PASSED] - Start listener action : beforeSuite: Current window maximized

19-08-2025T12:44:14 - [TEST_STEP][PASSED] - openBrowser(""): Browser is opened with url: ''

19-08-2025T12:44:14 - [MESSAGE][INFO] - Starting 'Chrome' driver

19-08-2025T12:44:15 - [MESSAGE][INFO] - Action delay is set to 0 milliseconds

19-08-2025T12:44:16 - [MESSAGE][PASSED] - Browser is opened with url: ''

19-08-2025T12:44:16 - [TEST_STEP][PASSED] - maximizeWindow(): Current window maximized

19-08-2025T12:44:16 - [MESSAGE][PASSED] - Current window maximized

19-08-2025T12:44:14 - [TEST_SUITE][PASSED] - Gis: null

19-08-2025T12:44:44 - [TEST_STEP][PASSED] - Start listener action : afterSuite: Browser is closed

19-08-2025T12:44:44 - [TEST_STEP][PASSED] - closeBrowser(): Browser is closed

19-08-2025T12:44:44 - [MESSAGE][PASSED] - Browser is closed]]></system-out>
      <system-err><![CDATA[]]></system-err>
   </testsuite>
</testsuites>
