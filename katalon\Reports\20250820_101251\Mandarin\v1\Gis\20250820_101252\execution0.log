<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE log SYSTEM "logger.dtd">
<log>
<record>
  <date>2025-08-20T02:12:55.780318200Z</date>
  <millis>1755655975780</millis>
  <nanos>318200</nanos>
  <sequence>0</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startSuite</method>
  <thread>1</thread>
  <message>Start Test Suite : Test Suites/Mandarin/v1/Gis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="rerunTestFailImmediately">false</property>
  <property name="retryCount">0</property>
  <property name="name">Gis</property>
  <property name="description"></property>
  <property name="id">Test Suites/Mandarin/v1/Gis</property>
</record>
<record>
  <date>2025-08-20T02:12:55.801818800Z</date>
  <millis>1755655975801</millis>
  <nanos>818800</nanos>
  <sequence>1</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>userFullName = Lin Kai Yee</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="userFullName">Lin Kai Yee</property>
</record>
<record>
  <date>2025-08-20T02:12:55.805930300Z</date>
  <millis>1755655975805</millis>
  <nanos>930300</nanos>
  <sequence>2</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>projectName = katalon</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="projectName">katalon</property>
</record>
<record>
  <date>2025-08-20T02:12:55.805930300Z</date>
  <millis>1755655975805</millis>
  <nanos>930300</nanos>
  <sequence>3</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>hostName = saw63 - host.docker.internal</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="hostName">saw63 - host.docker.internal</property>
</record>
<record>
  <date>2025-08-20T02:12:55.805930300Z</date>
  <millis>1755655975805</millis>
  <nanos>930300</nanos>
  <sequence>4</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>os = Windows 11 64bit</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="os">Windows 11 64bit</property>
</record>
<record>
  <date>2025-08-20T02:12:55.805930300Z</date>
  <millis>1755655975805</millis>
  <nanos>930300</nanos>
  <sequence>5</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>hostAddress = ************</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="hostAddress">************</property>
</record>
<record>
  <date>2025-08-20T02:12:55.805930300Z</date>
  <millis>1755655975805</millis>
  <nanos>930300</nanos>
  <sequence>6</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>katalonVersion = ********</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="katalonVersion">********</property>
</record>
<record>
  <date>2025-08-20T02:12:55.820200500Z</date>
  <millis>1755655975820</millis>
  <nanos>200500</nanos>
  <sequence>7</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start listener action : beforeSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:12:55.856424100Z</date>
  <millis>1755655975856</millis>
  <nanos>424100</nanos>
  <sequence>9</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T02:12:56.107885900Z</date>
  <millis>1755655976107</millis>
  <nanos>885900</nanos>
  <sequence>11</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Starting &amp;apos;Chrome&amp;apos; driver</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:12:56.202044Z</date>
  <millis>1755655976202</millis>
  <nanos>44000</nanos>
  <sequence>12</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Action delay is set to 0 milliseconds</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:12:58.000385200Z</date>
  <millis>1755655978000</millis>
  <nanos>385200</nanos>
  <sequence>14</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>sessionId = 4ef896f4413fa54bcac175bb25b9b142</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="sessionId">4ef896f4413fa54bcac175bb25b9b142</property>
</record>
<record>
  <date>2025-08-20T02:12:58.002025Z</date>
  <millis>1755655978002</millis>
  <nanos>25000</nanos>
  <sequence>15</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>browser = Chrome 138.0.7204.185</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="browser">Chrome 138.0.7204.185</property>
</record>
<record>
  <date>2025-08-20T02:12:58.003035300Z</date>
  <millis>1755655978003</millis>
  <nanos>35300</nanos>
  <sequence>16</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>platform = Windows 11</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="platform">Windows 11</property>
</record>
<record>
  <date>2025-08-20T02:12:58.003035300Z</date>
  <millis>1755655978003</millis>
  <nanos>35300</nanos>
  <sequence>17</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>seleniumVersion = 4.28.1</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="seleniumVersion">4.28.1</property>
</record>
<record>
  <date>2025-08-20T02:12:58.011034200Z</date>
  <millis>1755655978011</millis>
  <nanos>34200</nanos>
  <sequence>18</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&amp;quot;&amp;quot;, isApplyToDesiredCapabilities=true }</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="proxyInformation">ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }</property>
</record>
<record>
  <date>2025-08-20T02:12:58.050150700Z</date>
  <millis>1755655978050</millis>
  <nanos>150700</nanos>
  <sequence>19</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is opened with url: &amp;apos;&amp;apos;</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.OpenBrowserKeyword.openBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:12:58.052241100Z</date>
  <millis>1755655978052</millis>
  <nanos>241100</nanos>
  <sequence>20</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:12:58.053325200Z</date>
  <millis>1755655978053</millis>
  <nanos>325200</nanos>
  <sequence>21</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : maximizeWindow()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T02:12:58.107100500Z</date>
  <millis>1755655978107</millis>
  <nanos>100500</nanos>
  <sequence>23</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Current window maximized</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.MaximizeWindowKeyword.maximizeWindow</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:12:58.108116800Z</date>
  <millis>1755655978108</millis>
  <nanos>116800</nanos>
  <sequence>25</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : maximizeWindow()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:12:58.110112100Z</date>
  <millis>1755655978110</millis>
  <nanos>112100</nanos>
  <sequence>26</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End listener action : beforeSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:12:58.536063300Z</date>
  <millis>1755655978536</millis>
  <nanos>63300</nanos>
  <sequence>27</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/GoToGis</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/GoToGis</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\GoToGis.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T02:12:58.605660400Z</date>
  <millis>1755655978605</millis>
  <nanos>660400</nanos>
  <sequence>29</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">20</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T02:12:58.610569100Z</date>
  <millis>1755655978610</millis>
  <nanos>569100</nanos>
  <sequence>31</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>A browser is already opened. Closing browser and opening a new one</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:12:58.789949500Z</date>
  <millis>1755655978789</millis>
  <nanos>949500</nanos>
  <sequence>32</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Starting &amp;apos;Chrome&amp;apos; driver</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:12:58.792489900Z</date>
  <millis>1755655978792</millis>
  <nanos>489900</nanos>
  <sequence>33</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Action delay is set to 0 milliseconds</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:12:59.737024500Z</date>
  <millis>1755655979737</millis>
  <nanos>24500</nanos>
  <sequence>35</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>sessionId = 93ce76b7168566a82179e5a5c1a9ff22</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="sessionId">93ce76b7168566a82179e5a5c1a9ff22</property>
</record>
<record>
  <date>2025-08-20T02:12:59.737024500Z</date>
  <millis>1755655979737</millis>
  <nanos>24500</nanos>
  <sequence>36</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>browser = Chrome 138.0.7204.185</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="browser">Chrome 138.0.7204.185</property>
</record>
<record>
  <date>2025-08-20T02:12:59.737024500Z</date>
  <millis>1755655979737</millis>
  <nanos>24500</nanos>
  <sequence>37</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>platform = Windows 11</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="platform">Windows 11</property>
</record>
<record>
  <date>2025-08-20T02:12:59.737024500Z</date>
  <millis>1755655979737</millis>
  <nanos>24500</nanos>
  <sequence>38</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>seleniumVersion = 4.28.1</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="seleniumVersion">4.28.1</property>
</record>
<record>
  <date>2025-08-20T02:12:59.737024500Z</date>
  <millis>1755655979737</millis>
  <nanos>24500</nanos>
  <sequence>39</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&amp;quot;&amp;quot;, isApplyToDesiredCapabilities=true }</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="proxyInformation">ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }</property>
</record>
<record>
  <date>2025-08-20T02:12:59.762074Z</date>
  <millis>1755655979762</millis>
  <nanos>74000</nanos>
  <sequence>40</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is opened with url: &amp;apos;&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.OpenBrowserKeyword.openBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:12:59.765104100Z</date>
  <millis>1755655979765</millis>
  <nanos>104100</nanos>
  <sequence>41</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:12:59.765645700Z</date>
  <millis>1755655979765</millis>
  <nanos>645700</nanos>
  <sequence>42</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : navigateToUrl(&amp;quot;https://land2.daoyidh.com/Gis&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T02:13:00.132111900Z</date>
  <millis>1755655980132</millis>
  <nanos>111900</nanos>
  <sequence>45</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Navigate to &amp;apos;https://land2.daoyidh.com/Gis&amp;apos; successfully</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.NavigateToUrlKeyword.navigateToUrl</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:00.134114600Z</date>
  <millis>1755655980134</millis>
  <nanos>114600</nanos>
  <sequence>46</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : navigateToUrl(&amp;quot;https://land2.daoyidh.com/Gis&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:00.135120600Z</date>
  <millis>1755655980135</millis>
  <nanos>120600</nanos>
  <sequence>47</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:00.137119300Z</date>
  <millis>1755655980137</millis>
  <nanos>119300</nanos>
  <sequence>48</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T02:13:00.144111800Z</date>
  <millis>1755655980144</millis>
  <nanos>111800</nanos>
  <sequence>49</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</property>
  <property name="description">Login</property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\LoginWithEmail.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T02:13:00.215790700Z</date>
  <millis>1755655980215</millis>
  <nanos>790700</nanos>
  <sequence>51</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">20</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T02:13:01.612321200Z</date>
  <millis>1755655981612</millis>
  <nanos>321200</nanos>
  <sequence>61</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:01.613322Z</date>
  <millis>1755655981613</millis>
  <nanos>322000</nanos>
  <sequence>62</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:01.614322700Z</date>
  <millis>1755655981614</millis>
  <nanos>322700</nanos>
  <sequence>63</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T02:13:02.145897200Z</date>
  <millis>1755655982145</millis>
  <nanos>897200</nanos>
  <sequence>73</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:02.149367300Z</date>
  <millis>1755655982149</millis>
  <nanos>367300</nanos>
  <sequence>74</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:02.151862700Z</date>
  <millis>1755655982151</millis>
  <nanos>862700</nanos>
  <sequence>75</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;quot;), &amp;quot;<EMAIL>&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">24</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-20T02:13:02.963943200Z</date>
  <millis>1755655982963</millis>
  <nanos>943200</nanos>
  <sequence>91</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;<EMAIL>&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:02.963943200Z</date>
  <millis>1755655982963</millis>
  <nanos>943200</nanos>
  <sequence>92</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;quot;), &amp;quot;<EMAIL>&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:02.963943200Z</date>
  <millis>1755655982963</millis>
  <nanos>943200</nanos>
  <sequence>93</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-20T02:13:03.245064400Z</date>
  <millis>1755655983245</millis>
  <nanos>64400</nanos>
  <sequence>103</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:03.247063500Z</date>
  <millis>1755655983247</millis>
  <nanos>63500</nanos>
  <sequence>104</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:03.247063500Z</date>
  <millis>1755655983247</millis>
  <nanos>63500</nanos>
  <sequence>105</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setEncryptedText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;quot;), &amp;quot;0D41p6Ct1VJ35FCiyaw3Gw==&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">28</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-20T02:13:04.191879300Z</date>
  <millis>1755655984191</millis>
  <nanos>879300</nanos>
  <sequence>116</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text ****** has been set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetEncryptedTextKeyword.setEncryptedText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:04.202788500Z</date>
  <millis>1755655984202</millis>
  <nanos>788500</nanos>
  <sequence>117</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setEncryptedText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;quot;), &amp;quot;0D41p6Ct1VJ35FCiyaw3Gw==&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:04.203802700Z</date>
  <millis>1755655984203</millis>
  <nanos>802700</nanos>
  <sequence>118</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-20T02:13:04.479500100Z</date>
  <millis>1755655984479</millis>
  <nanos>500100</nanos>
  <sequence>128</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:04.479500100Z</date>
  <millis>1755655984479</millis>
  <nanos>500100</nanos>
  <sequence>129</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:04.479500100Z</date>
  <millis>1755655984479</millis>
  <nanos>500100</nanos>
  <sequence>130</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:04.483499800Z</date>
  <millis>1755655984483</millis>
  <nanos>499800</nanos>
  <sequence>131</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T02:13:04.486498700Z</date>
  <millis>1755655984486</millis>
  <nanos>498700</nanos>
  <sequence>132</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\SearchTextInGis.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T02:13:04.531583Z</date>
  <millis>1755655984531</millis>
  <nanos>583000</nanos>
  <sequence>134</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button__1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T02:13:07.151337600Z</date>
  <millis>1755655987151</millis>
  <nanos>337600</nanos>
  <sequence>144</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button__1&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:07.152336600Z</date>
  <millis>1755655987152</millis>
  <nanos>336600</nanos>
  <sequence>145</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button__1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:07.153336300Z</date>
  <millis>1755655987153</millis>
  <nanos>336300</nanos>
  <sequence>146</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;quot;), &amp;quot;新北勢&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">25</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T02:13:08.882396900Z</date>
  <millis>1755655988882</millis>
  <nanos>396900</nanos>
  <sequence>158</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;新北勢&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:08.883407500Z</date>
  <millis>1755655988883</millis>
  <nanos>407500</nanos>
  <sequence>159</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;quot;), &amp;quot;新北勢&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:08.884411900Z</date>
  <millis>1755655988884</millis>
  <nanos>411900</nanos>
  <sequence>160</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:08.885818500Z</date>
  <millis>1755655988885</millis>
  <nanos>818500</nanos>
  <sequence>161</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T02:13:08.889872100Z</date>
  <millis>1755655988889</millis>
  <nanos>872100</nanos>
  <sequence>162</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\ChoosePerPage.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T02:13:09.017997500Z</date>
  <millis>1755655989017</millis>
  <nanos>997500</nanos>
  <sequence>164</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">24</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T02:13:09.032502700Z</date>
  <millis>1755655989032</millis>
  <nanos>502700</nanos>
  <sequence>165</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:09.033634600Z</date>
  <millis>1755655989033</millis>
  <nanos>634600</nanos>
  <sequence>166</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;div[role=&amp;quot;button&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">25</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T02:13:09.047544300Z</date>
  <millis>1755655989047</millis>
  <nanos>544300</nanos>
  <sequence>167</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;div[role=&amp;quot;button&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:09.047544300Z</date>
  <millis>1755655989047</millis>
  <nanos>544300</nanos>
  <sequence>168</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(dropdownButton)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-20T02:13:09.516333100Z</date>
  <millis>1755655989516</millis>
  <nanos>333100</nanos>
  <sequence>177</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:09.518333500Z</date>
  <millis>1755655989518</millis>
  <nanos>333500</nanos>
  <sequence>178</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(dropdownButton)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:09.519597300Z</date>
  <millis>1755655989519</millis>
  <nanos>597300</nanos>
  <sequence>179</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : option50 = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-20T02:13:09.522617400Z</date>
  <millis>1755655989522</millis>
  <nanos>617400</nanos>
  <sequence>180</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : option50 = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:09.522617400Z</date>
  <millis>1755655989522</millis>
  <nanos>617400</nanos>
  <sequence>181</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : option50.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;li.MuiMenuItem-root[data-value=&amp;quot;50&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-20T02:13:09.525616200Z</date>
  <millis>1755655989525</millis>
  <nanos>616200</nanos>
  <sequence>182</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : option50.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;li.MuiMenuItem-root[data-value=&amp;quot;50&amp;quot;]&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:09.526615900Z</date>
  <millis>1755655989526</millis>
  <nanos>615900</nanos>
  <sequence>183</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : waitForElementVisible(option50, 5)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">31</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-20T02:13:10.117768500Z</date>
  <millis>1755655990117</millis>
  <nanos>768500</nanos>
  <sequence>192</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object &amp;apos;&amp;apos; is visible</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call</property>
  <property name="testops-execution-stacktrace">com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:324)
com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:319)
com.kms.katalon.core.logging.KeywordLogger$logPassed$0.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:84)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
ChoosePerPage.run(ChoosePerPage:31)
</property>
</record>
<record>
  <date>2025-08-20T02:13:10.119809500Z</date>
  <millis>1755655990119</millis>
  <nanos>809500</nanos>
  <sequence>193</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : waitForElementVisible(option50, 5)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:10.121026700Z</date>
  <millis>1755655990121</millis>
  <nanos>26700</nanos>
  <sequence>194</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(option50)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">32</property>
  <property name="stepIndex">7</property>
</record>
<record>
  <date>2025-08-20T02:13:11.095400300Z</date>
  <millis>1755655991095</millis>
  <nanos>400300</nanos>
  <sequence>203</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:11.097412800Z</date>
  <millis>1755655991097</millis>
  <nanos>412800</nanos>
  <sequence>204</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(option50)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:11.097412800Z</date>
  <millis>1755655991097</millis>
  <nanos>412800</nanos>
  <sequence>205</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:11.099445900Z</date>
  <millis>1755655991099</millis>
  <nanos>445900</nanos>
  <sequence>206</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T02:13:11.105061Z</date>
  <millis>1755655991105</millis>
  <nanos>61000</nanos>
  <sequence>207</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/EditCoordinates</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/EditCoordinates</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/EditCoordinates</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\EditCoordinates.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T02:13:11.292460300Z</date>
  <millis>1755655991292</millis>
  <nanos>460300</nanos>
  <sequence>209</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">21</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T02:13:12.208633800Z</date>
  <millis>1755655992208</millis>
  <nanos>633800</nanos>
  <sequence>221</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;148485&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:12.216231100Z</date>
  <millis>1755655992216</millis>
  <nanos>231100</nanos>
  <sequence>222</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:12.217571900Z</date>
  <millis>1755655992217</millis>
  <nanos>571900</nanos>
  <sequence>223</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T02:13:12.832284200Z</date>
  <millis>1755655992832</millis>
  <nanos>284200</nanos>
  <sequence>235</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;2406567 &amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:12.835516900Z</date>
  <millis>1755655992835</millis>
  <nanos>516900</nanos>
  <sequence>236</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:12.836588600Z</date>
  <millis>1755655992836</millis>
  <nanos>588600</nanos>
  <sequence>237</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">23</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-20T02:13:13.298306400Z</date>
  <millis>1755655993298</millis>
  <nanos>306400</nanos>
  <sequence>249</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;148485&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:13.301313300Z</date>
  <millis>1755655993301</millis>
  <nanos>313300</nanos>
  <sequence>250</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:13.301313300Z</date>
  <millis>1755655993301</millis>
  <nanos>313300</nanos>
  <sequence>251</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">24</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-20T02:13:13.812646400Z</date>
  <millis>1755655993812</millis>
  <nanos>646400</nanos>
  <sequence>263</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;2406567 &amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:13.812646400Z</date>
  <millis>1755655993812</millis>
  <nanos>646400</nanos>
  <sequence>264</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:13.815459200Z</date>
  <millis>1755655993815</millis>
  <nanos>459200</nanos>
  <sequence>265</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-20T02:13:14.471006600Z</date>
  <millis>1755655994471</millis>
  <nanos>6600</nanos>
  <sequence>275</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/pagination&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:14.472009200Z</date>
  <millis>1755655994472</millis>
  <nanos>9200</nanos>
  <sequence>276</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:14.473007400Z</date>
  <millis>1755655994473</millis>
  <nanos>7400</nanos>
  <sequence>277</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">28</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-20T02:13:14.893498Z</date>
  <millis>1755655994893</millis>
  <nanos>498000</nanos>
  <sequence>289</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;148485&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:14.894499200Z</date>
  <millis>1755655994894</millis>
  <nanos>499200</nanos>
  <sequence>290</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:14.894499200Z</date>
  <millis>1755655994894</millis>
  <nanos>499200</nanos>
  <sequence>291</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">7</property>
</record>
<record>
  <date>2025-08-20T02:13:15.349461400Z</date>
  <millis>1755655995349</millis>
  <nanos>461400</nanos>
  <sequence>303</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;2406567 &amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:15.349461400Z</date>
  <millis>1755655995349</millis>
  <nanos>461400</nanos>
  <sequence>304</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;quot;), &amp;quot;2406567 &amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:15.349461400Z</date>
  <millis>1755655995349</millis>
  <nanos>461400</nanos>
  <sequence>305</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination_1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">31</property>
  <property name="stepIndex">8</property>
</record>
<record>
  <date>2025-08-20T02:13:16.002989100Z</date>
  <millis>1755655996002</millis>
  <nanos>989100</nanos>
  <sequence>315</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/pagination_1&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:16.008013700Z</date>
  <millis>1755655996008</millis>
  <nanos>13700</nanos>
  <sequence>316</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination_1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:16.009025700Z</date>
  <millis>1755655996009</millis>
  <nanos>25700</nanos>
  <sequence>317</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyElementVisible(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">33</property>
  <property name="stepIndex">9</property>
</record>
<record>
  <date>2025-08-20T02:13:16.045481800Z</date>
  <millis>1755655996045</millis>
  <nanos>481800</nanos>
  <sequence>323</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint&amp;apos; is visible</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.VerifyElementVisibleKeyword.verifyElementVisible</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:16.045481800Z</date>
  <millis>1755655996045</millis>
  <nanos>481800</nanos>
  <sequence>324</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyElementVisible(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:16.045481800Z</date>
  <millis>1755655996045</millis>
  <nanos>481800</nanos>
  <sequence>325</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/EditCoordinates</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:16.045481800Z</date>
  <millis>1755655996045</millis>
  <nanos>481800</nanos>
  <sequence>326</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/EditCoordinates</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T02:13:16.050073800Z</date>
  <millis>1755655996050</millis>
  <nanos>73800</nanos>
  <sequence>327</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\SaveAndVerify.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-20T02:13:16.076646100Z</date>
  <millis>1755655996076</millis>
  <nanos>646100</nanos>
  <sequence>329</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_save&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">21</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T02:13:16.533294Z</date>
  <millis>1755655996533</millis>
  <nanos>294000</nanos>
  <sequence>339</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_save&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:16.535548900Z</date>
  <millis>1755655996535</millis>
  <nanos>548900</nanos>
  <sequence>340</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_save&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:16.535548900Z</date>
  <millis>1755655996535</millis>
  <nanos>548900</nanos>
  <sequence>341</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : waitForElementVisible(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;quot;), 5)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">23</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-20T02:13:16.996932600Z</date>
  <millis>1755655996996</millis>
  <nanos>932600</nanos>
  <sequence>351</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object &amp;apos;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;apos; is visible</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call</property>
  <property name="testops-execution-stacktrace">com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:324)
com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:319)
com.kms.katalon.core.logging.KeywordLogger$logPassed$0.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:84)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
SaveAndVerify.run(SaveAndVerify:23)
</property>
</record>
<record>
  <date>2025-08-20T02:13:16.996932600Z</date>
  <millis>1755655996996</millis>
  <nanos>932600</nanos>
  <sequence>352</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : waitForElementVisible(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;quot;), 5)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:16.998158100Z</date>
  <millis>1755655996998</millis>
  <nanos>158100</nanos>
  <sequence>353</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-20T02:13:17.328427900Z</date>
  <millis>1755655997328</millis>
  <nanos>427900</nanos>
  <sequence>363</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:17.329426700Z</date>
  <millis>1755655997329</millis>
  <nanos>426700</nanos>
  <sequence>364</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/save_modal_save_button&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:17.330425400Z</date>
  <millis>1755655997330</millis>
  <nanos>425400</nanos>
  <sequence>365</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-20T02:13:22.577832100Z</date>
  <millis>1755656002577</millis>
  <nanos>832100</nanos>
  <sequence>373</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;apos; is: &amp;apos;148485&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:22.599191100Z</date>
  <millis>1755656002599</millis>
  <nanos>191100</nanos>
  <sequence>375</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;148485&amp;apos; and expected object &amp;apos;148485&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:22.609471100Z</date>
  <millis>1755656002609</millis>
  <nanos>471100</nanos>
  <sequence>376</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:22.609471100Z</date>
  <millis>1755656002609</millis>
  <nanos>471100</nanos>
  <sequence>377</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-20T02:13:22.637991200Z</date>
  <millis>1755656002637</millis>
  <nanos>991200</nanos>
  <sequence>385</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;apos; is: &amp;apos;2406567&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:22.637991200Z</date>
  <millis>1755656002637</millis>
  <nanos>991200</nanos>
  <sequence>387</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;2406567&amp;apos; and expected object &amp;apos;2406567&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:22.637991200Z</date>
  <millis>1755656002637</millis>
  <nanos>991200</nanos>
  <sequence>388</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:22.637991200Z</date>
  <millis>1755656002637</millis>
  <nanos>991200</nanos>
  <sequence>389</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">31</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-20T02:13:22.668448200Z</date>
  <millis>1755656002668</millis>
  <nanos>448200</nanos>
  <sequence>397</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;apos; is: &amp;apos;148485&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:22.668448200Z</date>
  <millis>1755656002668</millis>
  <nanos>448200</nanos>
  <sequence>399</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;148485&amp;apos; and expected object &amp;apos;148485&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:22.668448200Z</date>
  <millis>1755656002668</millis>
  <nanos>448200</nanos>
  <sequence>400</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:22.668448200Z</date>
  <millis>1755656002668</millis>
  <nanos>448200</nanos>
  <sequence>401</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">32</property>
  <property name="stepIndex">7</property>
</record>
<record>
  <date>2025-08-20T02:13:22.702688300Z</date>
  <millis>1755656002702</millis>
  <nanos>688300</nanos>
  <sequence>409</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;apos; is: &amp;apos;2406567&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:22.704679Z</date>
  <millis>1755656002704</millis>
  <nanos>679000</nanos>
  <sequence>411</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;2406567&amp;apos; and expected object &amp;apos;2406567&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:22.704679Z</date>
  <millis>1755656002704</millis>
  <nanos>679000</nanos>
  <sequence>412</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:22.704679Z</date>
  <millis>1755656002704</millis>
  <nanos>679000</nanos>
  <sequence>413</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">33</property>
  <property name="stepIndex">8</property>
</record>
<record>
  <date>2025-08-20T02:13:23.138305700Z</date>
  <millis>1755656003138</millis>
  <nanos>305700</nanos>
  <sequence>423</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/pagination&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:23.138305700Z</date>
  <millis>1755656003138</millis>
  <nanos>305700</nanos>
  <sequence>424</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/pagination&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:23.138305700Z</date>
  <millis>1755656003138</millis>
  <nanos>305700</nanos>
  <sequence>425</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">35</property>
  <property name="stepIndex">9</property>
</record>
<record>
  <date>2025-08-20T02:13:23.187999300Z</date>
  <millis>1755656003187</millis>
  <nanos>999300</nanos>
  <sequence>433</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;apos; is: &amp;apos;148485&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:23.189018600Z</date>
  <millis>1755656003189</millis>
  <nanos>18600</nanos>
  <sequence>435</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;148485&amp;apos; and expected object &amp;apos;148485&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:23.190017800Z</date>
  <millis>1755656003190</millis>
  <nanos>17800</nanos>
  <sequence>436</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:23.190017800Z</date>
  <millis>1755656003190</millis>
  <nanos>17800</nanos>
  <sequence>437</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">36</property>
  <property name="stepIndex">10</property>
</record>
<record>
  <date>2025-08-20T02:13:23.218136300Z</date>
  <millis>1755656003218</millis>
  <nanos>136300</nanos>
  <sequence>445</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Attribute &amp;apos;value&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;apos; is: &amp;apos;2406567&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.GetAttributeKeyword.getAttribute</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:23.219137200Z</date>
  <millis>1755656003219</millis>
  <nanos>137200</nanos>
  <sequence>447</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Actual object &amp;apos;2406567&amp;apos; and expected object &amp;apos;2406567&amp;apos; are equal</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-is-assertion">true</property>
  <property name="testops-method-name">com.kms.katalon.core.keyword.builtin.VerifyEqualKeyword.verifyEqual</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:23.219137200Z</date>
  <millis>1755656003219</millis>
  <nanos>137200</nanos>
  <sequence>448</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : verifyEqual(getAttribute(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y&amp;quot;), &amp;quot;value&amp;quot;), &amp;quot;2406567&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:23.219137200Z</date>
  <millis>1755656003219</millis>
  <nanos>137200</nanos>
  <sequence>449</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:23.220136500Z</date>
  <millis>1755656003220</millis>
  <nanos>136500</nanos>
  <sequence>450</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: true,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-20T02:13:23.220136500Z</date>
  <millis>1755656003220</millis>
  <nanos>136500</nanos>
  <sequence>451</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start listener action : afterSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:23.221138Z</date>
  <millis>1755656003221</millis>
  <nanos>138000</nanos>
  <sequence>453</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : closeBrowser()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">35</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-20T02:13:23.374678400Z</date>
  <millis>1755656003374</millis>
  <nanos>678400</nanos>
  <sequence>455</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is closed</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.CloseBrowserKeyword.closeBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-20T02:13:23.374678400Z</date>
  <millis>1755656003374</millis>
  <nanos>678400</nanos>
  <sequence>457</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : closeBrowser()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:23.374678400Z</date>
  <millis>1755656003374</millis>
  <nanos>678400</nanos>
  <sequence>458</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End listener action : afterSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T02:13:23.399870600Z</date>
  <millis>1755656003399</millis>
  <nanos>870600</nanos>
  <sequence>459</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endSuite</method>
  <thread>1</thread>
  <message>End Test Suite : Test Suites/Mandarin/v1/Gis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
</log>
