import Act from "../actions";

const initState = {
  landId: "",
  landName: "", // 紀錄地段
  landSerialNumber: "", // 紀錄地號
  extraBasic: [], // 記錄箱號、紀錄編輯者
  basicInfo: [], // 紀錄某[地段+地號]的基本資訊
  landMarkInfo: [], // 紀錄某[地段+地號]的土地標示變更
  landRightInfo: [], // 紀錄某[地段+地號]的土地權利變更
  selctionIndex: 0, // 控制選擇[地號]上一筆與下一筆資料
  LSNList: [], // [地號]清單
  isLock: false, // 編輯狀態
  editLoading: false, // 解鎖、上鎖，顯示loading
  lockUser: "", // 編輯者
  infoLoading: false, // landSerialNumber更新，顯示loading
  showSaveBtnPopper: false, // 按下Save後，還有錯誤訊息顯示在popper
  addStrDialogParam: {
    open: false, // 顯示增加字串對話框
    type: "", // 顯示在Title分辨是要合併還是分割
    newData: {}, // 可以辨識在哪個landMark event的資訊
  },
};

const homePageReducer = (state = initState, action) => {
  switch (action.type) {
    case Act.SET_LANDID:
      return { ...state, landId: action.payload };
    case Act.SET_LANDNAME:
      return { ...state, landName: action.payload };
    case Act.SET_LANDSERIALNUMBER:
      return { ...state, landSerialNumber: action.payload };
    case Act.SET_EXTRABASIC:
      return { ...state, extraBasic: action.payload };
    case Act.SET_BASICINFO:
      return { ...state, basicInfo: action.payload };
    case Act.SET_LANDMARKINFO:
      return { ...state, landMarkInfo: action.payload };
    case Act.SET_LANDRIGHTINFO:
      return { ...state, landRightInfo: action.payload };
    case Act.SET_SELECTIONINDEX:
      return { ...state, selctionIndex: action.payload };
    case Act.SET_LSNLIST:
      return { ...state, LSNList: action.payload };
    case Act.SET_ISLOCK:
      return { ...state, isLock: action.payload };
    case Act.SET_EDITLOADING:
      return { ...state, editLoading: action.payload };
    case Act.SET_LOCKERUSER:
      return { ...state, lockUser: action.payload };
    case Act.SET_INFOLOADING:
      return { ...state, infoLoading: action.payload };
    case Act.SET_SHOWSAVEBTNPOPPER:
      return { ...state, showSaveBtnPopper: action.payload };
    case Act.SET_ADDSTRDIALOGPARAM:
      return { ...state, addStrDialogParam: action.payload };
    default:
      return state;
  }
};

export default homePageReducer;
