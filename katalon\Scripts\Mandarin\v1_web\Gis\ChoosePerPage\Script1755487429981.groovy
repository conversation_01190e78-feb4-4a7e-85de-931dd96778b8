import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject
import static com.kms.katalon.core.testobject.ObjectRepository.findWindowsObject
import com.kms.katalon.core.checkpoint.Checkpoint as Checkpoint
import com.kms.katalon.core.cucumber.keyword.CucumberBuiltinKeywords as CucumberKW
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling as FailureHandling
import com.kms.katalon.core.testcase.TestCase as TestCase
import com.kms.katalon.core.testdata.TestData as TestData
import com.kms.katalon.core.testng.keyword.TestNGBuiltinKeywords as TestNGKW
import com.kms.katalon.core.testobject.TestObject as TestObject
import com.kms.katalon.core.testobject.ConditionType as ConditionType
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI
import com.kms.katalon.core.windows.keyword.WindowsBuiltinKeywords as Windows
import internal.GlobalVariable as GlobalVariable
import org.openqa.selenium.Keys as Keys


// 選擇單頁50筆
// 點擊下拉按鈕 local用（role="combobox"），部屬後用(role="button")
TestObject dropdownButton = new TestObject()
dropdownButton.addProperty("css", ConditionType.EQUALS, 'div[role="button"]')
WebUI.click(dropdownButton)

// 等待選單出現並選擇「每頁顯示 50 筆」
TestObject option50 = new TestObject()
option50.addProperty("css", ConditionType.EQUALS, 'li.MuiMenuItem-root[data-value="50"]')
WebUI.waitForElementVisible(option50, 5)
WebUI.click(option50)


//import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
//import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
//import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
//import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject
//import static com.kms.katalon.core.testobject.ObjectRepository.findWindowsObject
//import com.kms.katalon.core.checkpoint.Checkpoint as Checkpoint
//import com.kms.katalon.core.cucumber.keyword.CucumberBuiltinKeywords as CucumberKW
//import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
//import com.kms.katalon.core.model.FailureHandling as FailureHandling
//import com.kms.katalon.core.testcase.TestCase as TestCase
//import com.kms.katalon.core.testdata.TestData as TestData
//import com.kms.katalon.core.testng.keyword.TestNGBuiltinKeywords as TestNGKW
//import com.kms.katalon.core.testobject.TestObject as TestObject
//import com.kms.katalon.core.testobject.ConditionType as ConditionType
//import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
//import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI
//import com.kms.katalon.core.windows.keyword.WindowsBuiltinKeywords as Windows
//import internal.GlobalVariable as GlobalVariable
//import org.openqa.selenium.Keys as Keys
//
//// 改良版：選擇單頁50筆
//try {
//	// 方法1：等待頁面完全載入
//	WebUI.waitForPageLoad(10)
//	
//	// 方法2：使用更穩定的選擇器組合
//	TestObject dropdownButton = new TestObject()
//	
//	// 嘗試多種選擇器策略
//	List<String> selectors = [
//		'div[role="combobox"]',
//		'[role="combobox"]',
//		'div[aria-haspopup="listbox"]',
//		'.MuiSelect-root',
//		'div.MuiInputBase-root[role="combobox"]'
//	]
//	
//	boolean dropdownFound = false
//	
//	for (String selector : selectors) {
//		try {
//			dropdownButton.addProperty("css", ConditionType.EQUALS, selector)
//			
//			// 增加等待時間並檢查元素是否可見且可點擊
//			if (WebUI.waitForElementVisible(dropdownButton, 10)) {
//				WebUI.waitForElementClickable(dropdownButton, 5)
//				
//				// 滾動到元素位置確保可見
//				WebUI.scrollToElement(dropdownButton, 3)
//				
//				// 使用 JavaScript 點擊以避免元素被遮擋
//				WebUI.executeJavaScript("arguments[0].click();", [WebUI.findWebElement(dropdownButton)])
//				
//				dropdownFound = true
//				break
//			}
//		} catch (Exception e) {
//			println("選擇器失敗: ${selector}, 錯誤: ${e.getMessage()}")
//			continue
//		}
//	}
//	
//	if (!dropdownFound) {
//		throw new Exception("無法找到下拉選單按鈕")
//	}
//	
//	// 等待下拉選單完全展開
//	WebUI.delay(2)
//	
//	// 方法3：等待並選擇50筆選項
//	TestObject option50 = new TestObject()
//	
//	// 嘗試多種50筆選項的選擇器
//	List<String> optionSelectors = [
//		'li.MuiMenuItem-root[data-value="50"]',
//		'li[data-value="50"]',
//		'li.MuiMenuItem-root:contains("50")',
//		'[role="option"][data-value="50"]',
//		'li:contains("每頁顯示 50 筆")',
//		'li:contains("50")'
//	]
//	
//	boolean optionFound = false
//	
//	for (String optionSelector : optionSelectors) {
//		try {
//			option50.addProperty("css", ConditionType.EQUALS, optionSelector)
//			
//			if (WebUI.waitForElementVisible(option50, 10)) {
//				WebUI.waitForElementClickable(option50, 5)
//				
//				// 滾動到選項位置
//				WebUI.scrollToElement(option50, 3)
//				
//				// 使用 JavaScript 點擊
//				WebUI.executeJavaScript("arguments[0].click();", [WebUI.findWebElement(option50)])
//				
//				optionFound = true
//				break
//			}
//		} catch (Exception e) {
//			println("選項選擇器失敗: ${optionSelector}, 錯誤: ${e.getMessage()}")
//			continue
//		}
//	}
//	
//	if (!optionFound) {
//		throw new Exception("無法找到50筆選項")
//	}
//	
//	// 等待選擇生效
//	WebUI.delay(3)
//	
//	// 驗證選擇是否成功
//	WebUI.waitForPageLoad(10)
//	
//	println("成功選擇每頁顯示50筆")
//	
//} catch (Exception e) {
//	println("選擇每頁50筆失敗: ${e.getMessage()}")
//	
//	// 備用方案：使用鍵盤操作
//	try {
//		println("嘗試備用方案...")
//		
//		TestObject dropdownBackup = new TestObject()
//		dropdownBackup.addProperty("css", ConditionType.EQUALS, '[role="combobox"]')
//		
//		if (WebUI.waitForElementVisible(dropdownBackup, 5)) {
//			WebUI.click(dropdownBackup)
//			WebUI.delay(1)
//			
//			// 使用鍵盤方向鍵選擇
//			WebUI.sendKeys(dropdownBackup, Keys.chord(Keys.ARROW_DOWN, Keys.ARROW_DOWN, Keys.ENTER))
//			WebUI.delay(2)
//			
//			println("備用方案執行完成")
//		}
//	} catch (Exception backupError) {
//		println("備用方案也失敗: ${backupError.getMessage()}")
//		throw new Exception("所有選擇方案都失敗")
//	}
//}