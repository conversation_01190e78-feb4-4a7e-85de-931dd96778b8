$btnFontSize: 1rem;

@mixin itemStyle($itemClsName, $bgColor, $borderStyle) {
  @if $itemClsName == ".Basic" {
    #{$itemClsName}Item {
      display: flex;
      flex-wrap: nowrap;
      background-color: $bgColor;
      .colName {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      #{$itemClsName}infoArea {
        padding: 5px;
        border: $borderStyle;
        .labelName {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .addOrRemoveCol {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        .iconPos {
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          .icon {
            font-size: 2rem;
          }
        }
      }
    }
  } @else {
    #{$itemClsName}Item {
      background-color: $bgColor;
      .colName {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      #{$itemClsName}infoArea {
        padding: 5px;
        margin: 5px;
        border-radius: 8px;
        border: $borderStyle;
        .labelName {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .addOrRemoveCol {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        .iconPos {
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          .icon {
            font-size: 2rem;
          }
        }
      }
    }
  }
}

*{
  box-sizing: border-box;
}

.EditPage {
  //height: 100%;
  padding: 1%;
  //border: 1px red solid;
  .buttonArea {
    .topArea {
      .MuiGrid-item {
        padding: 0;
      }
      .editBtn, .preBtn, .nextBtn, .AddLandButton {
        background-color: #008CBA;
        color: white;
        border-radius: 8px;
        font-size: $btnFontSize;
      }

      .saveBtn {
        background-color: #f44336;
        color: white;
        border-radius: 8px;
        font-size: $btnFontSize;
      }

      .saveBtnDisable, .editBtnDisable, .preBtnDisable, .nextBtnDisable {
        background-color: #D8D5D1;
        color: #A6A6A6;
        border-radius: 8px;
        font-size: $btnFontSize;
      }

    }
    .middleArea {
      padding: 6px 0;
    }
  }
  .infoArea {
    .BaiscInfo{
      $BasicBGColor: #F2F2DC;
      $borderStyle: "initial";
      @include itemStyle(".Basic", $BasicBGColor, $borderStyle);
    }

    .LandMarks {
      $LMKBGcolor: #DCF2E8;
      $borderStyle: 1px gray dashed;
      @include itemStyle(".LMK", $LMKBGcolor, $borderStyle);

      .header{
        text-align: center;
        background: #1abc9c;
        color: white;
        font-size: 30px;
        .headerItem{
          padding: 6px 8px;
          .addItemBtn {
            background-color: #e7e7e7;
            color: black;
            border-radius: 8px;
            font-size: $btnFontSize;
          }
        }
      }
    }

    .LandRights {
      margin-bottom: 5vh;
      $LRTBGcolor: #DCEEF2;
      $borderStyle: 1px gray dashed;
      @include itemStyle(".LRT", $LRTBGcolor, $borderStyle);

      .header{
        text-align: center;
        background: #1abc9c;
        color: white;
        font-size: 30px;
        .headerItem{
          padding: 6px 8px;
          .addItemBtn {
            background-color: #e7e7e7;
            color: black;
            border-radius: 8px;
            font-size: $btnFontSize;
          }
        }
      }
    }
  }
}