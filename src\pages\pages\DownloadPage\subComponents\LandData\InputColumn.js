import React, { useContext, useState, useEffect } from "react";

// material ui
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";
import { Api, readOntoData } from "../../../../../api/land/Api";
import Act from "../../../../../store/actions";

function InputColumn() {
  const [state, dispatch] = useContext(StoreContext);
  const { dataFormType, dwLandName, startYear, landType } = state.download;
  const [LNList, setLNList] = useState([]);
  const [LSNList, setLSNList] = useState([]);
  const [tmpFormType, setTmpFormType] = useState(""); // type3不顯示結束年份輸入欄位
  const [yearList, setYearList] = useState([]);

  useEffect(() => {
    const findObj = dataFormType.find((el) => el.check);
    if (findObj) {
      setTmpFormType(findObj.type);
    } else {
      setTmpFormType("");
    }
  }, [dataFormType]);

  useEffect(() => {
    const apiStr = Api.getLNList();
    let mounted = true;
    readOntoData(apiStr).then((result) => {
      if (mounted) {
        setLNList(result.data);
      }
    });

    return () => {
      mounted = false;
    };
  }, []);

  useEffect(() => {
    if (dwLandName === "" || dwLandName === null) return;
    const apiStr = Api.getLSList().replace("{landName}", dwLandName);
    readOntoData(apiStr).then((result) => {
      if (result.data && Array.isArray(result.data)) {
        setLSNList(result.data);
      } else {
        setLSNList([]);
      }
    }).catch(() => {
      setLSNList([]);
    });
  }, [dwLandName]);

  useEffect(() => {
    const selectType = landType.find((el) => el.select);
    if (!dwLandName || !selectType) return;
    const tmpType = selectType.property;
    const apiStr = Api.getLandYearList()
      .replace("{landName}", dwLandName)
      .replace("{type}", tmpType);

    readOntoData(apiStr).then((res) => {
      try {
        const tmpArr = [];
        if (res.data && res.data.length > 0) {
          const numPattern = /^[0-9]+$/;
          const yearRes = res.data
            .filter(({ year }) => year && numPattern.test(year))
            .map(({ year }) => parseInt(year, 10))
            .sort((a, b) => a - b);
          
          if (yearRes.length > 0) {
            const minYear = yearRes[0];
            const maxYear = yearRes[yearRes.length - 1];

            for (let i = minYear; i <= maxYear; i += 1) {
              tmpArr.push(i.toString());
            }
          }
        }
        setYearList(tmpArr);
      } catch (error) {
        setYearList([]);
      }
    }).catch(() => {
      setYearList([]);
    });
  }, [landType, dwLandName]);

  const setLandName = (event, value) => {
    dispatch({
      type: Act.SET_DWLANDNAME,
      payload: value,
    });
  };

  const setLandSerialNumber = (event, value) => {
    dispatch({
      type: Act.SET_DWLANDSERIALNUMBER,
      payload: value,
    });
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Typography 
        variant="h6" 
        sx={{ 
          mb: { xs: 2, md: 3 }, 
          color: "text.primary",
          fontWeight: 600
        }}
      >
        搜尋地區與時間
      </Typography>
      
      <Grid container spacing={{ xs: 1.5, md: 2 }}>
        {/* 空間範圍選擇 */}
        <Grid item xs={12}>
          <Typography 
            variant="subtitle1" 
            sx={{ 
              mb: { xs: 1, md: 1.5 }, 
              color: "text.secondary",
              fontWeight: 600
            }}
          >
            空間範圍
          </Typography>
          <Grid container spacing={{ xs: 1, md: 1.5 }}>
            <Grid item xs={12} md={tmpFormType === "type2" ? 8 : 12}>
              <Autocomplete
                options={LNList.map((el) => el.landName)}
                value={dwLandName || null}
                renderInput={(params) => (
                  <TextField 
                    {...params} 
                    label="土名（舊地段名）"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        backgroundColor: 'background.paper',
                      },
                    }}
                  />
                )}
                onChange={setLandName}
              />
            </Grid>
            {tmpFormType === "type2" && (
              <Grid item xs={12} md={4}>
                <Autocomplete
                  options={LSNList.map((element) => element.landSerialNumber)}
                  value={state.download.dwLandSerialNumber || null}
                  renderInput={(params) => (
                    <TextField 
                      {...params} 
                      label="地番（地號）"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          backgroundColor: 'background.paper',
                        },
                      }}
                    />
                  )}
                  onChange={setLandSerialNumber}
                />
              </Grid>
            )}
          </Grid>
        </Grid>
        
        {/* 時間範圍選擇 */}
        <Grid item xs={12}>
          <Typography 
            variant="subtitle1" 
            sx={{ 
              mb: { xs: 1, md: 1.5 }, 
              color: "text.secondary",
              fontWeight: 600
            }}
          >
            時間範圍
          </Typography>
          <Grid container spacing={{ xs: 1, md: 1.5 }} alignItems="center">
            <Grid item xs={12} md={tmpFormType !== "type3" ? 5 : 8}>
              <Autocomplete
                options={yearList}
                value={startYear || null}
                renderInput={(params) => (
                  <TextField 
                    {...params} 
                    label="開始年分"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        backgroundColor: 'background.paper',
                      },
                    }}
                  />
                )}
                onChange={(evt, data) => {
                  dispatch({
                    type: Act.SET_STARTYEAR,
                    payload: data,
                  });
                }}
                disabled={!dwLandName || yearList.length === 0}
              />
            </Grid>
            
            {tmpFormType !== "type3" && (
              <>
                <Grid item xs={12} md={2} sx={{ textAlign: "center" }}>
                  <Typography 
                    variant="body1" 
                    sx={{ 
                      color: "text.secondary",
                      fontWeight: 500,
                      display: { xs: "none", md: "block" }
                    }}
                  >
                    到
                  </Typography>
                </Grid>
                
                <Grid item xs={12} md={5}>
                  <Autocomplete
                    options={yearList}
                    value={state.download.endYear || null}
                    renderInput={(params) => (
                      <TextField 
                        {...params} 
                        label="結束年分"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 2,
                            backgroundColor: 'background.paper',
                          },
                        }}
                      />
                    )}
                    onChange={(evt, data) => {
                      if (data && startYear && parseInt(data) <= parseInt(startYear)) return;
                      dispatch({
                        type: Act.SET_ENDYEAR,
                        payload: data,
                      });
                    }}
                    disabled={!dwLandName || !startYear || yearList.length === 0}
                  />
                </Grid>
              </>
            )}
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
}

export default InputColumn;
