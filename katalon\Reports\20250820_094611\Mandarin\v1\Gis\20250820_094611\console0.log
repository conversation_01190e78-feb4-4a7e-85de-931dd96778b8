Warning: NLS unused message: QTEST in: com.kms.katalon.constants.globalMessages
Warning: NLS missing message: QTEST_NAME in: com.kms.katalon.constants.globalMessages
2025-08-20 09:46:14.378 INFO  c.k.katalon.core.main.TestSuiteExecutor  - START Test Suites/Mandarin/v1/Gis
2025-08-20 09:46:14.478 INFO  c.k.katalon.core.main.TestSuiteExecutor  - userFullName = Lin Kai Yee
2025-08-20 09:46:14.481 INFO  c.k.katalon.core.main.TestSuiteExecutor  - projectName = katalon
2025-08-20 09:46:14.482 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostName = saw63 - host.docker.internal
2025-08-20 09:46:14.483 INFO  c.k.katalon.core.main.TestSuiteExecutor  - os = Windows 11 64bit
2025-08-20 09:46:14.484 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostAddress = ************
2025-08-20 09:46:14.485 INFO  c.k.katalon.core.main.TestSuiteExecutor  - katalonVersion = ********
2025-08-20 09:46:14.826 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-20 09:46:14.922 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 20, 2025 9:46:16 上午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-20 09:46:16.744 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 4da81be517a6c043ed67c080ea02a56a
2025-08-20 09:46:16.746 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-20 09:46:16.747 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-20 09:46:16.747 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-20 09:46:16.758 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-20 09:46:17.343 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 09:46:17.343 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-20 09:46:17.401 DEBUG testcase.GoToGis                         - 1: openBrowser("")
2025-08-20 09:46:17.406 WARN  c.k.k.core.webui.driver.DriverFactory    - A browser is already opened. Closing browser and opening a new one
2025-08-20 09:46:17.587 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-20 09:46:17.591 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 20, 2025 9:46:18 上午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-20 09:46:18.477 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 3e561d059535bb105d19f83287d1f35b
2025-08-20 09:46:18.478 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-20 09:46:18.479 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-20 09:46:18.479 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-20 09:46:18.480 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-20 09:46:18.505 DEBUG testcase.GoToGis                         - 2: navigateToUrl("https://land2.daoyidh.com/Gis")
2025-08-20 09:46:18.966 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-20 09:46:18.974 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 09:46:18.975 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-20 09:46:19.039 DEBUG testcase.LoginWithEmail                  - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_"))
2025-08-20 09:46:20.175 DEBUG testcase.LoginWithEmail                  - 2: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail"))
2025-08-20 09:46:20.685 DEBUG testcase.LoginWithEmail                  - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Email_email"), "<EMAIL>")
2025-08-20 09:46:21.501 DEBUG testcase.LoginWithEmail                  - 4: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Next"))
2025-08-20 09:46:21.793 DEBUG testcase.LoginWithEmail                  - 5: setEncryptedText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Password_password"), "0D41p6Ct1VJ35FCiyaw3Gw==")
2025-08-20 09:46:22.710 DEBUG testcase.LoginWithEmail                  - 6: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign In"))
2025-08-20 09:46:23.002 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-20 09:46:23.005 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 09:46:23.005 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-20 09:46:23.071 DEBUG testcase.SearchTextInGis                 - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button__1"))
2025-08-20 09:46:25.605 DEBUG testcase.SearchTextInGis                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input__mui-3"), "新北勢")
2025-08-20 09:46:26.143 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-20 09:46:26.148 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 09:46:26.148 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-20 09:46:26.205 DEBUG testcase.ChoosePerPage                   - 1: dropdownButton = new com.kms.katalon.core.testobject.TestObject()
2025-08-20 09:46:26.210 DEBUG testcase.ChoosePerPage                   - 2: dropdownButton.addProperty("css", EQUALS, "div[role="button"]")
2025-08-20 09:46:26.217 DEBUG testcase.ChoosePerPage                   - 3: click(dropdownButton)
2025-08-20 09:46:26.626 DEBUG testcase.ChoosePerPage                   - 4: option50 = new com.kms.katalon.core.testobject.TestObject()
2025-08-20 09:46:26.632 DEBUG testcase.ChoosePerPage                   - 5: option50.addProperty("css", EQUALS, "li.MuiMenuItem-root[data-value="50"]")
2025-08-20 09:46:26.633 DEBUG testcase.ChoosePerPage                   - 6: waitForElementVisible(option50, 5)
2025-08-20 09:46:27.085 DEBUG testcase.ChoosePerPage                   - 7: click(option50)
2025-08-20 09:46:27.501 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-20 09:46:27.505 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 09:46:27.505 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-20 09:46:27.574 DEBUG testcase.EditCoordinates                 - 1: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "148485")
2025-08-20 09:46:28.075 DEBUG testcase.EditCoordinates                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "2406567 ")
2025-08-20 09:46:28.622 DEBUG testcase.EditCoordinates                 - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "148485")
2025-08-20 09:46:29.023 DEBUG testcase.EditCoordinates                 - 4: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "2406567 ")
2025-08-20 09:46:29.470 DEBUG testcase.EditCoordinates                 - 5: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination"))
2025-08-20 09:46:30.140 DEBUG testcase.EditCoordinates                 - 6: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "148485")
2025-08-20 09:46:30.620 DEBUG testcase.EditCoordinates                 - 7: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "2406567 ")
2025-08-20 09:46:31.056 DEBUG testcase.EditCoordinates                 - 8: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination_1"))
2025-08-20 09:46:31.681 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-20 09:46:31.683 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 09:46:31.683 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SaveAndVerify
2025-08-20 09:46:31.739 DEBUG testcase.SaveAndVerify                   - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_save"))
2025-08-20 09:46:32.204 DEBUG testcase.SaveAndVerify                   - 2: waitForElementVisible(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button"), 5)
2025-08-20 09:46:32.657 DEBUG testcase.SaveAndVerify                   - 3: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button"))
2025-08-20 09:46:32.980 DEBUG testcase.SaveAndVerify                   - 4: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "value"), "148485")
2025-08-20 09:46:36.804 DEBUG testcase.SaveAndVerify                   - 5: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "value"), "2406567")
2025-08-20 09:46:36.834 DEBUG testcase.SaveAndVerify                   - 6: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "value"), "148485")
2025-08-20 09:46:36.861 DEBUG testcase.SaveAndVerify                   - 7: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "value"), "2406567")
2025-08-20 09:46:36.894 DEBUG testcase.SaveAndVerify                   - 8: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination"))
2025-08-20 09:46:37.327 DEBUG testcase.SaveAndVerify                   - 9: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "value"), "148485")
2025-08-20 09:46:37.393 DEBUG testcase.SaveAndVerify                   - 10: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "value"), "2406567")
2025-08-20 09:46:37.422 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SaveAndVerify
2025-08-20 09:46:37.620 INFO  c.k.katalon.core.main.TestSuiteExecutor  - --------------------
2025-08-20 09:46:37.620 INFO  c.k.katalon.core.main.TestSuiteExecutor  - END Test Suites/Mandarin/v1/Gis
2025-08-20 09:46:37.620 INFO  c.k.katalon.core.main.TestSuiteExecutor  - ====================
