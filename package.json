{"name": "land", "version": "0.4.0", "private": true, "dependencies": {"@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@mui/icons-material": "^5.3.1", "@mui/material": "^5.4.0", "assert": "^2.1.0", "axios": "^0.25.0", "base64url": "^3.0.1", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "d3": "^7.8.5", "dom-to-image": "^2.6.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "firebase": "^9.6.6", "firebaseui": "^6.0.0", "html-react-parser": "^3.0.7", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lodash": "^4.17.21", "markdown-it": "^12.3.2", "path-browserify": "^1.0.1", "process": "^0.11.10", "proj4": "^2.19.10", "prop-types": "^15.8.1", "query-string": "^7.1.1", "react": "^17.0.2", "react-beautiful-dnd": "^13.1.0", "react-data-export": "^0.6.0", "react-datepicker": "^4.7.0", "react-dom": "^17.0.2", "react-dropzone": "^12.0.5", "react-firebaseui": "^6.0.0", "react-intl": "^5.24.6", "react-number-format": "^4.9.1", "react-router": "5.2.1", "react-router-dom": "^5.3.0", "react-scripts": "5.0.1", "react-share": "^5.0.3", "stream-browserify": "^3.0.0", "url": "^0.11.0", "util": "^0.12.5", "vm-browserify": "^1.1.2", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/eslint-parser": "^7.23.10", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@craco/craco": "^7.1.0", "@typescript-eslint/parser": "^5.62.0", "babel-plugin-named-exports-order": "0.0.2", "babel-plugin-transform-imports": "^2.0.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.10.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss-normalize": "^10.0.0", "prettier": "^2.8.8", "sass": "^1.69.7", "typescript": "^4.9.5"}, "scripts": {"build": "craco build", "eject": "react-scripts eject", "start": "craco start", "test": "craco test"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": "^18.20.0", "npm": ">=10.0.0"}, "overrides": {"tempa-xlsx": {"commander": "7.2.0", "codepage": "1.15.0"}}}