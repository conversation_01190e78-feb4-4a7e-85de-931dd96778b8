import { specRow } from "../../../config";
import evtType from "../../../../../../utils/evtType";

/**
 * 檢查說明: 後續相同是LandMark or LandRights的欄位，任一欄位如果有填值，此欄位必填
 * */
const checkLMLRNumber = (cell, worksheet) => {
  const colLabel = worksheet
    .getRow(specRow.labelIdx)
    .getCell(cell.fullAddress.col).value;
  const headerRow = worksheet.getRow(specRow.idIdx).values; // first row
  const row = worksheet.getRow(cell.fullAddress.row); // current row

  const idSplitSymbol = "-->";
  const landEvtType = worksheet
    .getRow(specRow.idIdx)
    .getCell(cell.fullAddress.col)
    .value.split(idSplitSymbol)[2];

  const spceCol =
    landEvtType === evtType.LandMark
      ? [
          "Land-->hasEvent-->LandMark-->cause",
          "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent",
          "Land-->hasEvent-->LandMark-->landGrades",
          "Land-->hasEvent-->LandMark-->landCategory",
          "Land-->hasEvent-->LandMark-->landArea",
          "Land-->hasEvent-->LandMark-->landRent",
        ]
      : [
          "Land-->hasEvent-->LandRights-->landRightsNumber",
          "Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent",
          "Land-->hasEvent-->LandRights-->cause",
          "Land-->hasEvent-->LandRights-->hasOwner-->Owner",
        ];

  // 要特別檢查的欄位，後續相同是LandMark or LandRights的欄位，任一欄位如果有填值，此欄位必填
  const checkColVal = spceCol
    .map((val) => {
      const idx = headerRow.findIndex((el) => el === val);
      return idx !== -1 ? row.values[idx] : "";
    })
    .some((val) => val);

  let tmpStr = "";
  if (checkColVal && !cell.value) {
    const reason = `後續相同是${landEvtType}的欄位，任一欄位如果有填值，此欄位必填`;
    tmpStr += `${cell.address}, [${cell.value}], 欄位:${colLabel}，${reason}。\n`;
  }

  return tmpStr;
};

export default checkLMLRNumber;
