# Excel 匯入檔案格式說明

## 概述

本文件詳細說明土地資料匯入系統所需的 Excel 檔案格式規範。系統會驗證上傳的 Excel 檔案是否符合此格式，只有符合規範的檔案才能成功匯入。

---

## 1. Worksheet 命名

### Worksheet 名稱
- **名稱**: `Sheet1`
- **說明**: 系統只會讀取第一個 worksheet，worksheet 名稱必須為 `Sheet1`
- **注意事項**: 
  - 如果 Excel 檔案包含多個 worksheet，系統只會處理第一個
  - Worksheet 名稱區分大小寫

---

## 2. 第一列（Row 1）- 欄位標題列

第一列為**欄位標題列**，定義了每一欄的欄位名稱。系統會驗證欄位名稱是否正確。

### 欄位列表（依序排列）

| 欄位編號 | 欄位名稱 | 說明 |
|---------|---------|------|
| A | `Land-->collectionPlace` | 典藏地 |
| B | `Land-->operator` | 經辦者 |
| C | `Land-->boxNumber` | 箱號 |
| D | `Land-->number` | 號數 |
| E | `Land-->landName` | 土地名稱 |
| F | `Land-->landSerialNumber` | 地號 |
| G | `Land-->abstract` | 摘要 |
| H | `Land-->source` | 來源 |
| I | `Land-->pawnRight` | 典權 |
| J | `Land-->plowingRight` | 耕作權 |
| K | `Land-->hasEvent-->LandMark-->landMarkNumber` | 地籍編號 |
| L | `Land-->hasEvent-->LandMark-->cause` | 地籍原因 |
| M | `Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent` | 地籍開始日期 |
| N | `Land-->hasEvent-->LandMark-->landGrades` | 地等 |
| O | `Land-->hasEvent-->LandMark-->landCategory` | 地目 |
| P | `Land-->hasEvent-->LandMark-->landArea` | 土地面積 |
| Q | `Land-->hasEvent-->LandMark-->landRent` | 租金 |
| R | `Land-->hasEvent-->LandRights-->landRightsNumber` | 地權編號 |
| S | `Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent` | 地權開始日期 |
| T | `Land-->hasEvent-->LandRights-->cause` | 地權原因 |
| U | `Land-->hasEvent-->LandRights-->hasOwner-->Owner` | 業主 |

### 重要說明
- **欄位順序**: 欄位順序必須與上表一致
- **欄位名稱**: 欄位名稱必須完全匹配（包含大小寫、箭頭符號 `-->`）
- **不可修改**: 不可新增、刪除或修改欄位名稱

---

## 3. 第二列（Row 2）- 範例資料列 1

第二列為**第一筆範例資料**，展示每個欄位的正確填寫格式。

| 欄位 | 範例值 | 說明 |
|-----|--------|------|
| Land-->collectionPlace | `典藏地1` | 文字格式 |
| Land-->operator | `經辦者1` | 文字格式 |
| Land-->boxNumber | `箱號1` | 文字格式 |
| Land-->number | `號數1` | 文字格式 |
| Land-->landName | `土地名稱1` | 文字格式 |
| Land-->landSerialNumber | `地號1` | 文字格式 |
| Land-->abstract | `摘要1` | 文字格式 |
| Land-->source | `來源1` | 文字格式 |
| Land-->pawnRight | `典權1` | 文字格式 |
| Land-->plowingRight | `耕作權1` | 文字格式 |
| Land-->hasEvent-->LandMark-->landMarkNumber | `地籍編號1` | 文字格式 |
| Land-->hasEvent-->LandMark-->cause | `原因1` | 文字格式 |
| Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent | `1920` | 年份格式（4位數字） |
| Land-->hasEvent-->LandMark-->landGrades | `地等1` | 文字格式 |
| Land-->hasEvent-->LandMark-->landCategory | `地目1` | 文字格式 |
| Land-->hasEvent-->LandMark-->landArea | `100.5` | 數值格式（可含小數） |
| Land-->hasEvent-->LandMark-->landRent | `50.0` | 數值格式（可含小數） |
| Land-->hasEvent-->LandRights-->landRightsNumber | `地權編號1` | 文字格式 |
| Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent | `1925` | 年份格式（4位數字） |
| Land-->hasEvent-->LandRights-->cause | `地權原因1` | 文字格式 |
| Land-->hasEvent-->LandRights-->hasOwner-->Owner | `業主1A,業主1B` | 多值文字（逗號分隔） |

---

## 4. 第三列（Row 3）- 範例資料列 2

第三列為**第二筆範例資料**，提供另一組填寫範例。

| 欄位 | 範例值 | 說明 |
|-----|--------|------|
| Land-->collectionPlace | `典藏地2` | 文字格式 |
| Land-->operator | `經辦者2` | 文字格式 |
| Land-->boxNumber | `箱號2` | 文字格式 |
| Land-->number | `號數2` | 文字格式 |
| Land-->landName | `土地名稱2` | 文字格式 |
| Land-->landSerialNumber | `地號2` | 文字格式 |
| Land-->abstract | `摘要2` | 文字格式 |
| Land-->source | `來源2` | 文字格式 |
| Land-->pawnRight | `典權2` | 文字格式 |
| Land-->plowingRight | `耕作權2` | 文字格式 |
| Land-->hasEvent-->LandMark-->landMarkNumber | `地籍編號2` | 文字格式 |
| Land-->hasEvent-->LandMark-->cause | `原因2` | 文字格式 |
| Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent | `1920` | 年份格式（4位數字） |
| Land-->hasEvent-->LandMark-->landGrades | `地等2` | 文字格式 |
| Land-->hasEvent-->LandMark-->landCategory | `地目2` | 文字格式 |
| Land-->hasEvent-->LandMark-->landArea | `100.5` | 數值格式（可含小數） |
| Land-->hasEvent-->LandMark-->landRent | `50.0` | 數值格式（可含小數） |
| Land-->hasEvent-->LandRights-->landRightsNumber | `地權編號2` | 文字格式 |
| Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent | `1925` | 年份格式（4位數字） |
| Land-->hasEvent-->LandRights-->cause | `地權原因2` | 文字格式 |
| Land-->hasEvent-->LandRights-->hasOwner-->Owner | `業主2A,業主2B` | 多值文字（逗號分隔） |

---

## 5. 資料開始列（Row 4 起）- 實際資料列

從**第四列（Row 4）**開始為實際要匯入的資料。每一列代表一筆土地資料。

### 5.1 欄位分類

#### A. 土地基本資訊欄位（Land）

| 欄位名稱 | 必填/選填 | 資料類型 | 格式說明 | 範例 |
|---------|----------|---------|---------|------|
| Land-->collectionPlace | 選填 | 文字 | 典藏地點名稱 | `國家圖書館` |
| Land-->operator | 選填 | 文字 | 經辦者姓名 | `張三` |
| Land-->boxNumber | 選填 | 文字 | 存放箱號 | `A001` |
| Land-->number | 選填 | 文字 | 編號 | `001` |
| Land-->landName | **必填** ⚠️ | 文字 | 土地名稱（與地號同時填寫時會新增土地基本資訊） | `高雄市某區土地` |
| Land-->landSerialNumber | **必填** ⚠️ | 文字 | 地號（與土地名稱同時填寫時會新增土地基本資訊） | `123-456` |
| Land-->abstract | 選填 | 文字 | 摘要說明 | `這是一筆歷史土地資料` |
| Land-->source | 選填 | 文字 | 資料來源 | `日治時期土地登記簿` |
| Land-->pawnRight | 選填 | 文字 | 典權相關資訊 | `有典權` |
| Land-->plowingRight | 選填 | 文字 | 耕作權相關資訊 | `有耕作權` |

#### B. 地籍資訊欄位（LandMark）

| 欄位名稱 | 必填/選填 | 資料類型 | 格式說明 | 範例 |
|---------|----------|---------|---------|------|
| Land-->hasEvent-->LandMark-->landMarkNumber | 條件必填 ⚠️ | 文字 | 地籍編號（有此欄位時會創建 LandMark 事件） | `LM-001` |
| Land-->hasEvent-->LandMark-->cause | 選填 | 文字 | 地籍變更原因 | `重新測量` |
| Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent | 選填 | 年份 | 4位數年份 | `1920` |
| Land-->hasEvent-->LandMark-->landGrades | 選填 | 文字 | 土地等級 | `一等` |
| Land-->hasEvent-->LandMark-->landCategory | 選填 | 文字 | 土地類別/地目 | `田` |
| Land-->hasEvent-->LandMark-->landArea | 選填 | 數值 | 土地面積（可含小數點） | `100.5` |
| Land-->hasEvent-->LandMark-->landRent | 選填 | 數值 | 租金金額（可含小數點） | `50.0` |

#### C. 地權資訊欄位（LandRights）

| 欄位名稱 | 必填/選填 | 資料類型 | 格式說明 | 範例 |
|---------|----------|---------|---------|------|
| Land-->hasEvent-->LandRights-->landRightsNumber | 條件必填 ⚠️ | 文字 | 地權編號（有此欄位時會創建 LandRights 事件） | `LR-001` |
| Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent | 選填 | 年份 | 4位數年份 | `1925` |
| Land-->hasEvent-->LandRights-->cause | 選填 | 文字 | 地權變更原因 | `繼承` |
| Land-->hasEvent-->LandRights-->hasOwner-->Owner | 選填 | 多值文字 | 業主姓名，多個業主用半形逗號分隔 | `張三,李四,王五` |

### 5.2 必填欄位說明

#### 🔴 絕對必填
- **`Land-->landName`** 和 **`Land-->landSerialNumber`** 必須**同時存在**才會新增土地基本資訊
  - 如果只填其中一個，系統不會創建新的土地資料
  - 這兩個欄位是識別一筆土地資料的關鍵

#### 🟡 條件必填
- **`Land-->hasEvent-->LandMark-->landMarkNumber`**
  - 如果要新增地籍資訊，此欄位必須填寫
  - 有此欄位時，系統會自動生成 LandMark ID
  
- **`Land-->hasEvent-->LandRights-->landRightsNumber`**
  - 如果要新增地權資訊，此欄位必須填寫
  - 有此欄位時，系統會自動生成 LandRights ID

#### 🟢 選填
- 其他所有欄位均為選填
- 選填欄位可以留空，但不建議填入無意義的空白字元

---

## 6. 特殊格式說明

### 6.1 多值欄位
某些欄位支援填入多個值，多個值之間使用**半形逗號（,）**分隔。

#### 支援多值的欄位
- `Land-->hasEvent-->LandRights-->hasOwner-->Owner`（業主）

#### 範例
```
張三,李四,王五
```

**注意事項**：
- 必須使用**半形逗號**，不可使用全形逗號
- 逗號前後不要加空格
- 業主名稱如果不存在，系統會自動創建新的業主資料

### 6.2 日期格式
日期欄位使用**4位數年份**格式。

#### 日期欄位
- `Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent`
- `Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent`

#### 格式說明
- 只需填寫年份（4位數字）
- 系統會自動補上 `-00-00` 後綴
- 例如：填寫 `1920`，系統儲存為 `1920-00-00`

#### 範例
```
1920
1925
1945
```

### 6.3 數值格式
數值欄位可以包含小數點。

#### 數值欄位
- `Land-->hasEvent-->LandMark-->landArea`（土地面積）
- `Land-->hasEvent-->LandMark-->landRent`（租金）

#### 格式說明
- 可以是整數或小數
- 使用半形數字
- 小數點使用半形句點（.）

#### 範例
```
100
100.5
1234.56
```

---

## 7. 資料處理邏輯

### 7.1 資料起始位置
系統從**第4列（Row 4）**開始處理資料（`START_IDX = 2`，因為陣列索引從0開始）。

### 7.2 土地資料分組邏輯
系統會根據 `Land-->landName` 和 `Land-->landSerialNumber` 來判斷是否為新的土地資料：

1. 當同時出現 `landName` 和 `landSerialNumber` 時，系統會：
   - 儲存前一筆土地的資料（如果存在）
   - 開始處理新的土地資料
   - 為新土地自動生成唯一 ID

2. 如果只有 LandMark 或 LandRights 資訊而沒有 landName + landSerialNumber：
   - 這些事件會附加到前一筆土地資料上

### 7.3 事件資料關聯
- **LandMark 事件**：當 `landMarkNumber` 有值時創建
- **LandRights 事件**：當 `landRightsNumber` 有值時創建
- 一筆土地可以有多個 LandMark 和 LandRights 事件

### 7.4 業主資料處理
系統會在匯入開始時預先處理所有業主：
1. 檢查業主名稱是否已存在於系統中
2. 如果不存在，自動創建新的業主資料
3. 為新業主生成唯一 ID
4. 將業主 ID 關聯到對應的 LandRights

---

## 8. 常見錯誤

### 8.1 欄位名稱錯誤
❌ **錯誤**：欄位名稱拼寫錯誤或使用中文名稱
```
土地名稱  （錯誤：應使用 Land-->landName）
```

✅ **正確**：使用完整的欄位路徑
```
Land-->landName
```

### 8.2 多值分隔符錯誤
❌ **錯誤**：使用全形逗號或其他分隔符
```
張三，李四  （使用了全形逗號）
張三;李四   （使用了分號）
張三, 李四  （逗號後有空格）
```

✅ **正確**：使用半形逗號，不加空格
```
張三,李四,王五
```

### 8.3 日期格式錯誤
❌ **錯誤**：使用完整日期或其他格式
```
1920-01-01
1920/01/01
民國9年
```

✅ **正確**：只填寫4位數年份
```
1920
```

### 8.4 數值格式錯誤
❌ **錯誤**：包含單位或使用全形數字
```
100.5公頃
１００．５
```

✅ **正確**：純數字，使用半形
```
100.5
```

### 8.5 必填欄位缺失
❌ **錯誤**：只填寫 landName 或只填寫 landSerialNumber
```
landName: 高雄市某區土地
landSerialNumber: （空白）
```

✅ **正確**：兩個欄位都要填寫
```
landName: 高雄市某區土地
landSerialNumber: 123-456
```

---

## 9. 範例資料

### 範例 1：完整的土地資料

| 欄位 | 值 |
|-----|---|
| Land-->landName | `高雄市三民區土地` |
| Land-->landSerialNumber | `123-456` |
| Land-->collectionPlace | `國家圖書館` |
| Land-->operator | `張三` |
| Land-->hasEvent-->LandMark-->landMarkNumber | `LM-2024-001` |
| Land-->hasEvent-->LandMark-->landArea | `150.5` |
| Land-->hasEvent-->LandMark-->landRent | `75.0` |
| Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent | `1920` |
| Land-->hasEvent-->LandRights-->landRightsNumber | `LR-2024-001` |
| Land-->hasEvent-->LandRights-->hasOwner-->Owner | `張三,李四` |
| Land-->hasEvent-->LandRights-->cause | `買賣` |

### 範例 2：只有土地基本資訊

| 欄位 | 值 |
|-----|---|
| Land-->landName | `台北市大安區土地` |
| Land-->landSerialNumber | `789-012` |
| Land-->collectionPlace | `中央研究院` |
| Land-->abstract | `日治時期土地` |
| 其他欄位 | （全部留空） |

### 範例 3：一筆土地多個事件

**第一列（土地基本資訊 + 第一個 LandMark）**：
| Land-->landName | Land-->landSerialNumber | Land-->hasEvent-->LandMark-->landMarkNumber | ... |
|----------------|------------------------|-------------------------------------------|-----|
| 台南市安平區土地 | 345-678 | LM-001 | ... |

**第二列（同一土地的第二個 LandMark）**：
| Land-->landName | Land-->landSerialNumber | Land-->hasEvent-->LandMark-->landMarkNumber | ... |
|----------------|------------------------|-------------------------------------------|-----|
| （留空） | （留空） | LM-002 | ... |

**說明**：第二列因為沒有 landName + landSerialNumber，所以 LM-002 會附加到前一筆土地上。

---

## 10. 檔案驗證

### 系統會驗證以下項目：

1. ✅ **Worksheet 存在性**：檔案必須包含至少一個 worksheet
2. ✅ **資料非空**：檔案不能是空白的
3. ✅ **欄位完整性**：所有必要欄位都必須存在
4. ✅ **欄位順序**：欄位順序必須正確
5. ✅ **Pattern 有效性**：指定的資料集名稱（如 `south`）必須在系統中定義

### 驗證失敗時的錯誤訊息：

- `FILE_ROWS_BLANK`：檔案沒有資料
- `PATTERN_NOT_EXIST`：指定的資料集名稱不存在
- `FIREBASE_READ_ERROR`：無法讀取 Excel 表頭定義
- `FIREBASE_UPDATE_ERROR`：資料更新失敗

---

## 11. 下載範例檔案

### API 端點
```
GET /import/template/:pattern
```

### 範例
```bash
# 下載 south 資料集的範例檔案
curl -OJ http://localhost:4000/import/template/south
```

或在瀏覽器中直接訪問：
```
http://localhost:4000/import/template/south
```

範例檔案已包含：
- 正確的 Worksheet 名稱
- 完整的欄位標題列
- 兩筆範例資料
- 各種格式的正確示範

---

## 12. 匯入流程

### 步驟 1：準備 Excel 檔案
1. 下載範例檔案
2. 保持第一列欄位標題不變
3. 從第四列開始填入實際資料
4. 確保必填欄位都有填寫
5. 儲存為 `.xlsx` 格式

### 步驟 2：上傳檔案
```bash
POST /import/file/:pattern
Content-Type: multipart/form-data
Authorization: Bearer <token>

{
  "file": <excel_file>,
  "userName": "使用者名稱",
  "userEmail": "<EMAIL>"
}
```

### 步驟 3：等待處理結果
- 成功：會收到電子郵件通知
- 失敗：會收到錯誤訊息和失敗原因

---

## 13. 技術細節

### 支援的資料集（Pattern）
目前系統支援以下資料集：
- `south`：南部地區土地資料

### 檔案限制
- 格式：`.xlsx`（Excel 2007 以上版本）
- 大小：根據系統設定（預設 50MB）
- 編碼：UTF-8

### 處理機制
- 系統使用 `node-xlsx` 解析 Excel 檔案
- 資料從第 4 列開始處理（`START_IDX = 2`）
- 支援自動生成唯一 ID
- 自動建立業主資料
- 自動關聯土地與事件資料

---

## 14. 相關文件

- [API 文檔](./API_DOCUMENTATION.md)
- [範例下載說明](../routers/handleImport/TEMPLATE_DOWNLOAD_README.md)
- [匯入配置](../config/config.import.js)
- [系統架構說明](./ARCHITECTURE.md)

---

## 15. 聯絡資訊

如有任何問題，請聯繫系統管理員或參考 [README.md](../README.md)。

---

**文件版本**: 1.0  
**最後更新**: 2025-10-01  
**維護者**: Land API Team
