# Excel 表頭快速檢查清單

## ✅ 上傳前必檢項目

在上傳 Excel 檔案前，請逐一檢查以下項目：

---

## 📋 表頭檢查清單（第 1 列）

### 重點檢查欄位（最容易出錯）

- [ ] **第 13 欄** - 土地標示起始日期
  ```
  正確：Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent
  錯誤：Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent-->year、month、day
  ```

- [ ] **第 19 欄** - 土地權利起始日期
  ```
  正確：Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent
  錯誤：Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent-->year、month、day
  ```

- [ ] **第 21 欄** - 所有權人
  ```
  正確：Land-->hasEvent-->LandRights-->hasOwner-->Owner
  錯誤：Land-->hasEvent-->LandRights-->hasOwner-->label
  ```

---

## 🔍 完整 21 欄檢查

複製以下內容到 Excel 第一列，確保每個欄位都完全一致：

```
Land-->collectionPlace
Land-->operator
Land-->boxNumber
Land-->number
Land-->landName
Land-->landSerialNumber
Land-->abstract
Land-->source
Land-->pawnRight
Land-->plowingRight
Land-->hasEvent-->LandMark-->landMarkNumber
Land-->hasEvent-->LandMark-->cause
Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent
Land-->hasEvent-->LandMark-->landGrades
Land-->hasEvent-->LandMark-->landCategory
Land-->hasEvent-->LandMark-->landArea
Land-->hasEvent-->LandMark-->landRent
Land-->hasEvent-->LandRights-->landRightsNumber
Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent
Land-->hasEvent-->LandRights-->cause
Land-->hasEvent-->LandRights-->hasOwner-->Owner
```

---

## 🎯 快速驗證方法

### 方法一：使用 Excel 搜尋功能

1. 在 Excel 中按 `Ctrl + F`（或 Mac 的 `Cmd + F`）
2. 搜尋以下錯誤關鍵字：

   - [ ] 搜尋 `-->year、month、day`（應該找不到）
   - [ ] 搜尋 `-->label`（應該找不到）
   - [ ] 搜尋 `-->Owner`（應該找到 1 個，在第 21 欄）

### 方法二：欄位計數檢查

1. 選取第 1 列的所有資料
2. 檢查已填入的欄位數量：**應該剛好 21 個**
3. 太多或太少都表示有問題

### 方法三：使用範本比對

1. 下載系統提供的「範本」檔案
2. 將您的第 1 列與範本的第 1 列逐一比對
3. 確保每個欄位的文字完全一致（包括大小寫、標點符號）

---

## ⚠️ 常見錯誤對照表

| 錯誤類型 | 錯誤範例 | 正確範例 |
|---------|---------|---------|
| 日期欄位多了後綴 | `DateEvent-->year、month、day` | `DateEvent` |
| 箭頭符號錯誤 | `Land->source` | `Land-->source` |
| 大小寫錯誤 | `Land-->owner` | `Land-->Owner` |
| 單字拼錯 | `hasOwner-->label` | `hasOwner-->Owner` |
| 欄位順序錯誤 | `source` 在第 10 欄 | `source` 在第 8 欄 |
| 缺少欄位 | 只有 20 個欄位 | 必須有 21 個欄位 |
| 多餘欄位 | 有 22 個欄位 | 必須有 21 個欄位 |

---

## 🚀 上傳後驗證

上傳檔案後，應該看到以下訊息之一：

### ✅ 成功訊息
```
✅ 驗證通過
共 X 筆資料，0 個錯誤
預計匯入時間：約 Y 分鐘
```

### ❌ 失敗訊息（有具體錯誤說明）
```
❌ 驗證失敗
發現 3 個錯誤，請修正後重新上傳

📊 表頭格式錯誤 [3 個錯誤]
  └─ 第 19 欄：預期為「...DateEvent」，實際為「...DateEvent-->year、month、day」
  └─ ...
```

### ⚠️ 異常訊息（需要檢查）
```
❌ 驗證失敗
發現 0 個錯誤，請修正後重新上傳
```

**如果看到「0 個錯誤」但驗證失敗**：
1. 清除瀏覽器快取後重新整理（Ctrl + F5）
2. 重新下載範本並比對第 1 列
3. 確認檔案格式為 `.xlsx` 或 `.xls`

---

## 💡 最佳實踐

### ✅ 推薦做法

1. **使用範本**
   - 永遠從系統「下載範本」開始
   - 不要手動建立或修改第 1-3 列

2. **分段檢查**
   - 先檢查第 1-10 欄
   - 再檢查第 11-21 欄
   - 重點檢查第 13、19、21 欄

3. **保留備份**
   - 修改前先複製一份原始檔案
   - 修改後也保存一份「已修正」版本

### ❌ 避免做法

1. ❌ 手動輸入欄位名稱（容易拼錯）
2. ❌ 從舊檔案複製表頭（可能格式已過時）
3. ❌ 調整欄位順序（必須按照標準順序）
4. ❌ 增加或刪除欄位（必須剛好 21 個）
5. ❌ 使用全形符號（如全形的 `－`、`＞`）

---

## 📞 故障排除

### 問題：看到「發現 0 個錯誤」但驗證失敗

**可能原因**：
- 系統錯誤檢測有問題
- 瀏覽器快取導致舊版本程式碼

**解決方法**：
1. 按 `Ctrl + F5` 強制重新整理頁面
2. 清除瀏覽器快取
3. 手動檢查第 13、19、21 欄是否正確
4. 重新下載範本並比對

### 問題：修改後仍然顯示錯誤

**檢查項目**：
- [ ] 是否遺漏某個欄位的修改？
- [ ] 是否有隱藏的空白字元？
- [ ] 是否使用全形字元？
- [ ] 檔案格式是否為 `.xlsx` 或 `.xls`？

---

## 📥 快速修正工作流程

```
1. 下載「範本」檔案
   ↓
2. 開啟您的原始 Excel 檔案
   ↓
3. 複製範本的第 1-3 列
   ↓
4. 貼上到您的檔案，覆蓋原本的第 1-3 列
   ↓
5. 確認資料從第 4 列開始（沒有被覆蓋）
   ↓
6. 儲存檔案
   ↓
7. 重新上傳
   ↓
8. 查看驗證結果
```

---

**最後更新**: 2025-10-01  
**版本**: 1.0.0
