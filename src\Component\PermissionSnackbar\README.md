# PermissionSnackbar 組件

可重用的權限通知組件，用於取代 `alert()` 提供更優雅的使用者提示。

## 功能特點

- ✅ **自動隱藏**：預設 4 秒後自動關閉
- ✅ **多種嚴重程度**：支援 error、warning、info、success
- ✅ **自定義位置**：可配置顯示位置（預設頂部居中）
- ✅ **多行文字支援**：支援換行顯示長訊息
- ✅ **Material-UI 設計**：使用 filled 風格的 Alert
- ✅ **易於使用**：提供 Hook 簡化狀態管理

## 基本用法

### 1. 使用 Hook（推薦）

```javascript
import React from 'react';
import PermissionSnackbar from '../../../Component/PermissionSnackbar';
import usePermissionSnackbar from '../../../Component/PermissionSnackbar/usePermissionSnackbar';
import { getPermissionMessage } from '../../../utils/permissionUtils';

function MyComponent() {
  const { snackbarState, showError, hideSnackbar } = usePermissionSnackbar();

  const handleSave = () => {
    if (!hasPermission) {
      // 顯示權限錯誤
      showError(getPermissionMessage(user, OPERATION.EDIT));
      return;
    }
    // 執行儲存邏輯
  };

  return (
    <>
      <button onClick={handleSave}>儲存</button>
      
      <PermissionSnackbar
        open={snackbarState.open}
        onClose={hideSnackbar}
        message={snackbarState.message}
        severity={snackbarState.severity}
      />
    </>
  );
}
```

### 2. 手動管理狀態

```javascript
import React, { useState } from 'react';
import PermissionSnackbar from '../../../Component/PermissionSnackbar';

function MyComponent() {
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'error',
  });

  const handleError = () => {
    setSnackbar({
      open: true,
      message: '您沒有執行此操作的權限',
      severity: 'error',
    });
  };

  const handleClose = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  return (
    <>
      <button onClick={handleError}>測試錯誤</button>
      
      <PermissionSnackbar
        open={snackbar.open}
        onClose={handleClose}
        message={snackbar.message}
        severity={snackbar.severity}
      />
    </>
  );
}
```

## Hook API

### usePermissionSnackbar()

返回一個包含以下屬性的物件：

#### 狀態
- `snackbarState`: `{ open: boolean, message: string, severity: string }`

#### 方法
- `showSnackbar(message, severity)` - 顯示指定嚴重程度的訊息
- `showError(message)` - 顯示錯誤訊息（紅色）
- `showWarning(message)` - 顯示警告訊息（橙色）
- `showInfo(message)` - 顯示提示訊息（藍色）
- `showSuccess(message)` - 顯示成功訊息（綠色）
- `hideSnackbar()` - 關閉 Snackbar

### 範例：使用快捷方法

```javascript
const { snackbarState, showError, showSuccess, hideSnackbar } = usePermissionSnackbar();

// 顯示錯誤
showError('權限不足，無法執行此操作');

// 顯示成功
showSuccess('資料已成功儲存！');

// 顯示警告
showWarning('此操作將會覆蓋現有資料');

// 顯示提示
showInfo('請先選擇要編輯的項目');
```

## 組件 Props

| Prop | 類型 | 預設值 | 說明 |
|------|------|--------|------|
| `open` | boolean | - | 是否顯示 Snackbar |
| `onClose` | function | - | 關閉時的回調函數 |
| `message` | string | - | 要顯示的訊息內容 |
| `severity` | 'error' \| 'warning' \| 'info' \| 'success' | 'error' | 訊息的嚴重程度 |
| `autoHideDuration` | number | 4000 | 自動隱藏時間（毫秒） |
| `anchorOrigin` | object | `{ vertical: 'top', horizontal: 'center' }` | 顯示位置 |
| `action` | ReactNode | null | 自定義操作按鈕 |

## 自定義位置

```javascript
<PermissionSnackbar
  open={open}
  onClose={handleClose}
  message="訊息內容"
  severity="error"
  anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
/>
```

可用位置：
- `vertical`: 'top' | 'bottom'
- `horizontal`: 'left' | 'center' | 'right'

## 自定義自動隱藏時間

```javascript
<PermissionSnackbar
  open={open}
  onClose={handleClose}
  message="此訊息將在 6 秒後關閉"
  severity="info"
  autoHideDuration={6000}  // 6 秒
/>
```

## 多行訊息

組件支援換行字元 `\n`：

```javascript
showError('操作失敗\n原因：權限不足\n請聯絡管理員');
```

## 與 permissionUtils 整合

```javascript
import { getPermissionMessage, OPERATION } from '../../../utils/permissionUtils';
import { canEdit } from '../../../utils/permissionUtils';

const handleEdit = () => {
  if (!canEdit(user)) {
    showError(getPermissionMessage(user, OPERATION.EDIT));
    return;
  }
  // 執行編輯邏輯
};
```

## 遷移指南

### 從 alert() 遷移

**Before (使用 alert):**
```javascript
const handleSave = () => {
  if (!hasPermission) {
    alert(getPermissionMessage(user, OPERATION.EDIT));
    return;
  }
  // ...
};
```

**After (使用 PermissionSnackbar):**
```javascript
const { snackbarState, showError, hideSnackbar } = usePermissionSnackbar();

const handleSave = () => {
  if (!hasPermission) {
    showError(getPermissionMessage(user, OPERATION.EDIT));
    return;
  }
  // ...
};

// 在 JSX 中加入
<PermissionSnackbar
  open={snackbarState.open}
  onClose={hideSnackbar}
  message={snackbarState.message}
  severity={snackbarState.severity}
/>
```

## 注意事項

1. 每個頁面只需要一個 PermissionSnackbar 實例
2. 使用 `showError` 等快捷方法可以讓代碼更簡潔
3. 避免同時顯示多個 Snackbar（新訊息會覆蓋舊訊息）
4. 權限錯誤建議使用 `severity="error"`
5. 成功操作建議使用 `severity="success"`
