import { isEmpty } from "../../../../utils";
import { Api, createOntoData } from "../../../../api/land/Api";

export const historyType = {
  updateBasic: "變更基本資料",
  updateLandMarkInfo: "變更土地標記資料",
  createLandMarkInfo: "建立土地標記資料",
  deleteLandMarkInfo: "刪除土地標記資料",
  updateLandRightInfo: "變更土地權利資料",
  createLandRightInfo: "建立土地權利資料",
  deleteLandRightInfo: "刪除土地權利資料",
};

// 產出歷史訊息
/**
 * preCol: 原始資料value
 * modifyCol: 變更資料value
 * type: 歷史訊息類別
 * infoArr: (optional) 所有資料的陣列，需要反查訊息可以使用
 * */
export const recordHistory = (preCol, modifyCol, type, infoArr = []) => {
  let recordStr = "";
  switch (type) {
    case historyType.updateBasic: {
      if (isEmpty(preCol) || isEmpty(modifyCol)) return "";
      recordStr += `${historyType.updateBasic}: \n\n`;
      preCol.forEach((element) => {
        const findValue = modifyCol.find(
          (item) => item.predicate === element.predicate
        );
        recordStr += `${element.object}: 從 [${element.value}] 改成 [${findValue.value}] \n\n`;
      });
      return `${recordStr}\n`;
    }
    case historyType.updateLandMarkInfo: {
      if (isEmpty(preCol) || isEmpty(modifyCol)) return "";
      recordStr += `${historyType.updateLandMarkInfo}: \n\n`;

      // 先找出所有ID
      let LMIDs = preCol.map((element) => element.landMarkId);
      LMIDs = LMIDs.filter((item, pos) => LMIDs.indexOf(item) === pos);

      // 從ID反查landMarkNumber
      const LMNumbers = infoArr
        .filter((info) => info.find((data) => LMIDs.includes(data.landMarkId)))
        .map((info) => {
          const findObj = info.find(
            (element) => element.predicate === "landMarkNumber"
          );
          return {
            number: findObj.value,
            id: findObj.landMarkId,
          };
        });

      LMNumbers.forEach((element) => {
        const findSrc = preCol.filter((item) => item.landMarkId === element.id);
        const findDst = modifyCol.filter(
          (item) => item.landMarkId === element.id
        );
        recordStr += `土地標記變更次序 ${element.number}: \n`;
        findSrc.forEach((obj) => {
          const findValue = findDst.find(
            (item) => item.predicate === obj.predicate
          );
          recordStr += `${obj.object}: 從 [${obj.value}] 改成 [${findValue.value}] \n\n`;
        });
      });
      return `${recordStr}\n`;
    }
    case historyType.createLandMarkInfo: {
      if (isEmpty(preCol)) return "";
      recordStr += `${historyType.createLandMarkInfo}: \n\n`;
      preCol.forEach((obj) => {
        const findNumber = obj.find(
          (element) => element.predicate === "landMarkNumber"
        ).value;
        recordStr += `建立土地標記次序 ${findNumber} \n`;
        obj.forEach((element) => {
          recordStr += `${element.object}: [${element.value}]\n`;
        });
      });
      return `${recordStr}\n`;
    }
    case historyType.deleteLandMarkInfo: {
      if (isEmpty(preCol)) return "";
      recordStr += `${historyType.deleteLandMarkInfo}: \n\n`;
      // 從ID反查landMarkNumber
      const LMNumbers = infoArr
        .filter((element) => element.predicate === "landMarkNumber")
        .filter((element) => preCol.includes(element.landMarkId));

      LMNumbers.forEach((element) => {
        recordStr += `刪除土地標記次序 ${element.value} 資料 \n`;
      });

      return `${recordStr}\n`;
    }
    case historyType.updateLandRightInfo: {
      if (isEmpty(preCol) || isEmpty(modifyCol)) return "";
      recordStr += `${historyType.updateLandRightInfo}: \n\n`;

      // 先找出所有ID
      let LRIDs = preCol.map((element) => element.landRightsId);
      LRIDs = LRIDs.filter((item, pos) => LRIDs.indexOf(item) === pos);

      // 從ID反查landRightNumber
      const LRNumbers = infoArr
        .filter((info) =>
          info.find((data) => LRIDs.includes(data.landRightsId))
        )
        .map((info) => {
          const findObj = info.find(
            (element) => element.predicate === "landRightsNumber"
          );
          return {
            number: findObj.value,
            id: findObj.landRightsId,
          };
        });

      LRNumbers.forEach((element) => {
        const findSrc = preCol.filter(
          (item) => item.landRightsId === element.id
        );
        const findDst = modifyCol.filter(
          (item) => item.landRightsId === element.id
        );
        recordStr += `土地權利變更次序 ${element.number}: \n`;
        findSrc.forEach((obj) => {
          const findValue = findDst.find(
            (item) => item.predicate === obj.predicate
          );
          recordStr += `${obj.object}: 從 [${obj.value}] 改成 [${findValue.value}] \n\n`;
        });
      });
      return `${recordStr}\n`;
    }
    case historyType.createLandRightInfo: {
      if (isEmpty(preCol)) return "";
      recordStr += `${historyType.createLandRightInfo}: \n\n`;
      preCol.forEach((obj) => {
        const findNumber = obj.find(
          (element) => element.predicate === "landRightsNumber"
        ).value;
        recordStr += `建立土地權利次序 ${findNumber} \n`;
        obj.forEach((element) => {
          recordStr += `${element.object}: [${element.value}]\n`;
        });
      });
      return `${recordStr}\n`;
    }
    case historyType.deleteLandRightInfo: {
      if (isEmpty(preCol)) return "";
      recordStr += `${historyType.deleteLandRightInfo}: \n\n`;
      // 從ID反查landRightNumber
      const LRNumbers = infoArr
        .filter((element) => element.predicate === "landRightsNumber")
        .filter((element) => preCol.includes(element.landRightsId));

      LRNumbers.forEach((element) => {
        recordStr += `刪除土地權利次序 ${element.value} 資料 \n`;
      });

      return `${recordStr}\n`;
    }
    default:
      break;
  }
};

export const createHistoryEvent = (date, message, user, landId) =>
  new Promise((resolve, reject) => {
    const entry = {
      graph: "south",
      classType: "Land",
      srcId: landId,
      value: {
        hasHistory: {
          classType: "HistoryEvent",
          srcId: "",
          value: {
            user,
            time: date.toString(),
            dateTime: date.toISOString(),
            action: message,
          },
        },
      },
    };

    createOntoData(Api.restfulCRUD(), entry)
      .then((result) => {
        resolve(result.state);
      })
      .catch((error) => {
        reject(error);
      });
  });
