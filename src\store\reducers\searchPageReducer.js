import Act from "../actions";

const initState = {
  keyWord: "",
  searchColList: [], // 搜尋欄位清單
  resultList: [], // 條列搜尋結果
  searchLoading: false, // 搜尋中，顯示loading圖
  pageNumber: 1, // 顯示頁數
  searchDuration: 0, // 搜尋時間
};

const searchPageReducer = (state = initState, action) => {
  switch (action.type) {
    case Act.SET_KEYWORD:
      return { ...state, keyWord: action.payload };
    case Act.SET_SEARCHCOLLIST:
      return { ...state, searchColList: action.payload };
    case Act.SET_RESULTLIST:
      return { ...state, resultList: action.payload };
    case Act.SET_SEARCHLOADING:
      return { ...state, searchLoading: action.payload };
    case Act.SET_PAGENUMBER:
      return { ...state, pageNumber: action.payload };
    case Act.SET_SEARCHDURATION:
      return { ...state, searchDuration: action.payload };
    default:
      return state;
  }
};

export default searchPageReducer;
