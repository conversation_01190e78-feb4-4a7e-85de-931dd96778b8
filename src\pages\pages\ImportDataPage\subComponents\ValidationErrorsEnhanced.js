import React, { useState, useMemo } from 'react';
import {
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Chip,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  ToggleButtonGroup,
  ToggleButton,
  Tabs,
  Tab,
  Pagination,
  Button,
  LinearProgress,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  TableChart as TableChartIcon,
  ViewList as ViewListIcon,
  ContentCopy as ContentCopyIcon,
  Visibility as VisibilityIcon,
  Download as DownloadIcon,
  Assessment as AssessmentIcon,
  Lightbulb as LightbulbIcon,
  BarChart as BarChartIcon,
  GpsFixed as GpsFixedIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import './ValidationErrorsEnhanced.scss';

/**
 * 增強版驗證錯誤顯示元件（優化版）
 * 支援列表檢視、表格檢視、統計摘要、分頁、錯誤報告下載
 * 
 * 優化重點：
 * 1. 分頁顯示：每頁 50 個錯誤，避免渲染過多 DOM
 * 2. 統計摘要：快速了解錯誤分佈
 * 3. 錯誤報告下載：可離線查看完整錯誤清單
 * 4. 大量錯誤警告：提供優化建議
 * 5. 原始資料顯示：開發模式下可查看實際讀取的 Excel 資料
 */
const ValidationErrorsEnhanced = ({ errors = [], rawExcelData = {} }) => {
  const [viewMode, setViewMode] = useState('list'); // 'list' | 'table' | 'summary'
  const [expandedRow, setExpandedRow] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [errorsPerPage] = useState(50); // 每頁顯示 50 個錯誤

  // 分類錯誤
  const categorizedErrors = useMemo(() => {
    const categories = {
      header_order: [],
      header_content: [],
      data_error: [],
      system_error: [],
    };

    errors.forEach((error) => {
      const type = error.type || 'system_error';
      if (categories[type]) {
        categories[type].push(error);
      }
    });

    return categories;
  }, [errors]);

  // 按列分組錯誤（用於表格檢視）
  const errorsByRow = useMemo(() => {
    const grouped = {};
    errors.forEach((error) => {
      if (error.rowNumber) {
        if (!grouped[error.rowNumber]) {
          grouped[error.rowNumber] = [];
        }
        grouped[error.rowNumber].push(error);
      }
    });
    return grouped;
  }, [errors]);

  // 錯誤統計
  const errorStats = useMemo(() => {
    const stats = {
      total: errors.length,
      byType: {},
      byRow: {},
      byColumn: {},
      topErrors: [],
    };

    // 按類型統計
    Object.entries(categorizedErrors).forEach(([type, typeErrors]) => {
      stats.byType[type] = typeErrors.length;
    });

    // 按列統計
    errors.forEach((error) => {
      if (error.rowNumber) {
        stats.byRow[error.rowNumber] = (stats.byRow[error.rowNumber] || 0) + 1;
      }
      if (error.columnName) {
        stats.byColumn[error.columnName] = (stats.byColumn[error.columnName] || 0) + 1;
      }
    });

    // 找出錯誤最多的前 10 列
    stats.topErrors = Object.entries(stats.byRow)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([row, count]) => ({ row: Number(row), count }));

    return stats;
  }, [errors, categorizedErrors]);

  // 當前頁的錯誤
  const getCurrentPageErrors = (errorList) => {
    const startIndex = (currentPage - 1) * errorsPerPage;
    const endIndex = startIndex + errorsPerPage;
    return errorList.slice(startIndex, endIndex);
  };

  // 當前標籤頁的錯誤
  const getCurrentTabErrors = () => {
    if (activeTab === 0) return errors;
    if (activeTab === 1) return [...categorizedErrors.header_order, ...categorizedErrors.header_content];
    if (activeTab === 2) return categorizedErrors.data_error;
    return [];
  };

  const totalPages = Math.ceil(getCurrentTabErrors().length / errorsPerPage);

  const getCategoryLabel = (type) => {
    const labels = {
      header_order: '標題順序錯誤',
      header_content: '標題內容錯誤',
      data_error: '資料內容錯誤',
      system_error: '系統錯誤',
    };
    return labels[type] || '其他錯誤';
  };

  const getErrorIcon = (severity) => (
    severity === 'warning' ? (
      <WarningIcon color="warning" />
    ) : (
      <ErrorIcon color="error" />
    )
  );

  const handleCopyLocation = (row, col) => {
    const location = `Row ${row}, Column ${col}`;
    navigator.clipboard.writeText(location);
  };

  const handleToggleRowDetails = (rowNumber) => {
    // 如果當前不是表格檢視，自動切換到表格檢視
    if (viewMode !== 'table') {
      setViewMode('table');
      setExpandedRow(rowNumber);
      // 稍微延遲滾動，等待視圖切換完成
      setTimeout(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }, 100);
    } else {
      // 在表格檢視中，切換展開/收合狀態
      setExpandedRow(expandedRow === rowNumber ? null : rowNumber);
    }
  };

  const handlePageChange = (event, value) => {
    setCurrentPage(value);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setCurrentPage(1); // 切換標籤時重置到第一頁
  };

  // 下載錯誤報告
  const handleDownloadErrorReport = () => {
    const reportLines = ['錯誤報告', `生成時間：${new Date().toLocaleString('zh-TW')}`, '', ''];

    // 統計摘要
    reportLines.push('=== 錯誤統計 ===');
    reportLines.push(`總錯誤數：${errorStats.total}`);
    reportLines.push(`標題錯誤：${errorStats.byType.header_order + errorStats.byType.header_content}`);
    reportLines.push(`資料錯誤：${errorStats.byType.data_error}`);
    reportLines.push('');

    // 錯誤最多的列
    reportLines.push('=== 錯誤最多的前 10 列 ===');
    errorStats.topErrors.forEach(({ row, count }) => {
      reportLines.push(`第 ${row} 列：${count} 個錯誤`);
    });
    reportLines.push('');

    // 詳細錯誤清單
    reportLines.push('=== 詳細錯誤清單 ===');
    errors.forEach((error, index) => {
      reportLines.push(`\n[錯誤 ${index + 1}]`);
      if (error.rowNumber) {
        reportLines.push(`位置：第 ${error.rowNumber} 列，${error.columnName || `欄位 ${error.columnIndex}`}`);
      }
      reportLines.push(`訊息：${error.message || error.toString()}`);
      if (error.suggestion) {
        reportLines.push(`建議：${error.suggestion}`);
      }
      if (error.expected) {
        reportLines.push(`期望值：${error.expected}`);
      }
      if (error.actual) {
        reportLines.push(`實際值：${error.actual}`);
      }
    });

    // 創建並下載檔案
    const blob = new Blob([reportLines.join('\n')], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `錯誤報告_${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 統計摘要檢視
  const renderSummaryView = () => (
    <Box className="validation-errors-summary">
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <BarChartIcon sx={{ fontSize: 18 }} /> 統計摘要：快速了解錯誤分佈，幫助您優先處理問題最多的區域
        </Typography>
      </Alert>

      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="h3" color="error">{errorStats.total}</Typography>
          <Typography variant="body2" color="text.secondary">總錯誤數</Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="h3" color="warning.main">
            {errorStats.byType.header_order + errorStats.byType.header_content}
          </Typography>
          <Typography variant="body2" color="text.secondary">標題錯誤</Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="h3" color="error">{errorStats.byType.data_error}</Typography>
          <Typography variant="body2" color="text.secondary">資料錯誤</Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="h3" color="primary">
            {Object.keys(errorStats.byRow).length}
          </Typography>
          <Typography variant="body2" color="text.secondary">受影響列數</Typography>
        </Paper>
      </Box>

      {/* 錯誤最多的列 */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <GpsFixedIcon /> 錯誤最多的前 10 列（建議優先修正）
        </Typography>
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>排名</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>列號</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>錯誤數</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>錯誤比例</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {errorStats.topErrors.map((item, index) => (
                <TableRow key={item.row} hover>
                  <TableCell>#{index + 1}</TableCell>
                  <TableCell>
                    <Chip label={`Row ${item.row}`} size="small" color="error" />
                  </TableCell>
                  <TableCell>
                    <strong>{item.count}</strong> 個
                  </TableCell>
                  <TableCell>
                    <LinearProgress
                      variant="determinate"
                      value={(item.count / errorStats.total) * 100}
                      sx={{ width: 100 }}
                    />
                  </TableCell>
                  <TableCell>
                    <Button
                      size="small"
                      onClick={() => {
                        setViewMode('table');
                        setExpandedRow(item.row);
                      }}
                    >
                      查看詳情
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* 按欄位統計 */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AssignmentIcon /> 錯誤欄位分佈
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {Object.entries(errorStats.byColumn)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 15)
            .map(([column, count]) => (
              <Chip
                key={column}
                label={`${column} (${count})`}
                size="small"
                color={count > 10 ? 'error' : 'default'}
                variant={count > 10 ? 'filled' : 'outlined'}
              />
            ))}
        </Box>
      </Paper>
    </Box>
  );

  // 列表檢視（分頁版）
  const renderListView = () => {
    const currentTabErrors = getCurrentTabErrors();
    const currentPageErrors = getCurrentPageErrors(currentTabErrors);

    return (
      <Box className="validation-errors-list">
        <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
          <Tab label={`全部 (${errors.length})`} />
          <Tab label={`標題錯誤 (${categorizedErrors.header_order.length + categorizedErrors.header_content.length})`} />
          <Tab label={`資料錯誤 (${categorizedErrors.data_error.length})`} />
        </Tabs>

        {/* 分頁資訊 */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            顯示 {(currentPage - 1) * errorsPerPage + 1} - {Math.min(currentPage * errorsPerPage, currentTabErrors.length)} / 共 {currentTabErrors.length} 個錯誤
          </Typography>
          {totalPages > 1 && (
            <Pagination
              count={totalPages}
              page={currentPage}
              onChange={handlePageChange}
              size="small"
              showFirstButton
              showLastButton
            />
          )}
        </Box>

        {/* 全部錯誤 */}
        {activeTab === 0 && (
          <>
            {Object.entries(categorizedErrors).map(([type, typeErrors]) => {
              const pageTypeErrors = getCurrentPageErrors(typeErrors);
              if (pageTypeErrors.length === 0) return null;

              return (
                <Accordion key={type} defaultExpanded={pageTypeErrors.length > 0}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                      {getErrorIcon('error')}
                      <Typography sx={{ flexGrow: 1 }}>
                        {getCategoryLabel(type)}
                      </Typography>
                      <Chip
                        label={`${pageTypeErrors.length}/${typeErrors.length}`}
                        size="small"
                        color="error"
                        sx={{ mr: 2 }}
                      />
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    {pageTypeErrors.map((error, idx) => (
                      <Alert
                        key={idx}
                        severity={error.severity || 'error'}
                        sx={{ mb: 1 }}
                        action={
                          error.rowNumber && (
                            <Box>
                              <Tooltip title="複製位置">
                                <IconButton
                                  size="small"
                                  onClick={() => handleCopyLocation(error.rowNumber, error.columnIndex)}
                                >
                                  <ContentCopyIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title={viewMode === 'table' ? "切換顯示詳情" : "在表格中查看"}>
                                <IconButton
                                  size="small"
                                  onClick={() => handleToggleRowDetails(error.rowNumber)}
                                >
                                  <VisibilityIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          )
                        }
                      >
                        <Box>
                          {error.rowNumber && (
                            <Typography variant="caption" display="block" sx={{ mb: 0.5 }}>
                              <Chip
                                label={`第 ${error.rowNumber} 列`}
                                size="small"
                                variant="outlined"
                                sx={{ mr: 1 }}
                              />
                              {error.columnName && (
                                <Chip
                                  label={error.columnName}
                                  size="small"
                                  variant="outlined"
                                />
                              )}
                            </Typography>
                          )}
                          <Typography variant="body2">
                            {error.message || error.toString()}
                          </Typography>
                          {error.suggestion && (
                            <Typography variant="caption" sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 0.5, color: 'success.main' }}>
                              <LightbulbIcon sx={{ fontSize: 14 }} /> 建議：{error.suggestion}
                            </Typography>
                          )}
                          {error.expected && (
                            <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                              期望值：<code>{error.expected}</code>
                              {error.actual && <> | 實際值：<code>{error.actual}</code></>}
                            </Typography>
                          )}
                        </Box>
                      </Alert>
                    ))}
                  </AccordionDetails>
                </Accordion>
              );
            })}
          </>
        )}

        {/* 標題錯誤 */}
        {activeTab === 1 && (
          <>
            {currentPageErrors.map((error, idx) => (
              <Alert key={idx} severity={error.severity || 'error'} sx={{ mb: 1 }}>
                <Typography variant="body2">{error.message}</Typography>
                {error.suggestion && (
                  <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 1, color: 'success.main' }}>
                    <LightbulbIcon sx={{ fontSize: 14 }} /> 建議：{error.suggestion}
                  </Typography>
                )}
              </Alert>
            ))}
          </>
        )}

        {/* 資料錯誤 */}
        {activeTab === 2 && (
          <>
            {currentPageErrors.map((error, idx) => (
              <Alert
                key={idx}
                severity={error.severity || 'error'}
                sx={{ mb: 1 }}
                action={
                  <Box>
                    <Tooltip title="複製位置">
                      <IconButton
                        size="small"
                        onClick={() => handleCopyLocation(error.rowNumber, error.columnIndex)}
                      >
                        <ContentCopyIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title={viewMode === 'table' ? "切換顯示詳情" : "在表格中查看"}>
                      <IconButton
                        size="small"
                        onClick={() => handleToggleRowDetails(error.rowNumber)}
                      >
                        <VisibilityIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                }
              >
                <Box>
                  <Typography variant="caption" display="block" sx={{ mb: 0.5 }}>
                    <Chip
                      label={`第 ${error.rowNumber} 列`}
                      size="small"
                      variant="outlined"
                      sx={{ mr: 1 }}
                    />
                    {error.columnName && (
                      <Chip
                        label={error.columnName}
                        size="small"
                        variant="outlined"
                      />
                    )}
                  </Typography>
                  <Typography variant="body2">{error.message}</Typography>
                  {error.suggestion && (
                    <Typography variant="caption" sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 0.5, color: 'success.main' }}>
                      <LightbulbIcon sx={{ fontSize: 14 }} /> 建議：{error.suggestion}
                    </Typography>
                  )}
                </Box>
              </Alert>
            ))}
          </>
        )}

        {/* 底部分頁 */}
        {totalPages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
            <Pagination
              count={totalPages}
              page={currentPage}
              onChange={handlePageChange}
              showFirstButton
              showLastButton
            />
          </Box>
        )}
      </Box>
    );
  };

  // 表格檢視（分頁版）
  const renderTableView = () => {
    const rowNumbers = Object.keys(errorsByRow).map(Number).sort((a, b) => a - b);
    const currentPageRows = getCurrentPageErrors(rowNumbers);
    const totalTablePages = Math.ceil(rowNumbers.length / errorsPerPage);

    return (
      <Box className="validation-errors-table">
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <BarChartIcon sx={{ fontSize: 18 }} /> 表格檢視：顯示第 {(currentPage - 1) * errorsPerPage + 1} - {Math.min(currentPage * errorsPerPage, rowNumbers.length)} 列 / 共 {rowNumbers.length} 列有錯誤
          </Typography>
        </Alert>

        {totalTablePages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
            <Pagination
              count={totalTablePages}
              page={currentPage}
              onChange={handlePageChange}
              size="small"
              showFirstButton
              showLastButton
            />
          </Box>
        )}

        <TableContainer component={Paper} sx={{ maxHeight: 600 }}>
          <Table stickyHeader size="small">
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold', bgcolor: 'primary.light' }}>
                  列號
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', bgcolor: 'primary.light' }}>
                  錯誤數
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', bgcolor: 'primary.light' }}>
                  錯誤欄位
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', bgcolor: 'primary.light' }}>
                  操作
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {currentPageRows.map((rowNumber) => {
                const rowErrors = errorsByRow[rowNumber];
                return (
                  <React.Fragment key={rowNumber}>
                    <TableRow
                      hover
                      sx={{
                        bgcolor: expandedRow === rowNumber ? 'action.hover' : 'inherit',
                        cursor: 'pointer',
                      }}
                      onClick={() => handleToggleRowDetails(rowNumber)}
                    >
                      <TableCell>
                        <Chip
                          label={`Row ${rowNumber}`}
                          size="small"
                          color="error"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip label={rowErrors.length} size="small" color="error" />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {rowErrors.map((err, idx) => (
                            <Chip
                              key={idx}
                              label={err.columnName || `Col ${err.columnIndex}`}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '0.7rem' }}
                            />
                          ))}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <IconButton size="small">
                          <ExpandMoreIcon
                            sx={{
                              transform: expandedRow === rowNumber ? 'rotate(180deg)' : 'rotate(0deg)',
                              transition: 'transform 0.3s',
                            }}
                          />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                    {expandedRow === rowNumber && (
                      <TableRow>
                        <TableCell colSpan={4} sx={{ bgcolor: 'grey.50', p: 2 }}>
                          <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold' }}>
                            第 {rowNumber} 列的錯誤詳情：
                          </Typography>
                          {rowErrors.map((error, idx) => (
                            <Alert key={idx} severity="error" sx={{ mb: 1 }}>
                              <Typography variant="body2">
                                <strong>{error.columnName || `欄位 ${error.columnIndex}`}：</strong>
                                {error.message}
                              </Typography>
                              {error.suggestion && (
                                <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5, color: 'success.main' }}>
                                  <LightbulbIcon sx={{ fontSize: 14 }} /> {error.suggestion}
                                </Typography>
                              )}
                            </Alert>
                          ))}
                          
                          {/* 顯示原始 Excel 資料 (開發用) */}
                          {rawExcelData[rowNumber] && (
                            <Box sx={{ mt: 2, p: 2, bgcolor: 'info.lighter', borderRadius: 1 }}>
                              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1, display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <AssignmentIcon sx={{ fontSize: 18 }} /> 
                                原始 Excel 資料 (開發模式)
                              </Typography>
                              <Box sx={{ 
                                bgcolor: 'background.paper', 
                                p: 1.5, 
                                borderRadius: 1, 
                                fontFamily: 'monospace',
                                fontSize: '0.85rem',
                                maxHeight: '300px',
                                overflow: 'auto'
                              }}>
                                <Typography variant="caption" sx={{ display: 'block', mb: 1, color: 'text.secondary' }}>
                                  工作表: {rawExcelData[rowNumber].sheetName} | 列號: {rawExcelData[rowNumber].rowNumber}
                                </Typography>
                                <Table size="small" sx={{ '& td': { fontFamily: 'monospace', fontSize: '0.8rem' } }}>
                                  <TableHead>
                                    <TableRow>
                                      <TableCell sx={{ fontWeight: 'bold', width: '30%' }}>欄位 ID</TableCell>
                                      <TableCell sx={{ fontWeight: 'bold', width: '30%' }}>欄位名稱</TableCell>
                                      <TableCell sx={{ fontWeight: 'bold', width: '30%' }}>實際值</TableCell>
                                      <TableCell sx={{ fontWeight: 'bold', width: '10%' }}>類型</TableCell>
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>
                                    {Object.entries(rawExcelData[rowNumber].data).map(([fieldId, cellData]) => (
                                      <TableRow key={fieldId}>
                                        <TableCell sx={{ wordBreak: 'break-word' }}>{fieldId}</TableCell>
                                        <TableCell>{cellData.columnName}</TableCell>
                                        <TableCell sx={{ 
                                          color: cellData.value === null || cellData.value === undefined || cellData.value === '' 
                                            ? 'text.disabled' 
                                            : 'text.primary',
                                          fontStyle: cellData.value === null || cellData.value === undefined || cellData.value === '' 
                                            ? 'italic' 
                                            : 'normal'
                                        }}>
                                          {cellData.value === null || cellData.value === undefined || cellData.value === '' 
                                            ? '(空值)' 
                                            : String(cellData.value)}
                                        </TableCell>
                                        <TableCell>
                                          <Chip 
                                            label={cellData.type || 'unknown'} 
                                            size="small" 
                                            variant="outlined"
                                            sx={{ fontSize: '0.7rem' }}
                                          />
                                        </TableCell>
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </Box>
                            </Box>
                          )}
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>

        {totalTablePages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <Pagination
              count={totalTablePages}
              page={currentPage}
              onChange={handlePageChange}
              showFirstButton
              showLastButton
            />
          </Box>
        )}
      </Box>
    );
  };

  if (!errors || errors.length === 0) {
    return null;
  }

  // 大量錯誤警告
  const showWarning = errors.length > 100;

  return (
    <Box className="validation-errors-enhanced" sx={{ mt: 3 }}>
      {/* 大量錯誤警告 */}
      {showWarning && (
        <Alert severity="warning" sx={{ mb: 2 }} icon={<WarningIcon />}>
          <Typography variant="body2" gutterBottom>
            偵測到大量錯誤（{errors.length} 個），建議：
          </Typography>
          <Typography variant="caption" component="div">
            1. 先查看「統計摘要」了解問題分佈<br />
            2. 優先修正錯誤最多的列<br />
            3. 下載完整錯誤報告以便離線查看
          </Typography>
        </Alert>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, flexWrap: 'wrap', gap: 2 }}>
        <Typography variant="h6">
          錯誤詳情
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Button
            variant="outlined"
            size="small"
            startIcon={<DownloadIcon />}
            onClick={handleDownloadErrorReport}
          >
            下載錯誤報告
          </Button>
          <ToggleButtonGroup
            value={viewMode}
            exclusive
            onChange={(e, val) => {
              if (val) {
                setViewMode(val);
                setCurrentPage(1); // 切換檢視時重置頁碼
              }
            }}
            size="small"
          >
            <ToggleButton value="summary">
              <AssessmentIcon sx={{ mr: 0.5 }} />
              統計摘要
            </ToggleButton>
            <ToggleButton value="list">
              <ViewListIcon sx={{ mr: 0.5 }} />
              列表檢視
            </ToggleButton>
            <ToggleButton value="table">
              <TableChartIcon sx={{ mr: 0.5 }} />
              表格檢視
            </ToggleButton>
          </ToggleButtonGroup>
        </Box>
      </Box>

      {viewMode === 'summary' && renderSummaryView()}
      {viewMode === 'list' && renderListView()}
      {viewMode === 'table' && renderTableView()}
    </Box>
  );
};

export default ValidationErrorsEnhanced;
