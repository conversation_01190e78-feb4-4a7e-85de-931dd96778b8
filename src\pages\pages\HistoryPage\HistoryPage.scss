@use "../../../scss/common";

// History Page 樣式
.HistoryPage {
  // 統計資訊區塊
  &__stats {
    padding: 16px;
    margin-bottom: 16px;
    background-color: var(--color-surface-alt);
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    @media (max-width: 600px) {
      flex-direction: column;
      gap: 8px;
      align-items: flex-start;
    }
  }

  // 搜尋框區塊
  &__search {
    margin-bottom: 16px;
    
    .MuiTextField-root {
      background-color: var(--color-surface);
    }
  }

  // 分頁控制器
  &__pagination {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    margin-bottom: 16px;
    
    .MuiPagination-root {
      .MuiPaginationItem-root {
        &.Mui-selected {
          background-color: var(--color-primary);
          color: #fff;
          
          &:hover {
            background-color: var(--color-primary-hover);
          }
        }
      }
    }
  }

  // 展開區域樣式
  &__collapse-content {
    background-color: var(--color-surface-alt);
    padding: 16px;
    border-left: 3px solid var(--color-primary);
    
    .MuiListItem-root {
      transition: background-color 0.2s ease;
      border-radius: 4px;
      
      &:hover {
        background-color: var(--color-surface);
      }
    }
  }

  // 空狀態
  &__empty {
    text-align: center;
    padding: 48px 16px;
    
    svg {
      font-size: 4rem;
      color: var(--color-text-muted);
      opacity: 0.5;
      margin-bottom: 16px;
    }
  }

  // 記錄項目
  &__record-item {
    display: grid;
    gap: 8px;
    align-items: center;
    padding: 12px 16px;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr !important;
      
      .MuiListItemIcon-root {
        margin-bottom: 8px;
      }
    }
  }

  // 小型分頁控制器（展開內容中）
  &__small-pagination {
    display: flex;
    justify-content: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--color-border-light);
  }

  // 載入狀態
  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 48px;
  }
}





