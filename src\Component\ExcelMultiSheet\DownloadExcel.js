/* eslint-disable prefer-destructuring */
import React from "react";
import PropTypes from "prop-types";
import ReactExport from "react-data-export";

const ExcelFile = ReactExport.ExcelFile;
const ExcelSheet = ReactExport.ExcelFile.ExcelSheet;
const ExcelColumn = ReactExport.ExcelFile.ExcelColumn;

// 如果data的value帶有Array type，轉成字串
const checkDataValue = (tmpBookInfo, colNames) =>
  tmpBookInfo.map((el) => {
    const copyEl = JSON.parse(JSON.stringify(el));
    colNames.forEach((colName) => {
      if (Array.isArray(el[colName])) {
        copyEl[colName] = el[colName].join("、");
      }
    });
    return copyEl;
  });
/*
    因為xlsx內的子依賴cptable無法被storybook打包
    在各專案下暫時無法設定引用cptable自nmtl-ui的路徑
    因此只能將react-data-export整個移出nmtl-ui至各專案component底下使用
*/
const DownloadExcel = ({ data, element, fileName }) => {
  const xlsxCols = Object.keys((data && data[0]) || {}) || [];
  return (
    <ExcelFile element={element} filename={fileName}>
      <ExcelSheet data={checkDataValue(data, xlsxCols)} name="Sheet 1">
        {(xlsxCols || []).map((col) => (
          <ExcelColumn key={col} label={col} value={col} />
        ))}
      </ExcelSheet>
    </ExcelFile>
  );
};
DownloadExcel.defaultProps = {
  data: [],
  element: undefined,
  fileName: "Output",
};
DownloadExcel.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape({})),
  element: PropTypes.node,
  fileName: PropTypes.string,
};

export default DownloadExcel;
