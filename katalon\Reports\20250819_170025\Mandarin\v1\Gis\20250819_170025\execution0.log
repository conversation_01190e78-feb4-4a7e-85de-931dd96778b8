<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE log SYSTEM "logger.dtd">
<log>
<record>
  <date>2025-08-19T09:00:29.014724400Z</date>
  <millis>1755594029014</millis>
  <nanos>724400</nanos>
  <sequence>0</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startSuite</method>
  <thread>1</thread>
  <message>Start Test Suite : Test Suites/Mandarin/v1/Gis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="rerunTestFailImmediately">false</property>
  <property name="retryCount">0</property>
  <property name="name">Gis</property>
  <property name="description"></property>
  <property name="id">Test Suites/Mandarin/v1/Gis</property>
</record>
<record>
  <date>2025-08-19T09:00:29.039891200Z</date>
  <millis>1755594029039</millis>
  <nanos>891200</nanos>
  <sequence>1</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>userFullName = Lin Kai Yee</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="userFullName">Lin Kai Yee</property>
</record>
<record>
  <date>2025-08-19T09:00:29.040492700Z</date>
  <millis>1755594029040</millis>
  <nanos>492700</nanos>
  <sequence>2</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>projectName = katalon</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="projectName">katalon</property>
</record>
<record>
  <date>2025-08-19T09:00:29.041037400Z</date>
  <millis>1755594029041</millis>
  <nanos>37400</nanos>
  <sequence>3</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>hostName = saw63 - host.docker.internal</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="hostName">saw63 - host.docker.internal</property>
</record>
<record>
  <date>2025-08-19T09:00:29.041037400Z</date>
  <millis>1755594029041</millis>
  <nanos>37400</nanos>
  <sequence>4</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>os = Windows 11 64bit</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="os">Windows 11 64bit</property>
</record>
<record>
  <date>2025-08-19T09:00:29.041607400Z</date>
  <millis>1755594029041</millis>
  <nanos>607400</nanos>
  <sequence>5</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>hostAddress = ************</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="hostAddress">************</property>
</record>
<record>
  <date>2025-08-19T09:00:29.042942700Z</date>
  <millis>1755594029042</millis>
  <nanos>942700</nanos>
  <sequence>6</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>katalonVersion = ********</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="katalonVersion">********</property>
</record>
<record>
  <date>2025-08-19T09:00:29.056389100Z</date>
  <millis>1755594029056</millis>
  <nanos>389100</nanos>
  <sequence>7</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start listener action : beforeSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:29.074512600Z</date>
  <millis>1755594029074</millis>
  <nanos>512600</nanos>
  <sequence>9</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">29</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:00:29.372383400Z</date>
  <millis>1755594029372</millis>
  <nanos>383400</nanos>
  <sequence>11</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Starting &amp;apos;Chrome&amp;apos; driver</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:29.492202400Z</date>
  <millis>1755594029492</millis>
  <nanos>202400</nanos>
  <sequence>12</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Action delay is set to 0 milliseconds</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:31.402028700Z</date>
  <millis>1755594031402</millis>
  <nanos>28700</nanos>
  <sequence>14</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>sessionId = 278154ae94a1c906c4c9e64473cf3628</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="sessionId">278154ae94a1c906c4c9e64473cf3628</property>
</record>
<record>
  <date>2025-08-19T09:00:31.403030300Z</date>
  <millis>1755594031403</millis>
  <nanos>30300</nanos>
  <sequence>15</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>browser = Chrome 138.0.7204.185</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="browser">Chrome 138.0.7204.185</property>
</record>
<record>
  <date>2025-08-19T09:00:31.404029400Z</date>
  <millis>1755594031404</millis>
  <nanos>29400</nanos>
  <sequence>16</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>platform = Windows 11</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="platform">Windows 11</property>
</record>
<record>
  <date>2025-08-19T09:00:31.405032100Z</date>
  <millis>1755594031405</millis>
  <nanos>32100</nanos>
  <sequence>17</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>seleniumVersion = 4.28.1</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="seleniumVersion">4.28.1</property>
</record>
<record>
  <date>2025-08-19T09:00:31.415549600Z</date>
  <millis>1755594031415</millis>
  <nanos>549600</nanos>
  <sequence>18</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&amp;quot;&amp;quot;, isApplyToDesiredCapabilities=true }</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="proxyInformation">ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }</property>
</record>
<record>
  <date>2025-08-19T09:00:31.449915600Z</date>
  <millis>1755594031449</millis>
  <nanos>915600</nanos>
  <sequence>19</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is opened with url: &amp;apos;&amp;apos;</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.OpenBrowserKeyword.openBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T09:00:31.452465600Z</date>
  <millis>1755594031452</millis>
  <nanos>465600</nanos>
  <sequence>20</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:31.452465600Z</date>
  <millis>1755594031452</millis>
  <nanos>465600</nanos>
  <sequence>21</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : maximizeWindow()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T09:00:31.535450900Z</date>
  <millis>1755594031535</millis>
  <nanos>450900</nanos>
  <sequence>23</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Current window maximized</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.MaximizeWindowKeyword.maximizeWindow</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T09:00:31.536447700Z</date>
  <millis>1755594031536</millis>
  <nanos>447700</nanos>
  <sequence>25</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : maximizeWindow()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:31.537455800Z</date>
  <millis>1755594031537</millis>
  <nanos>455800</nanos>
  <sequence>26</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End listener action : beforeSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:31.977200200Z</date>
  <millis>1755594031977</millis>
  <nanos>200200</nanos>
  <sequence>27</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/GoToGis</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/GoToGis</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\GoToGis.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-19T09:00:32.048920500Z</date>
  <millis>1755594032048</millis>
  <nanos>920500</nanos>
  <sequence>29</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">20</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:00:32.054215200Z</date>
  <millis>1755594032054</millis>
  <nanos>215200</nanos>
  <sequence>31</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>A browser is already opened. Closing browser and opening a new one</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:32.261581Z</date>
  <millis>1755594032261</millis>
  <nanos>581000</nanos>
  <sequence>32</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Starting &amp;apos;Chrome&amp;apos; driver</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:32.263626600Z</date>
  <millis>1755594032263</millis>
  <nanos>626600</nanos>
  <sequence>33</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Action delay is set to 0 milliseconds</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:33.497702800Z</date>
  <millis>1755594033497</millis>
  <nanos>702800</nanos>
  <sequence>35</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>sessionId = f4889763a3d355dde04b3373e74a525e</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="sessionId">f4889763a3d355dde04b3373e74a525e</property>
</record>
<record>
  <date>2025-08-19T09:00:33.498700200Z</date>
  <millis>1755594033498</millis>
  <nanos>700200</nanos>
  <sequence>36</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>browser = Chrome 138.0.7204.185</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="browser">Chrome 138.0.7204.185</property>
</record>
<record>
  <date>2025-08-19T09:00:33.499699700Z</date>
  <millis>1755594033499</millis>
  <nanos>699700</nanos>
  <sequence>37</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>platform = Windows 11</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="platform">Windows 11</property>
</record>
<record>
  <date>2025-08-19T09:00:33.499699700Z</date>
  <millis>1755594033499</millis>
  <nanos>699700</nanos>
  <sequence>38</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>seleniumVersion = 4.28.1</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="seleniumVersion">4.28.1</property>
</record>
<record>
  <date>2025-08-19T09:00:33.501700500Z</date>
  <millis>1755594033501</millis>
  <nanos>700500</nanos>
  <sequence>39</sequence>
  <level>RUN_DATA</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&amp;quot;&amp;quot;, isApplyToDesiredCapabilities=true }</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="proxyInformation">ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }</property>
</record>
<record>
  <date>2025-08-19T09:00:33.526797300Z</date>
  <millis>1755594033526</millis>
  <nanos>797300</nanos>
  <sequence>40</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is opened with url: &amp;apos;&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.OpenBrowserKeyword.openBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T09:00:33.529832400Z</date>
  <millis>1755594033529</millis>
  <nanos>832400</nanos>
  <sequence>41</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : openBrowser(&amp;quot;&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:33.529832400Z</date>
  <millis>1755594033529</millis>
  <nanos>832400</nanos>
  <sequence>42</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : navigateToUrl(&amp;quot;https://land2.daoyidh.com/Gis&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T09:00:33.964789800Z</date>
  <millis>1755594033964</millis>
  <nanos>789800</nanos>
  <sequence>45</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Navigate to &amp;apos;https://land2.daoyidh.com/Gis&amp;apos; successfully</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.NavigateToUrlKeyword.navigateToUrl</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T09:00:33.966202500Z</date>
  <millis>1755594033966</millis>
  <nanos>202500</nanos>
  <sequence>46</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : navigateToUrl(&amp;quot;https://land2.daoyidh.com/Gis&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:33.967206200Z</date>
  <millis>1755594033967</millis>
  <nanos>206200</nanos>
  <sequence>47</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:33.968205100Z</date>
  <millis>1755594033968</millis>
  <nanos>205100</nanos>
  <sequence>48</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/GoToGis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-19T09:00:33.980449200Z</date>
  <millis>1755594033980</millis>
  <nanos>449200</nanos>
  <sequence>49</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</property>
  <property name="description">Login</property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\LoginWithEmail.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-19T09:00:34.097402Z</date>
  <millis>1755594034097</millis>
  <nanos>402000</nanos>
  <sequence>51</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">20</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:00:35.610213100Z</date>
  <millis>1755594035610</millis>
  <nanos>213100</nanos>
  <sequence>61</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T09:00:35.614222200Z</date>
  <millis>1755594035614</millis>
  <nanos>222200</nanos>
  <sequence>62</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:35.615729200Z</date>
  <millis>1755594035615</millis>
  <nanos>729200</nanos>
  <sequence>63</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T09:00:36.125972700Z</date>
  <millis>1755594036125</millis>
  <nanos>972700</nanos>
  <sequence>73</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T09:00:36.128982700Z</date>
  <millis>1755594036128</millis>
  <nanos>982700</nanos>
  <sequence>74</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:36.128982700Z</date>
  <millis>1755594036128</millis>
  <nanos>982700</nanos>
  <sequence>75</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;quot;), &amp;quot;<EMAIL>&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">24</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-19T09:00:36.964745500Z</date>
  <millis>1755594036964</millis>
  <nanos>745500</nanos>
  <sequence>91</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;<EMAIL>&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T09:00:36.965867200Z</date>
  <millis>1755594036965</millis>
  <nanos>867200</nanos>
  <sequence>92</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Email_email&amp;quot;), &amp;quot;<EMAIL>&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:36.967379100Z</date>
  <millis>1755594036967</millis>
  <nanos>379100</nanos>
  <sequence>93</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">26</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-19T09:00:37.268276400Z</date>
  <millis>1755594037268</millis>
  <nanos>276400</nanos>
  <sequence>103</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T09:00:37.270278200Z</date>
  <millis>1755594037270</millis>
  <nanos>278200</nanos>
  <sequence>104</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Next&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:37.271279500Z</date>
  <millis>1755594037271</millis>
  <nanos>279500</nanos>
  <sequence>105</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setEncryptedText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;quot;), &amp;quot;0D41p6Ct1VJ35FCiyaw3Gw==&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">28</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-19T09:00:38.219079300Z</date>
  <millis>1755594038219</millis>
  <nanos>79300</nanos>
  <sequence>116</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text ****** has been set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetEncryptedTextKeyword.setEncryptedText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T09:00:38.220077500Z</date>
  <millis>1755594038220</millis>
  <nanos>77500</nanos>
  <sequence>117</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setEncryptedText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input_Password_password&amp;quot;), &amp;quot;0D41p6Ct1VJ35FCiyaw3Gw==&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:38.220077500Z</date>
  <millis>1755594038220</millis>
  <nanos>77500</nanos>
  <sequence>118</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">30</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-19T09:00:38.522081Z</date>
  <millis>1755594038522</millis>
  <nanos>81000</nanos>
  <sequence>128</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T09:00:38.522081Z</date>
  <millis>1755594038522</millis>
  <nanos>81000</nanos>
  <sequence>129</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_Sign In&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:38.523089800Z</date>
  <millis>1755594038523</millis>
  <nanos>89800</nanos>
  <sequence>130</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:38.523089800Z</date>
  <millis>1755594038523</millis>
  <nanos>89800</nanos>
  <sequence>131</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/LoginWithEmail</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-19T09:00:38.525591100Z</date>
  <millis>1755594038525</millis>
  <nanos>591100</nanos>
  <sequence>132</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\SearchTextInGis.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-19T09:00:38.568071300Z</date>
  <millis>1755594038568</millis>
  <nanos>71300</nanos>
  <sequence>134</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button__1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">22</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:00:41.238700200Z</date>
  <millis>1755594041238</millis>
  <nanos>700200</nanos>
  <sequence>144</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Object: &amp;apos;Object Repository/Mandarin/v1_web/Gis/button__1&amp;apos; is clicked on</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T09:00:41.241705300Z</date>
  <millis>1755594041241</millis>
  <nanos>705300</nanos>
  <sequence>145</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button__1&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:41.242700500Z</date>
  <millis>1755594041242</millis>
  <nanos>700500</nanos>
  <sequence>146</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;quot;), &amp;quot;新北勢&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">25</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T09:00:41.982260500Z</date>
  <millis>1755594041982</millis>
  <nanos>260500</nanos>
  <sequence>158</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Text &amp;apos;新北勢&amp;apos; is set on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;apos;</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T09:00:41.983260100Z</date>
  <millis>1755594041983</millis>
  <nanos>260100</nanos>
  <sequence>159</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/input__mui-3&amp;quot;), &amp;quot;新北勢&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:41.984258800Z</date>
  <millis>1755594041984</millis>
  <nanos>258800</nanos>
  <sequence>160</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:41.985261100Z</date>
  <millis>1755594041985</millis>
  <nanos>261100</nanos>
  <sequence>161</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/SearchTextInGis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-19T09:00:41.989322700Z</date>
  <millis>1755594041989</millis>
  <nanos>322700</nanos>
  <sequence>162</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\ChoosePerPage.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-19T09:00:42.247793300Z</date>
  <millis>1755594042247</millis>
  <nanos>793300</nanos>
  <sequence>164</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : try</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">56</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:00:42.249784600Z</date>
  <millis>1755594042249</millis>
  <nanos>784600</nanos>
  <sequence>165</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : waitForPageLoad(10)</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">58</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:00:42.354027700Z</date>
  <millis>1755594042354</millis>
  <nanos>27700</nanos>
  <sequence>168</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Wait for page load successfully</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call</property>
  <property name="testops-execution-stacktrace">com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:324)
com.kms.katalon.core.logging.KeywordLogger.logPassed(KeywordLogger.java:319)
com.kms.katalon.core.logging.KeywordLogger$logPassed$0.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForPageLoadKeyword$_waitForPageLoad_closure1.doCall(WaitForPageLoadKeyword.groovy:94)
com.kms.katalon.core.webui.keyword.builtin.WaitForPageLoadKeyword$_waitForPageLoad_closure1.doCall(WaitForPageLoadKeyword.groovy)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
com.kms.katalon.core.webui.keyword.builtin.WaitForPageLoadKeyword.waitForPageLoad(WaitForPageLoadKeyword.groovy:95)
com.kms.katalon.core.webui.keyword.builtin.WaitForPageLoadKeyword.execute(WaitForPageLoadKeyword.groovy:68)
com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForPageLoad(WebUiBuiltInKeywords.groovy:2533)
ChoosePerPage.run(ChoosePerPage:58)
</property>
</record>
<record>
  <date>2025-08-19T09:00:42.355574600Z</date>
  <millis>1755594042355</millis>
  <nanos>574600</nanos>
  <sequence>169</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : waitForPageLoad(10)</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:42.355574600Z</date>
  <millis>1755594042355</millis>
  <nanos>574600</nanos>
  <sequence>170</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">61</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T09:00:42.362623100Z</date>
  <millis>1755594042362</millis>
  <nanos>623100</nanos>
  <sequence>171</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:42.362623100Z</date>
  <millis>1755594042362</millis>
  <nanos>623100</nanos>
  <sequence>172</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : selectors = [&amp;quot;div[role=&amp;quot;combobox&amp;quot;]&amp;quot;, &amp;quot;[role=&amp;quot;combobox&amp;quot;]&amp;quot;, &amp;quot;div[aria-haspopup=&amp;quot;listbox&amp;quot;]&amp;quot;, &amp;quot;.MuiSelect-root&amp;quot;, &amp;quot;div.MuiInputBase-root[role=&amp;quot;combobox&amp;quot;]&amp;quot;]</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">64</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-19T09:00:42.364631700Z</date>
  <millis>1755594042364</millis>
  <nanos>631700</nanos>
  <sequence>173</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : selectors = [&amp;quot;div[role=&amp;quot;combobox&amp;quot;]&amp;quot;, &amp;quot;[role=&amp;quot;combobox&amp;quot;]&amp;quot;, &amp;quot;div[aria-haspopup=&amp;quot;listbox&amp;quot;]&amp;quot;, &amp;quot;.MuiSelect-root&amp;quot;, &amp;quot;div.MuiInputBase-root[role=&amp;quot;combobox&amp;quot;]&amp;quot;]</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:42.364631700Z</date>
  <millis>1755594042364</millis>
  <nanos>631700</nanos>
  <sequence>174</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownFound = false</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">72</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-19T09:00:42.366171900Z</date>
  <millis>1755594042366</millis>
  <nanos>171900</nanos>
  <sequence>175</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownFound = false</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:42.366171900Z</date>
  <millis>1755594042366</millis>
  <nanos>171900</nanos>
  <sequence>176</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : for (String selector : selectors)</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">74</property>
  <property name="stepIndex">5</property>
</record>
<record>
  <date>2025-08-19T09:00:42.367211100Z</date>
  <millis>1755594042367</millis>
  <nanos>211100</nanos>
  <sequence>177</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : try</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">75</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:00:42.370118Z</date>
  <millis>1755594042370</millis>
  <nanos>118000</nanos>
  <sequence>178</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, selector)</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">76</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:00:42.376665700Z</date>
  <millis>1755594042376</millis>
  <nanos>665700</nanos>
  <sequence>179</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, selector)</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:42.376665700Z</date>
  <millis>1755594042376</millis>
  <nanos>665700</nanos>
  <sequence>180</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : if (waitForElementVisible(dropdownButton, 10))</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">79</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T09:00:53.496492500Z</date>
  <millis>1755594053496</millis>
  <nanos>492500</nanos>
  <sequence>232</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Unable to find the element located by &amp;apos;By.cssSelector: div[role=&amp;quot;combobox&amp;quot;]&amp;apos;. Please recheck the objects properties to make sure the desired element is located. </message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:53.497998900Z</date>
  <millis>1755594053497</millis>
  <nanos>998900</nanos>
  <sequence>233</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Failed to find element with id &amp;apos;&amp;apos;. Try using Self-healing.</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:53.508439700Z</date>
  <millis>1755594053508</millis>
  <nanos>439700</nanos>
  <sequence>235</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Index 0 out of bounds for length 0</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IndexOutOfBoundsException</property>
  <property name="failed.exception.stacktrace">java.lang.IndexOutOfBoundsException: Index 0 out of bounds for length 0
	at java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:64)
	at java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:70)
	at java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:266)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:531)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:955)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:843)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Index 0 out of bounds for length 0</property>
</record>
<record>
  <date>2025-08-19T09:00:53.509440400Z</date>
  <millis>1755594053509</millis>
  <nanos>440400</nanos>
  <sequence>237</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Cannot find elements when the selector is null</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IllegalArgumentException</property>
  <property name="failed.exception.stacktrace">java.lang.IllegalArgumentException: Cannot find elements when the selector is null
	at org.openqa.selenium.internal.Require$ArgumentChecker.nonNull(Require.java:211)
	at org.openqa.selenium.By$ByCssSelector.&lt;init&gt;(By.java:328)
	at org.openqa.selenium.By.cssSelector(By.java:112)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:527)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:951)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:846)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Cannot find elements when the selector is null</property>
</record>
<record>
  <date>2025-08-19T09:00:53.517381200Z</date>
  <millis>1755594053517</millis>
  <nanos>381200</nanos>
  <sequence>239</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Web element with id: &amp;apos;&amp;apos; located by &amp;apos;div[role=&amp;quot;combobox&amp;quot;]&amp;apos; not found</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">com.kms.katalon.core.webui.exception.WebElementNotFoundException</property>
  <property name="failed.exception.stacktrace">com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found</property>
</record>
<record>
  <date>2025-08-19T09:00:53.523895700Z</date>
  <millis>1755594053523</millis>
  <nanos>895700</nanos>
  <sequence>240</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : if (waitForElementVisible(dropdownButton, 10))</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:53.523895700Z</date>
  <millis>1755594053523</millis>
  <nanos>895700</nanos>
  <sequence>241</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : try</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:53.523895700Z</date>
  <millis>1755594053523</millis>
  <nanos>895700</nanos>
  <sequence>242</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : try</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">75</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:00:53.524896700Z</date>
  <millis>1755594053524</millis>
  <nanos>896700</nanos>
  <sequence>243</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, selector)</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">76</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:00:53.524896700Z</date>
  <millis>1755594053524</millis>
  <nanos>896700</nanos>
  <sequence>244</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, selector)</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:00:53.525896Z</date>
  <millis>1755594053525</millis>
  <nanos>896000</nanos>
  <sequence>245</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : if (waitForElementVisible(dropdownButton, 10))</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">79</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T09:01:04.571000900Z</date>
  <millis>1755594064571</millis>
  <nanos>900</nanos>
  <sequence>297</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Unable to find the element located by &amp;apos;By.cssSelector: div[role=&amp;quot;combobox&amp;quot;]&amp;apos;. Please recheck the objects properties to make sure the desired element is located. </message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:04.572001700Z</date>
  <millis>1755594064572</millis>
  <nanos>1700</nanos>
  <sequence>298</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Failed to find element with id &amp;apos;&amp;apos;. Try using Self-healing.</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:04.575001300Z</date>
  <millis>1755594064575</millis>
  <nanos>1300</nanos>
  <sequence>300</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Index 0 out of bounds for length 0</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IndexOutOfBoundsException</property>
  <property name="failed.exception.stacktrace">java.lang.IndexOutOfBoundsException: Index 0 out of bounds for length 0
	at java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:64)
	at java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:70)
	at java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:266)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:531)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:955)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:843)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Index 0 out of bounds for length 0</property>
</record>
<record>
  <date>2025-08-19T09:01:04.575001300Z</date>
  <millis>1755594064575</millis>
  <nanos>1300</nanos>
  <sequence>302</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Cannot find elements when the selector is null</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IllegalArgumentException</property>
  <property name="failed.exception.stacktrace">java.lang.IllegalArgumentException: Cannot find elements when the selector is null
	at org.openqa.selenium.internal.Require$ArgumentChecker.nonNull(Require.java:211)
	at org.openqa.selenium.By$ByCssSelector.&lt;init&gt;(By.java:328)
	at org.openqa.selenium.By.cssSelector(By.java:112)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:527)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:951)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:846)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Cannot find elements when the selector is null</property>
</record>
<record>
  <date>2025-08-19T09:01:04.576000700Z</date>
  <millis>1755594064576</millis>
  <nanos>700</nanos>
  <sequence>304</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Web element with id: &amp;apos;&amp;apos; located by &amp;apos;div[role=&amp;quot;combobox&amp;quot;]&amp;apos; not found</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">com.kms.katalon.core.webui.exception.WebElementNotFoundException</property>
  <property name="failed.exception.stacktrace">com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found</property>
</record>
<record>
  <date>2025-08-19T09:01:04.577001400Z</date>
  <millis>1755594064577</millis>
  <nanos>1400</nanos>
  <sequence>305</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : if (waitForElementVisible(dropdownButton, 10))</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:04.577001400Z</date>
  <millis>1755594064577</millis>
  <nanos>1400</nanos>
  <sequence>306</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : try</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:04.577001400Z</date>
  <millis>1755594064577</millis>
  <nanos>1400</nanos>
  <sequence>307</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : try</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">75</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:01:04.577001400Z</date>
  <millis>1755594064577</millis>
  <nanos>1400</nanos>
  <sequence>308</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, selector)</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">76</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:01:04.578001500Z</date>
  <millis>1755594064578</millis>
  <nanos>1500</nanos>
  <sequence>309</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, selector)</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:04.578001500Z</date>
  <millis>1755594064578</millis>
  <nanos>1500</nanos>
  <sequence>310</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : if (waitForElementVisible(dropdownButton, 10))</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">79</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T09:01:15.822906500Z</date>
  <millis>1755594075822</millis>
  <nanos>906500</nanos>
  <sequence>363</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Unable to find the element located by &amp;apos;By.cssSelector: div[role=&amp;quot;combobox&amp;quot;]&amp;apos;. Please recheck the objects properties to make sure the desired element is located. </message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:15.823559800Z</date>
  <millis>1755594075823</millis>
  <nanos>559800</nanos>
  <sequence>364</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Failed to find element with id &amp;apos;&amp;apos;. Try using Self-healing.</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:15.825894Z</date>
  <millis>1755594075825</millis>
  <nanos>894000</nanos>
  <sequence>366</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Index 0 out of bounds for length 0</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IndexOutOfBoundsException</property>
  <property name="failed.exception.stacktrace">java.lang.IndexOutOfBoundsException: Index 0 out of bounds for length 0
	at java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:64)
	at java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:70)
	at java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:266)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:531)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:955)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:843)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Index 0 out of bounds for length 0</property>
</record>
<record>
  <date>2025-08-19T09:01:15.826476300Z</date>
  <millis>1755594075826</millis>
  <nanos>476300</nanos>
  <sequence>368</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Cannot find elements when the selector is null</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IllegalArgumentException</property>
  <property name="failed.exception.stacktrace">java.lang.IllegalArgumentException: Cannot find elements when the selector is null
	at org.openqa.selenium.internal.Require$ArgumentChecker.nonNull(Require.java:211)
	at org.openqa.selenium.By$ByCssSelector.&lt;init&gt;(By.java:328)
	at org.openqa.selenium.By.cssSelector(By.java:112)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:527)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:951)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:846)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Cannot find elements when the selector is null</property>
</record>
<record>
  <date>2025-08-19T09:01:15.827039100Z</date>
  <millis>1755594075827</millis>
  <nanos>39100</nanos>
  <sequence>370</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Web element with id: &amp;apos;&amp;apos; located by &amp;apos;div[role=&amp;quot;combobox&amp;quot;]&amp;apos; not found</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">com.kms.katalon.core.webui.exception.WebElementNotFoundException</property>
  <property name="failed.exception.stacktrace">com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found</property>
</record>
<record>
  <date>2025-08-19T09:01:15.827039100Z</date>
  <millis>1755594075827</millis>
  <nanos>39100</nanos>
  <sequence>371</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : if (waitForElementVisible(dropdownButton, 10))</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:15.827621500Z</date>
  <millis>1755594075827</millis>
  <nanos>621500</nanos>
  <sequence>372</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : try</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:15.827621500Z</date>
  <millis>1755594075827</millis>
  <nanos>621500</nanos>
  <sequence>373</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : try</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">75</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:01:15.827621500Z</date>
  <millis>1755594075827</millis>
  <nanos>621500</nanos>
  <sequence>374</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, selector)</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">76</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:01:15.827621500Z</date>
  <millis>1755594075827</millis>
  <nanos>621500</nanos>
  <sequence>375</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, selector)</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:15.828155900Z</date>
  <millis>1755594075828</millis>
  <nanos>155900</nanos>
  <sequence>376</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : if (waitForElementVisible(dropdownButton, 10))</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">79</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T09:01:26.895739Z</date>
  <millis>1755594086895</millis>
  <nanos>739000</nanos>
  <sequence>427</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Unable to find the element located by &amp;apos;By.cssSelector: div[role=&amp;quot;combobox&amp;quot;]&amp;apos;. Please recheck the objects properties to make sure the desired element is located. </message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:26.895739Z</date>
  <millis>1755594086895</millis>
  <nanos>739000</nanos>
  <sequence>428</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Failed to find element with id &amp;apos;&amp;apos;. Try using Self-healing.</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:26.898250300Z</date>
  <millis>1755594086898</millis>
  <nanos>250300</nanos>
  <sequence>430</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Index 0 out of bounds for length 0</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IndexOutOfBoundsException</property>
  <property name="failed.exception.stacktrace">java.lang.IndexOutOfBoundsException: Index 0 out of bounds for length 0
	at java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:64)
	at java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:70)
	at java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:266)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:531)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:955)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:843)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Index 0 out of bounds for length 0</property>
</record>
<record>
  <date>2025-08-19T09:01:26.899258Z</date>
  <millis>1755594086899</millis>
  <nanos>258000</nanos>
  <sequence>432</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Cannot find elements when the selector is null</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IllegalArgumentException</property>
  <property name="failed.exception.stacktrace">java.lang.IllegalArgumentException: Cannot find elements when the selector is null
	at org.openqa.selenium.internal.Require$ArgumentChecker.nonNull(Require.java:211)
	at org.openqa.selenium.By$ByCssSelector.&lt;init&gt;(By.java:328)
	at org.openqa.selenium.By.cssSelector(By.java:112)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:527)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:951)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:846)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Cannot find elements when the selector is null</property>
</record>
<record>
  <date>2025-08-19T09:01:26.899258Z</date>
  <millis>1755594086899</millis>
  <nanos>258000</nanos>
  <sequence>434</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Web element with id: &amp;apos;&amp;apos; located by &amp;apos;div[role=&amp;quot;combobox&amp;quot;]&amp;apos; not found</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">com.kms.katalon.core.webui.exception.WebElementNotFoundException</property>
  <property name="failed.exception.stacktrace">com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found</property>
</record>
<record>
  <date>2025-08-19T09:01:26.901154400Z</date>
  <millis>1755594086901</millis>
  <nanos>154400</nanos>
  <sequence>435</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : if (waitForElementVisible(dropdownButton, 10))</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:26.901154400Z</date>
  <millis>1755594086901</millis>
  <nanos>154400</nanos>
  <sequence>436</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : try</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:26.901154400Z</date>
  <millis>1755594086901</millis>
  <nanos>154400</nanos>
  <sequence>437</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : try</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">75</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:01:26.902160300Z</date>
  <millis>1755594086902</millis>
  <nanos>160300</nanos>
  <sequence>438</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, selector)</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">76</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:01:26.903161700Z</date>
  <millis>1755594086903</millis>
  <nanos>161700</nanos>
  <sequence>439</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownButton.addProperty(&amp;quot;css&amp;quot;, EQUALS, selector)</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:26.903161700Z</date>
  <millis>1755594086903</millis>
  <nanos>161700</nanos>
  <sequence>440</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : if (waitForElementVisible(dropdownButton, 10))</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">79</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T09:01:38.108553600Z</date>
  <millis>1755594098108</millis>
  <nanos>553600</nanos>
  <sequence>493</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Unable to find the element located by &amp;apos;By.cssSelector: div[role=&amp;quot;combobox&amp;quot;]&amp;apos;. Please recheck the objects properties to make sure the desired element is located. </message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:38.108553600Z</date>
  <millis>1755594098108</millis>
  <nanos>553600</nanos>
  <sequence>494</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Failed to find element with id &amp;apos;&amp;apos;. Try using Self-healing.</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:38.110554400Z</date>
  <millis>1755594098110</millis>
  <nanos>554400</nanos>
  <sequence>496</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Index 0 out of bounds for length 0</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IndexOutOfBoundsException</property>
  <property name="failed.exception.stacktrace">java.lang.IndexOutOfBoundsException: Index 0 out of bounds for length 0
	at java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:64)
	at java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:70)
	at java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:266)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:531)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:955)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:843)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Index 0 out of bounds for length 0</property>
</record>
<record>
  <date>2025-08-19T09:01:38.111553800Z</date>
  <millis>1755594098111</millis>
  <nanos>553800</nanos>
  <sequence>498</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Cannot find elements when the selector is null</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IllegalArgumentException</property>
  <property name="failed.exception.stacktrace">java.lang.IllegalArgumentException: Cannot find elements when the selector is null
	at org.openqa.selenium.internal.Require$ArgumentChecker.nonNull(Require.java:211)
	at org.openqa.selenium.By$ByCssSelector.&lt;init&gt;(By.java:328)
	at org.openqa.selenium.By.cssSelector(By.java:112)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:527)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:951)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:846)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Cannot find elements when the selector is null</property>
</record>
<record>
  <date>2025-08-19T09:01:38.113553800Z</date>
  <millis>1755594098113</millis>
  <nanos>553800</nanos>
  <sequence>500</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Web element with id: &amp;apos;&amp;apos; located by &amp;apos;div[role=&amp;quot;combobox&amp;quot;]&amp;apos; not found</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">com.kms.katalon.core.webui.exception.WebElementNotFoundException</property>
  <property name="failed.exception.stacktrace">com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:79)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Web element with id: &apos;&apos; located by &apos;div[role=&quot;combobox&quot;]&apos; not found</property>
</record>
<record>
  <date>2025-08-19T09:01:38.117554100Z</date>
  <millis>1755594098117</millis>
  <nanos>554100</nanos>
  <sequence>501</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : if (waitForElementVisible(dropdownButton, 10))</message>
  <nestedLevel>4</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:38.117554100Z</date>
  <millis>1755594098117</millis>
  <nanos>554100</nanos>
  <sequence>502</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : try</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:38.117554100Z</date>
  <millis>1755594098117</millis>
  <nanos>554100</nanos>
  <sequence>503</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : for (String selector : selectors)</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:38.117554100Z</date>
  <millis>1755594098117</millis>
  <nanos>554100</nanos>
  <sequence>504</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : if (!(dropdownFound))</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">97</property>
  <property name="stepIndex">6</property>
</record>
<record>
  <date>2025-08-19T09:01:38.119059800Z</date>
  <millis>1755594098119</millis>
  <nanos>59800</nanos>
  <sequence>505</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : throw new java.lang.Exception(無法找到下拉選單按鈕)</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">98</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:01:38.122059700Z</date>
  <millis>1755594098122</millis>
  <nanos>59700</nanos>
  <sequence>506</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : throw new java.lang.Exception(無法找到下拉選單按鈕)</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:38.123059400Z</date>
  <millis>1755594098123</millis>
  <nanos>59400</nanos>
  <sequence>507</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : if (!(dropdownFound))</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:38.123059400Z</date>
  <millis>1755594098123</millis>
  <nanos>59400</nanos>
  <sequence>508</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : try</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:38.123059400Z</date>
  <millis>1755594098123</millis>
  <nanos>59400</nanos>
  <sequence>509</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : catch (Exception e)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">153</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T09:01:38.124059100Z</date>
  <millis>1755594098124</millis>
  <nanos>59100</nanos>
  <sequence>510</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : println(選擇每頁50筆失敗: $e.getMessage())</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">154</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:01:38.151291800Z</date>
  <millis>1755594098151</millis>
  <nanos>291800</nanos>
  <sequence>511</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : println(選擇每頁50筆失敗: $e.getMessage())</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:38.151291800Z</date>
  <millis>1755594098151</millis>
  <nanos>291800</nanos>
  <sequence>512</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : try</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">157</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T09:01:38.152290300Z</date>
  <millis>1755594098152</millis>
  <nanos>290300</nanos>
  <sequence>513</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : println(&amp;quot;嘗試備用方案...&amp;quot;)</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">158</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:01:38.155292Z</date>
  <millis>1755594098155</millis>
  <nanos>292000</nanos>
  <sequence>514</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : println(&amp;quot;嘗試備用方案...&amp;quot;)</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:38.155292Z</date>
  <millis>1755594098155</millis>
  <nanos>292000</nanos>
  <sequence>515</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownBackup = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">160</property>
  <property name="stepIndex">2</property>
</record>
<record>
  <date>2025-08-19T09:01:38.157292800Z</date>
  <millis>1755594098157</millis>
  <nanos>292800</nanos>
  <sequence>516</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownBackup = new com.kms.katalon.core.testobject.TestObject()</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:38.157292800Z</date>
  <millis>1755594098157</millis>
  <nanos>292800</nanos>
  <sequence>517</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : dropdownBackup.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;[role=&amp;quot;combobox&amp;quot;]&amp;quot;)</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">161</property>
  <property name="stepIndex">3</property>
</record>
<record>
  <date>2025-08-19T09:01:38.158391700Z</date>
  <millis>1755594098158</millis>
  <nanos>391700</nanos>
  <sequence>518</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : dropdownBackup.addProperty(&amp;quot;css&amp;quot;, EQUALS, &amp;quot;[role=&amp;quot;combobox&amp;quot;]&amp;quot;)</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:38.158391700Z</date>
  <millis>1755594098158</millis>
  <nanos>391700</nanos>
  <sequence>519</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : if (waitForElementVisible(dropdownBackup, 5))</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">163</property>
  <property name="stepIndex">4</property>
</record>
<record>
  <date>2025-08-19T09:01:44.352066800Z</date>
  <millis>1755594104352</millis>
  <nanos>66800</nanos>
  <sequence>551</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Unable to find the element located by &amp;apos;By.cssSelector: [role=&amp;quot;combobox&amp;quot;]&amp;apos;. Please recheck the objects properties to make sure the desired element is located. </message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:44.354455100Z</date>
  <millis>1755594104354</millis>
  <nanos>455100</nanos>
  <sequence>552</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Failed to find element with id &amp;apos;&amp;apos;. Try using Self-healing.</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:44.357535Z</date>
  <millis>1755594104357</millis>
  <nanos>535000</nanos>
  <sequence>554</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Index 0 out of bounds for length 0</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IndexOutOfBoundsException</property>
  <property name="failed.exception.stacktrace">java.lang.IndexOutOfBoundsException: Index 0 out of bounds for length 0
	at java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:64)
	at java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:70)
	at java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:266)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:531)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:955)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:843)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:163)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Index 0 out of bounds for length 0</property>
</record>
<record>
  <date>2025-08-19T09:01:44.358534300Z</date>
  <millis>1755594104358</millis>
  <nanos>534300</nanos>
  <sequence>556</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Cannot find elements when the selector is null</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">java.lang.IllegalArgumentException</property>
  <property name="failed.exception.stacktrace">java.lang.IllegalArgumentException: Cannot find elements when the selector is null
	at org.openqa.selenium.internal.Require$ArgumentChecker.nonNull(Require.java:211)
	at org.openqa.selenium.By$ByCssSelector.&lt;init&gt;(By.java:328)
	at org.openqa.selenium.By.cssSelector(By.java:112)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.buildLocator(WebUiCommonHelper.java:527)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementByNormalMethods(WebUiCommonHelper.java:1050)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsBySelectedMethod(WebUiCommonHelper.java:951)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findElementsWithSelfHealing(WebUiCommonHelper.java:846)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElements(WebUiCommonHelper.java:758)
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1448)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:163)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Cannot find elements when the selector is null</property>
</record>
<record>
  <date>2025-08-19T09:01:44.360053600Z</date>
  <millis>1755594104360</millis>
  <nanos>53600</nanos>
  <sequence>558</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Web element with id: &amp;apos;&amp;apos; located by &amp;apos;[role=&amp;quot;combobox&amp;quot;]&amp;apos; not found</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">com.kms.katalon.core.webui.exception.WebElementNotFoundException</property>
  <property name="failed.exception.stacktrace">com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &apos;&apos; located by &apos;[role=&quot;combobox&quot;]&apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword$findWebElement.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy:80)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword$_waitForElementVisible_closure1.doCall(WaitForElementVisibleKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain$runKeyword.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.waitForElementVisible(WaitForElementVisibleKeyword.groovy:99)
	at com.kms.katalon.core.webui.keyword.builtin.WaitForElementVisibleKeyword.execute(WaitForElementVisibleKeyword.groovy:69)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.waitForElementVisible(WebUiBuiltInKeywords.groovy:514)
	at ChoosePerPage.run(ChoosePerPage:163)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Web element with id: &apos;&apos; located by &apos;[role=&quot;combobox&quot;]&apos; not found</property>
</record>
<record>
  <date>2025-08-19T09:01:44.360053600Z</date>
  <millis>1755594104360</millis>
  <nanos>53600</nanos>
  <sequence>559</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : if (waitForElementVisible(dropdownBackup, 5))</message>
  <nestedLevel>3</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:44.361062500Z</date>
  <millis>1755594104361</millis>
  <nanos>62500</nanos>
  <sequence>560</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : try</message>
  <nestedLevel>2</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:44.361062500Z</date>
  <millis>1755594104361</millis>
  <nanos>62500</nanos>
  <sequence>561</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : catch (Exception e)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:44.361062500Z</date>
  <millis>1755594104361</millis>
  <nanos>62500</nanos>
  <sequence>562</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:01:44.364686300Z</date>
  <millis>1755594104364</millis>
  <nanos>686300</nanos>
  <sequence>563</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/ChoosePerPage</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-19T09:01:44.371441900Z</date>
  <millis>1755594104371</millis>
  <nanos>441900</nanos>
  <sequence>564</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/EditCoordinates</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/EditCoordinates</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/EditCoordinates</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\EditCoordinates.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-19T09:01:44.480183300Z</date>
  <millis>1755594104480</millis>
  <nanos>183300</nanos>
  <sequence>566</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">21</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:02:15.532419800Z</date>
  <millis>1755594135532</millis>
  <nanos>419800</nanos>
  <sequence>572</sequence>
  <level>WARNING</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Failed to find element with id &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;apos;. Try using Self-healing.</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:02:16.726031100Z</date>
  <millis>1755594136726</millis>
  <nanos>31100</nanos>
  <sequence>577</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Unable to find the element located by &amp;apos;By.xpath: //input[@id=&amp;apos;mui-359&amp;apos;]&amp;apos;. Please recheck the objects properties to make sure the desired element is located. </message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:02:17.397900500Z</date>
  <millis>1755594137397</millis>
  <nanos>900500</nanos>
  <sequence>579</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Failed Locator: &amp;apos;//input[@id=&amp;apos;mui-359&amp;apos;]&amp;apos;.</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:02:18.305047100Z</date>
  <millis>1755594138305</millis>
  <nanos>47100</nanos>
  <sequence>581</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Failed Locator: &amp;apos;//div[@id=&amp;apos;root&amp;apos;]/div/div[2]/div/div[3]/div/table/tbody/tr[49]/td[3]/div/div/input&amp;apos;.</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:02:18.603655600Z</date>
  <millis>1755594138603</millis>
  <nanos>655600</nanos>
  <sequence>583</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Failed Locator: &amp;apos;//tr[49]/td[3]/div/div/input&amp;apos;.</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:02:18.887576800Z</date>
  <millis>1755594138887</millis>
  <nanos>576800</nanos>
  <sequence>585</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>[SELF-HEALING] Failed Locator: &amp;apos;//input[@placeholder = &amp;apos;請輸入經度 (TWD97)&amp;apos; and @type = &amp;apos;number&amp;apos; and @id = &amp;apos;mui-359&amp;apos;]&amp;apos;.</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:02:19.910935200Z</date>
  <millis>1755594139910</millis>
  <nanos>935200</nanos>
  <sequence>591</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Unable to find the element located by &amp;apos;By.xpath: //*[@placeholder = &amp;apos;請輸入經度 (TWD97)&amp;apos; and @type = &amp;apos;number&amp;apos; and @id = &amp;apos;mui-359&amp;apos;]&amp;apos;. Please recheck the objects properties to make sure the desired element is located. </message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:02:20.950186500Z</date>
  <millis>1755594140950</millis>
  <nanos>186500</nanos>
  <sequence>597</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Unable to find the element located by &amp;apos;By.cssSelector: #mui-359&amp;apos;. Please recheck the objects properties to make sure the desired element is located. </message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:02:21.185680200Z</date>
  <millis>1755594141185</millis>
  <nanos>680200</nanos>
  <sequence>601</sequence>
  <level>FAILED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Unable to set text &amp;apos;148485&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;apos; (Root cause: com.kms.katalon.core.exception.StepFailedException: Unable to set text &amp;apos;148485&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;apos;
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at EditCoordinates.run(EditCoordinates:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;apos; located by &amp;apos;internal:role=row[name=&amp;quot;新北勢 119-1&amp;quot;i] &amp;gt;&amp;gt; internal:attr=[placeholder=&amp;quot;請輸入經度 (TWD97)&amp;quot;i]&amp;apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:26)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.doCall(SetTextKeyword.groovy:53)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.call(SetTextKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at Script1755487233288.run(Script1755487233288.groovy:21)
	... 15 more
)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">com.kms.katalon.core.webui.exception.WebElementNotFoundException</property>
  <property name="attachment">1755594140960.png</property>
  <property name="failed.exception.stacktrace">com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&apos; located by &apos;internal:role=row[name=&quot;新北勢 119-1&quot;i] &gt;&gt; internal:attr=[placeholder=&quot;請輸入經度 (TWD97)&quot;i]&apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:26)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.doCall(SetTextKeyword.groovy:53)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.call(SetTextKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at EditCoordinates.run(EditCoordinates:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">Web element with id: &apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&apos; located by &apos;internal:role=row[name=&quot;新北勢 119-1&quot;i] &gt;&gt; internal:attr=[placeholder=&quot;請輸入經度 (TWD97)&quot;i]&apos; not found</property>
</record>
<record>
  <date>2025-08-19T09:02:21.189416300Z</date>
  <millis>1755594141189</millis>
  <nanos>416300</nanos>
  <sequence>604</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : setText(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;quot;), &amp;quot;148485&amp;quot;)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:02:21.191780Z</date>
  <millis>1755594141191</millis>
  <nanos>780000</nanos>
  <sequence>607</sequence>
  <level>FAILED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.
Reason:
com.kms.katalon.core.exception.StepFailedException: Unable to set text &amp;apos;148485&amp;apos; of object &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;apos;
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at EditCoordinates.run(EditCoordinates:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &amp;apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&amp;apos; located by &amp;apos;internal:role=row[name=&amp;quot;新北勢 119-1&amp;quot;i] &amp;gt;&amp;gt; internal:attr=[placeholder=&amp;quot;請輸入經度 (TWD97)&amp;quot;i]&amp;apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:26)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.doCall(SetTextKeyword.groovy:53)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.call(SetTextKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	... 20 more
</message>
  <exception>
    <message>com.kms.katalon.core.exception.StepFailedException: Unable to set text '148485' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'</message>
    <frame>
      <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
      <method>logMessage</method>
      <line>500</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.logging.KeywordLogger</class>
      <method>logMessage</method>
      <line>439</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>logError</method>
      <line>593</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>onExecutionError</method>
      <line>187</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>processExecutionPhase</method>
      <line>465</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>accessMainPhase</method>
      <line>447</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>execute</method>
      <line>321</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.common.CommonExecutor</class>
      <method>accessTestCaseMainPhase</method>
      <line>71</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestSuiteExecutor</class>
      <method>accessTestSuiteMainPhase</method>
      <line>160</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestSuiteExecutor</class>
      <method>execute</method>
      <line>107</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseMain</class>
      <method>startTestSuite</method>
      <line>195</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.vmplugin.v8.IndyInterface</class>
      <method>fromCache</method>
      <line>318</line>
    </frame>
    <frame>
      <class>TempTestSuite1755594025591</class>
      <method>run</method>
      <line>35</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>runScriptOrMainOrTestOrRunnable</method>
      <line>254</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>run</method>
      <line>360</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>run</method>
      <line>349</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>processOnce</method>
      <line>652</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>run</method>
      <line>398</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>access$1400</method>
      <line>68</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain$GroovyCommand</class>
      <method>process</method>
      <line>322</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>processArgs</method>
      <line>142</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>main</method>
      <line>115</line>
    </frame>
    <frame>
      <class>jdk.internal.reflect.NativeMethodAccessorImpl</class>
      <method>invoke0</method>
    </frame>
    <frame>
      <class>jdk.internal.reflect.NativeMethodAccessorImpl</class>
      <method>invoke</method>
      <line>77</line>
    </frame>
    <frame>
      <class>jdk.internal.reflect.DelegatingMethodAccessorImpl</class>
      <method>invoke</method>
      <line>43</line>
    </frame>
    <frame>
      <class>java.lang.reflect.Method</class>
      <method>invoke</method>
      <line>569</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.tools.GroovyStarter</class>
      <method>rootLoader</method>
      <line>117</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.tools.GroovyStarter</class>
      <method>main</method>
      <line>39</line>
    </frame>
  </exception>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.stacktrace">com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: &apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&apos; located by &apos;internal:role=row[name=&quot;新北勢 119-1&quot;i] &gt;&gt; internal:attr=[placeholder=&quot;請輸入經度 (TWD97)&quot;i]&apos; not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:26)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.doCall(SetTextKeyword.groovy:53)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.call(SetTextKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at EditCoordinates.run(EditCoordinates:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.class">com.kms.katalon.core.webui.exception.WebElementNotFoundException</property>
  <property name="failed.exception.message">Web element with id: &apos;Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x&apos; located by &apos;internal:role=row[name=&quot;新北勢 119-1&quot;i] &gt;&gt; internal:attr=[placeholder=&quot;請輸入經度 (TWD97)&quot;i]&apos; not found</property>
</record>
<record>
  <date>2025-08-19T09:02:21.197756900Z</date>
  <millis>1755594141197</millis>
  <nanos>756900</nanos>
  <sequence>609</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/EditCoordinates</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-19T09:02:21.204691400Z</date>
  <millis>1755594141204</millis>
  <nanos>691400</nanos>
  <sequence>610</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startTest</method>
  <thread>1</thread>
  <message>Start Test Case : Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="name">Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</property>
  <property name="description"></property>
  <property name="iteration"></property>
  <property name="id">Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</property>
  <property name="source">C:\project\land-web\katalon\Test Cases\Mandarin\v1_web\Gis\SaveAndVerify.tc</property>
  <property name="tag"></property>
  <property name="isOptional">false</property>
</record>
<record>
  <date>2025-08-19T09:02:21.347023400Z</date>
  <millis>1755594141347</millis>
  <nanos>23400</nanos>
  <sequence>612</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_save&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">21</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:02:54.390450800Z</date>
  <millis>1755594174390</millis>
  <nanos>450800</nanos>
  <sequence>640</sequence>
  <level>FAILED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Unable to click on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_save&amp;apos; (Root cause: com.kms.katalon.core.exception.StepFailedException: Unable to click on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_save&amp;apos;
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at SaveAndVerify.run(SaveAndVerify:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: org.openqa.selenium.ElementClickInterceptedException: element click intercepted: Element &amp;lt;button class=&amp;quot;MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium css-1hw9j7s&amp;quot; tabindex=&amp;quot;-1&amp;quot; type=&amp;quot;button&amp;quot; disabled=&amp;quot;&amp;quot;&amp;gt;...&amp;lt;/button&amp;gt; is not clickable at point (851, 831). Other element would receive the click: &amp;lt;div class=&amp;quot;MuiBox-root css-e82o1n&amp;quot;&amp;gt;...&amp;lt;/div&amp;gt;
  (Session info: chrome=138.0.7204.185)
Build info: version: &amp;apos;4.28.1&amp;apos;, revision: &amp;apos;73f5ad48a2&amp;apos;
System info: os.name: &amp;apos;Windows 11&amp;apos;, os.arch: &amp;apos;amd64&amp;apos;, os.version: &amp;apos;10.0&amp;apos;, java.version: &amp;apos;17.0.14&amp;apos;
Driver info: com.kms.katalon.selenium.driver.CChromeDriver
Command: [f4889763a3d355dde04b3373e74a525e, clickElement {id=f.D87F838160F4ECCFD7F37128432F48DC.d.7DC46C5CDC26DD15DDDFBBF23A96792A.e.129}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.185, chrome: {chromedriverVersion: 138.0.7204.92 (f079b9bc781e..., userDataDir: C:\Users\<USER>\AppData\Loca...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49244}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: windows, proxy: Proxy(), se:cdp: ws://localhost:49244/devtoo..., se:cdpVersion: 138.0.7204.185, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: ignore, webSocketUrl: ws://localhost:34242/sessio..., webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[CChromeDriver: chrome on windows (f4889763a3d355dde04b3373e74a525e)] -&amp;gt; xpath: (//button[@type=&amp;apos;button&amp;apos;])[12]]
Session ID: f4889763a3d355dde04b3373e74a525e
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:215)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:216)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:174)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)
	at org.openqa.selenium.remote.RemoteWebElement.execute(RemoteWebElement.java:223)
	at org.openqa.selenium.remote.RemoteWebElement.click(RemoteWebElement.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor26.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.call(WebDriverDecorator.java:315)
	at org.openqa.selenium.support.decorators.DefaultDecorated.call(DefaultDecorated.java:48)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.lambda$createProxyFactory$3(WebDriverDecorator.java:405)
	at net.bytebuddy.renamed.java.lang.Object$ByteBuddy$mlaGwFki.click(Unknown Source)
	at org.openqa.selenium.WebElement$click.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy:82)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:68)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at Script1755487289260.run(Script1755487289260.groovy:21)
	... 15 more
)</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.class">org.openqa.selenium.ElementClickInterceptedException</property>
  <property name="attachment">1755594174237.png</property>
  <property name="failed.exception.stacktrace">org.openqa.selenium.ElementClickInterceptedException: element click intercepted: Element &lt;button class=&quot;MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium css-1hw9j7s&quot; tabindex=&quot;-1&quot; type=&quot;button&quot; disabled=&quot;&quot;&gt;...&lt;/button&gt; is not clickable at point (851, 831). Other element would receive the click: &lt;div class=&quot;MuiBox-root css-e82o1n&quot;&gt;...&lt;/div&gt;
  (Session info: chrome=138.0.7204.185)
Build info: version: &apos;4.28.1&apos;, revision: &apos;73f5ad48a2&apos;
System info: os.name: &apos;Windows 11&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;17.0.14&apos;
Driver info: com.kms.katalon.selenium.driver.CChromeDriver
Command: [f4889763a3d355dde04b3373e74a525e, clickElement {id=f.D87F838160F4ECCFD7F37128432F48DC.d.7DC46C5CDC26DD15DDDFBBF23A96792A.e.129}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.185, chrome: {chromedriverVersion: 138.0.7204.92 (f079b9bc781e..., userDataDir: C:\Users\<USER>\AppData\Loca...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49244}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: windows, proxy: Proxy(), se:cdp: ws://localhost:49244/devtoo..., se:cdpVersion: 138.0.7204.185, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: ignore, webSocketUrl: ws://localhost:34242/sessio..., webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[CChromeDriver: chrome on windows (f4889763a3d355dde04b3373e74a525e)] -&gt; xpath: (//button[@type=&apos;button&apos;])[12]]
Session ID: f4889763a3d355dde04b3373e74a525e
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:215)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:216)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:174)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)
	at org.openqa.selenium.remote.RemoteWebElement.execute(RemoteWebElement.java:223)
	at org.openqa.selenium.remote.RemoteWebElement.click(RemoteWebElement.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor26.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.call(WebDriverDecorator.java:315)
	at org.openqa.selenium.support.decorators.DefaultDecorated.call(DefaultDecorated.java:48)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.lambda$createProxyFactory$3(WebDriverDecorator.java:405)
	at net.bytebuddy.renamed.java.lang.Object$ByteBuddy$mlaGwFki.click(Unknown Source)
	at org.openqa.selenium.WebElement$click.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy:82)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:68)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at SaveAndVerify.run(SaveAndVerify:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.message">element click intercepted: Element &lt;button class=&quot;MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium css-1hw9j7s&quot; tabindex=&quot;-1&quot; type=&quot;button&quot; disabled=&quot;&quot;&gt;...&lt;/button&gt; is not clickable at point (851, 831). Other element would receive the click: &lt;div class=&quot;MuiBox-root css-e82o1n&quot;&gt;...&lt;/div&gt;
  (Session info: chrome=138.0.7204.185)
Build info: version: &apos;4.28.1&apos;, revision: &apos;73f5ad48a2&apos;
System info: os.name: &apos;Windows 11&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;17.0.14&apos;
Driver info: com.kms.katalon.selenium.driver.CChromeDriver
Command: [f4889763a3d355dde04b3373e74a525e, clickElement {id=f.D87F838160F4ECCFD7F37128432F48DC.d.7DC46C5CDC26DD15DDDFBBF23A96792A.e.129}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.185, chrome: {chromedriverVersion: 138.0.7204.92 (f079b9bc781e..., userDataDir: C:\Users\<USER>\AppData\Loca...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49244}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: windows, proxy: Proxy(), se:cdp: ws://localhost:49244/devtoo..., se:cdpVersion: 138.0.7204.185, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: ignore, webSocketUrl: ws://localhost:34242/sessio..., webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[CChromeDriver: chrome on windows (f4889763a3d355dde04b3373e74a525e)] -&gt; xpath: (//button[@type=&apos;button&apos;])[12]]
Session ID: f4889763a3d355dde04b3373e74a525e</property>
</record>
<record>
  <date>2025-08-19T09:02:54.393437400Z</date>
  <millis>*************</millis>
  <nanos>437400</nanos>
  <sequence>643</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : click(findTestObject(&amp;quot;Object Repository/Mandarin/v1_web/Gis/button_save&amp;quot;))</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:02:54.395440400Z</date>
  <millis>1755594174395</millis>
  <nanos>440400</nanos>
  <sequence>646</sequence>
  <level>FAILED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Test Cases/Mandarin/v1_web/Gis/SaveAndVerify FAILED.
Reason:
com.kms.katalon.core.exception.StepFailedException: Unable to click on object &amp;apos;Object Repository/Mandarin/v1_web/Gis/button_save&amp;apos;
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at SaveAndVerify.run(SaveAndVerify:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: org.openqa.selenium.ElementClickInterceptedException: element click intercepted: Element &amp;lt;button class=&amp;quot;MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium css-1hw9j7s&amp;quot; tabindex=&amp;quot;-1&amp;quot; type=&amp;quot;button&amp;quot; disabled=&amp;quot;&amp;quot;&amp;gt;...&amp;lt;/button&amp;gt; is not clickable at point (851, 831). Other element would receive the click: &amp;lt;div class=&amp;quot;MuiBox-root css-e82o1n&amp;quot;&amp;gt;...&amp;lt;/div&amp;gt;
  (Session info: chrome=138.0.7204.185)
Build info: version: &amp;apos;4.28.1&amp;apos;, revision: &amp;apos;73f5ad48a2&amp;apos;
System info: os.name: &amp;apos;Windows 11&amp;apos;, os.arch: &amp;apos;amd64&amp;apos;, os.version: &amp;apos;10.0&amp;apos;, java.version: &amp;apos;17.0.14&amp;apos;
Driver info: com.kms.katalon.selenium.driver.CChromeDriver
Command: [f4889763a3d355dde04b3373e74a525e, clickElement {id=f.D87F838160F4ECCFD7F37128432F48DC.d.7DC46C5CDC26DD15DDDFBBF23A96792A.e.129}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.185, chrome: {chromedriverVersion: 138.0.7204.92 (f079b9bc781e..., userDataDir: C:\Users\<USER>\AppData\Loca...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49244}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: windows, proxy: Proxy(), se:cdp: ws://localhost:49244/devtoo..., se:cdpVersion: 138.0.7204.185, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: ignore, webSocketUrl: ws://localhost:34242/sessio..., webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[CChromeDriver: chrome on windows (f4889763a3d355dde04b3373e74a525e)] -&amp;gt; xpath: (//button[@type=&amp;apos;button&amp;apos;])[12]]
Session ID: f4889763a3d355dde04b3373e74a525e
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:215)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:216)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:174)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)
	at org.openqa.selenium.remote.RemoteWebElement.execute(RemoteWebElement.java:223)
	at org.openqa.selenium.remote.RemoteWebElement.click(RemoteWebElement.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor26.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.call(WebDriverDecorator.java:315)
	at org.openqa.selenium.support.decorators.DefaultDecorated.call(DefaultDecorated.java:48)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.lambda$createProxyFactory$3(WebDriverDecorator.java:405)
	at net.bytebuddy.renamed.java.lang.Object$ByteBuddy$mlaGwFki.click(Unknown Source)
	at org.openqa.selenium.WebElement$click.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy:82)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:68)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	... 20 more
</message>
  <exception>
    <message>com.kms.katalon.core.exception.StepFailedException: Unable to click on object 'Object Repository/Mandarin/v1_web/Gis/button_save'</message>
    <frame>
      <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
      <method>logMessage</method>
      <line>500</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.logging.KeywordLogger</class>
      <method>logMessage</method>
      <line>439</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>logError</method>
      <line>593</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>onExecutionError</method>
      <line>187</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>processExecutionPhase</method>
      <line>465</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>accessMainPhase</method>
      <line>447</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseExecutor</class>
      <method>execute</method>
      <line>321</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.common.CommonExecutor</class>
      <method>accessTestCaseMainPhase</method>
      <line>71</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestSuiteExecutor</class>
      <method>accessTestSuiteMainPhase</method>
      <line>160</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestSuiteExecutor</class>
      <method>execute</method>
      <line>107</line>
    </frame>
    <frame>
      <class>com.kms.katalon.core.main.TestCaseMain</class>
      <method>startTestSuite</method>
      <line>195</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.vmplugin.v8.IndyInterface</class>
      <method>fromCache</method>
      <line>318</line>
    </frame>
    <frame>
      <class>TempTestSuite1755594025591</class>
      <method>run</method>
      <line>35</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>runScriptOrMainOrTestOrRunnable</method>
      <line>254</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>run</method>
      <line>360</line>
    </frame>
    <frame>
      <class>groovy.lang.GroovyShell</class>
      <method>run</method>
      <line>349</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>processOnce</method>
      <line>652</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>run</method>
      <line>398</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>access$1400</method>
      <line>68</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain$GroovyCommand</class>
      <method>process</method>
      <line>322</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>processArgs</method>
      <line>142</line>
    </frame>
    <frame>
      <class>groovy.ui.GroovyMain</class>
      <method>main</method>
      <line>115</line>
    </frame>
    <frame>
      <class>jdk.internal.reflect.NativeMethodAccessorImpl</class>
      <method>invoke0</method>
    </frame>
    <frame>
      <class>jdk.internal.reflect.NativeMethodAccessorImpl</class>
      <method>invoke</method>
      <line>77</line>
    </frame>
    <frame>
      <class>jdk.internal.reflect.DelegatingMethodAccessorImpl</class>
      <method>invoke</method>
      <line>43</line>
    </frame>
    <frame>
      <class>java.lang.reflect.Method</class>
      <method>invoke</method>
      <line>569</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.tools.GroovyStarter</class>
      <method>rootLoader</method>
      <line>117</line>
    </frame>
    <frame>
      <class>org.codehaus.groovy.tools.GroovyStarter</class>
      <method>main</method>
      <line>39</line>
    </frame>
  </exception>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="failed.exception.stacktrace">org.openqa.selenium.ElementClickInterceptedException: element click intercepted: Element &lt;button class=&quot;MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium css-1hw9j7s&quot; tabindex=&quot;-1&quot; type=&quot;button&quot; disabled=&quot;&quot;&gt;...&lt;/button&gt; is not clickable at point (851, 831). Other element would receive the click: &lt;div class=&quot;MuiBox-root css-e82o1n&quot;&gt;...&lt;/div&gt;
  (Session info: chrome=138.0.7204.185)
Build info: version: &apos;4.28.1&apos;, revision: &apos;73f5ad48a2&apos;
System info: os.name: &apos;Windows 11&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;17.0.14&apos;
Driver info: com.kms.katalon.selenium.driver.CChromeDriver
Command: [f4889763a3d355dde04b3373e74a525e, clickElement {id=f.D87F838160F4ECCFD7F37128432F48DC.d.7DC46C5CDC26DD15DDDFBBF23A96792A.e.129}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.185, chrome: {chromedriverVersion: 138.0.7204.92 (f079b9bc781e..., userDataDir: C:\Users\<USER>\AppData\Loca...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49244}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: windows, proxy: Proxy(), se:cdp: ws://localhost:49244/devtoo..., se:cdpVersion: 138.0.7204.185, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: ignore, webSocketUrl: ws://localhost:34242/sessio..., webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[CChromeDriver: chrome on windows (f4889763a3d355dde04b3373e74a525e)] -&gt; xpath: (//button[@type=&apos;button&apos;])[12]]
Session ID: f4889763a3d355dde04b3373e74a525e
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:215)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:216)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:174)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)
	at org.openqa.selenium.remote.RemoteWebElement.execute(RemoteWebElement.java:223)
	at org.openqa.selenium.remote.RemoteWebElement.click(RemoteWebElement.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor26.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.call(WebDriverDecorator.java:315)
	at org.openqa.selenium.support.decorators.DefaultDecorated.call(DefaultDecorated.java:48)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.lambda$createProxyFactory$3(WebDriverDecorator.java:405)
	at net.bytebuddy.renamed.java.lang.Object$ByteBuddy$mlaGwFki.click(Unknown Source)
	at org.openqa.selenium.WebElement$click.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy:82)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:68)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at SaveAndVerify.run(SaveAndVerify:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
</property>
  <property name="failed.exception.class">org.openqa.selenium.ElementClickInterceptedException</property>
  <property name="failed.exception.message">element click intercepted: Element &lt;button class=&quot;MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium css-1hw9j7s&quot; tabindex=&quot;-1&quot; type=&quot;button&quot; disabled=&quot;&quot;&gt;...&lt;/button&gt; is not clickable at point (851, 831). Other element would receive the click: &lt;div class=&quot;MuiBox-root css-e82o1n&quot;&gt;...&lt;/div&gt;
  (Session info: chrome=138.0.7204.185)
Build info: version: &apos;4.28.1&apos;, revision: &apos;73f5ad48a2&apos;
System info: os.name: &apos;Windows 11&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;17.0.14&apos;
Driver info: com.kms.katalon.selenium.driver.CChromeDriver
Command: [f4889763a3d355dde04b3373e74a525e, clickElement {id=f.D87F838160F4ECCFD7F37128432F48DC.d.7DC46C5CDC26DD15DDDFBBF23A96792A.e.129}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.185, chrome: {chromedriverVersion: 138.0.7204.92 (f079b9bc781e..., userDataDir: C:\Users\<USER>\AppData\Loca...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49244}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: windows, proxy: Proxy(), se:cdp: ws://localhost:49244/devtoo..., se:cdpVersion: 138.0.7204.185, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: ignore, webSocketUrl: ws://localhost:34242/sessio..., webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[CChromeDriver: chrome on windows (f4889763a3d355dde04b3373e74a525e)] -&gt; xpath: (//button[@type=&apos;button&apos;])[12]]
Session ID: f4889763a3d355dde04b3373e74a525e</property>
</record>
<record>
  <date>2025-08-19T09:02:54.400668700Z</date>
  <millis>*************</millis>
  <nanos>668700</nanos>
  <sequence>648</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endTest</method>
  <thread>1</thread>
  <message>End Test Case : Test Cases/Mandarin/v1_web/Gis/SaveAndVerify</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="executionBindingVariables">[]</property>
  <property name="applitools">false</property>
  <property name="keywordsUsage">{
  &quot;web_ui&quot;: true,
  &quot;web_service&quot;: false,
  &quot;cucumber&quot;: false,
  &quot;builtin&quot;: false,
  &quot;mobile&quot;: false,
  &quot;windows&quot;: false,
  &quot;testng&quot;: false
}</property>
</record>
<record>
  <date>2025-08-19T09:02:54.400668700Z</date>
  <millis>*************</millis>
  <nanos>668700</nanos>
  <sequence>649</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start listener action : afterSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:02:54.402679100Z</date>
  <millis>1755594174402</millis>
  <nanos>679100</nanos>
  <sequence>651</sequence>
  <level>START</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>startKeyword</method>
  <thread>1</thread>
  <message>Start action : closeBrowser()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="startLine">35</property>
  <property name="stepIndex">1</property>
</record>
<record>
  <date>2025-08-19T09:02:54.646782500Z</date>
  <millis>1755594174646</millis>
  <nanos>782500</nanos>
  <sequence>653</sequence>
  <level>PASSED</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>1</thread>
  <message>Browser is closed</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
  <property name="testops-method-name">com.kms.katalon.core.webui.keyword.builtin.CloseBrowserKeyword.closeBrowser</property>
  <property name="testops-execution-stacktrace"></property>
</record>
<record>
  <date>2025-08-19T09:02:54.646782500Z</date>
  <millis>1755594174646</millis>
  <nanos>782500</nanos>
  <sequence>655</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End action : closeBrowser()</message>
  <nestedLevel>1</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:02:54.647797200Z</date>
  <millis>1755594174647</millis>
  <nanos>797200</nanos>
  <sequence>656</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endKeyword</method>
  <thread>1</thread>
  <message>End listener action : afterSuite</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-19T09:02:54.696427900Z</date>
  <millis>1755594174696</millis>
  <nanos>427900</nanos>
  <sequence>657</sequence>
  <level>END</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>endSuite</method>
  <thread>1</thread>
  <message>End Test Suite : Test Suites/Mandarin/v1/Gis</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
</log>
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE log SYSTEM "logger.dtd">
<log>
<record>
  <date>2025-08-20T01:42:02.561142800Z</date>
  <millis>1755654122561</millis>
  <nanos>142800</nanos>
  <sequence>190</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>160</thread>
  <message>Action delay is set to 0 milliseconds</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
<record>
  <date>2025-08-20T01:53:55.409278300Z</date>
  <millis>1755654835409</millis>
  <nanos>278300</nanos>
  <sequence>757</sequence>
  <level>INFO</level>
  <class>com.kms.katalon.core.logging.XmlKeywordLogger</class>
  <method>logMessage</method>
  <thread>314</thread>
  <message>Action delay is set to 0 milliseconds</message>
  <nestedLevel>0</nestedLevel>
  <escapedJava>false</escapedJava>
</record>
</log>
