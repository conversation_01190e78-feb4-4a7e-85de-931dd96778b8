Warning: NLS unused message: QTEST in: com.kms.katalon.constants.globalMessages
Warning: NLS missing message: QTEST_NAME in: com.kms.katalon.constants.globalMessages
2025-08-19 17:00:28.937 INFO  c.k.katalon.core.main.TestSuiteExecutor  - START Test Suites/Mandarin/v1/Gis
2025-08-19 17:00:29.039 INFO  c.k.katalon.core.main.TestSuiteExecutor  - userFullName = Lin Kai Yee
2025-08-19 17:00:29.040 INFO  c.k.katalon.core.main.TestSuiteExecutor  - projectName = katalon
2025-08-19 17:00:29.040 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostName = saw63 - host.docker.internal
2025-08-19 17:00:29.041 INFO  c.k.katalon.core.main.TestSuiteExecutor  - os = Windows 11 64bit
2025-08-19 17:00:29.041 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostAddress = ************
2025-08-19 17:00:29.042 INFO  c.k.katalon.core.main.TestSuiteExecutor  - katalonVersion = ********
2025-08-19 17:00:29.372 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-19 17:00:29.492 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 19, 2025 5:00:30 下午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-19 17:00:31.402 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 278154ae94a1c906c4c9e64473cf3628
2025-08-19 17:00:31.403 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-19 17:00:31.404 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-19 17:00:31.405 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-19 17:00:31.414 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-19 17:00:31.977 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 17:00:31.977 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-19 17:00:32.047 DEBUG testcase.GoToGis                         - 1: openBrowser("")
2025-08-19 17:00:32.054 WARN  c.k.k.core.webui.driver.DriverFactory    - A browser is already opened. Closing browser and opening a new one
2025-08-19 17:00:32.260 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-19 17:00:32.262 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 19, 2025 5:00:33 下午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-19 17:00:33.497 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = f4889763a3d355dde04b3373e74a525e
2025-08-19 17:00:33.498 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-19 17:00:33.499 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-19 17:00:33.499 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-19 17:00:33.501 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-19 17:00:33.528 DEBUG testcase.GoToGis                         - 2: navigateToUrl("https://land2.daoyidh.com/Gis")
2025-08-19 17:00:33.968 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-19 17:00:33.980 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 17:00:33.980 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-19 17:00:34.097 DEBUG testcase.LoginWithEmail                  - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_"))
2025-08-19 17:00:35.614 DEBUG testcase.LoginWithEmail                  - 2: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail"))
2025-08-19 17:00:36.128 DEBUG testcase.LoginWithEmail                  - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Email_email"), "<EMAIL>")
2025-08-19 17:00:36.965 DEBUG testcase.LoginWithEmail                  - 4: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Next"))
2025-08-19 17:00:37.270 DEBUG testcase.LoginWithEmail                  - 5: setEncryptedText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Password_password"), "0D41p6Ct1VJ35FCiyaw3Gw==")
2025-08-19 17:00:38.220 DEBUG testcase.LoginWithEmail                  - 6: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign In"))
2025-08-19 17:00:38.523 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-19 17:00:38.525 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 17:00:38.525 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-19 17:00:38.568 DEBUG testcase.SearchTextInGis                 - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button__1"))
2025-08-19 17:00:41.241 DEBUG testcase.SearchTextInGis                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input__mui-3"), "新北勢")
2025-08-19 17:00:41.985 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-19 17:00:41.989 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 17:00:41.989 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-19 17:00:42.247 DEBUG testcase.ChoosePerPage                   - 1: try
2025-08-19 17:00:42.248 DEBUG testcase.ChoosePerPage                   - 1: waitForPageLoad(10)
2025-08-19 17:00:42.355 DEBUG testcase.ChoosePerPage                   - 2: dropdownButton = new com.kms.katalon.core.testobject.TestObject()
2025-08-19 17:00:42.362 DEBUG testcase.ChoosePerPage                   - 3: selectors = ["div[role="combobox"]", "[role="combobox"]", "div[aria-haspopup="listbox"]", ".MuiSelect-root", "div.MuiInputBase-root[role="combobox"]"]
2025-08-19 17:00:42.364 DEBUG testcase.ChoosePerPage                   - 4: dropdownFound = false
2025-08-19 17:00:42.366 DEBUG testcase.ChoosePerPage                   - 5: for (String selector : selectors)
2025-08-19 17:00:42.367 DEBUG testcase.ChoosePerPage                   - 1: try
2025-08-19 17:00:42.369 DEBUG testcase.ChoosePerPage                   - 1: dropdownButton.addProperty("css", EQUALS, selector)
2025-08-19 17:00:42.376 DEBUG testcase.ChoosePerPage                   - 2: if (waitForElementVisible(dropdownButton, 10))
2025-08-19 17:00:53.496 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - Unable to find the element located by 'By.cssSelector: div[role="combobox"]'. Please recheck the objects properties to make sure the desired element is located. 
2025-08-19 17:00:53.497 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Failed to find element with id ''. Try using Self-healing.
2025-08-19 17:00:53.508 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Index 0 out of bounds for length 0
2025-08-19 17:00:53.509 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Cannot find elements when the selector is null
2025-08-19 17:00:53.517 WARN  k.k.c.w.k.b.WaitForElementVisibleKeyword - Web element with id: '' located by 'div[role="combobox"]' not found
2025-08-19 17:00:53.523 DEBUG testcase.ChoosePerPage                   - 1: try
2025-08-19 17:00:53.524 DEBUG testcase.ChoosePerPage                   - 1: dropdownButton.addProperty("css", EQUALS, selector)
2025-08-19 17:00:53.524 DEBUG testcase.ChoosePerPage                   - 2: if (waitForElementVisible(dropdownButton, 10))
2025-08-19 17:01:04.571 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - Unable to find the element located by 'By.cssSelector: div[role="combobox"]'. Please recheck the objects properties to make sure the desired element is located. 
2025-08-19 17:01:04.572 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Failed to find element with id ''. Try using Self-healing.
2025-08-19 17:01:04.575 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Index 0 out of bounds for length 0
2025-08-19 17:01:04.575 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Cannot find elements when the selector is null
2025-08-19 17:01:04.576 WARN  k.k.c.w.k.b.WaitForElementVisibleKeyword - Web element with id: '' located by 'div[role="combobox"]' not found
2025-08-19 17:01:04.576 DEBUG testcase.ChoosePerPage                   - 1: try
2025-08-19 17:01:04.577 DEBUG testcase.ChoosePerPage                   - 1: dropdownButton.addProperty("css", EQUALS, selector)
2025-08-19 17:01:04.578 DEBUG testcase.ChoosePerPage                   - 2: if (waitForElementVisible(dropdownButton, 10))
2025-08-19 17:01:15.822 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - Unable to find the element located by 'By.cssSelector: div[role="combobox"]'. Please recheck the objects properties to make sure the desired element is located. 
2025-08-19 17:01:15.823 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Failed to find element with id ''. Try using Self-healing.
2025-08-19 17:01:15.825 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Index 0 out of bounds for length 0
2025-08-19 17:01:15.826 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Cannot find elements when the selector is null
2025-08-19 17:01:15.827 WARN  k.k.c.w.k.b.WaitForElementVisibleKeyword - Web element with id: '' located by 'div[role="combobox"]' not found
2025-08-19 17:01:15.827 DEBUG testcase.ChoosePerPage                   - 1: try
2025-08-19 17:01:15.827 DEBUG testcase.ChoosePerPage                   - 1: dropdownButton.addProperty("css", EQUALS, selector)
2025-08-19 17:01:15.827 DEBUG testcase.ChoosePerPage                   - 2: if (waitForElementVisible(dropdownButton, 10))
2025-08-19 17:01:26.895 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - Unable to find the element located by 'By.cssSelector: div[role="combobox"]'. Please recheck the objects properties to make sure the desired element is located. 
2025-08-19 17:01:26.895 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Failed to find element with id ''. Try using Self-healing.
2025-08-19 17:01:26.898 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Index 0 out of bounds for length 0
2025-08-19 17:01:26.899 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Cannot find elements when the selector is null
2025-08-19 17:01:26.899 WARN  k.k.c.w.k.b.WaitForElementVisibleKeyword - Web element with id: '' located by 'div[role="combobox"]' not found
2025-08-19 17:01:26.901 DEBUG testcase.ChoosePerPage                   - 1: try
2025-08-19 17:01:26.902 DEBUG testcase.ChoosePerPage                   - 1: dropdownButton.addProperty("css", EQUALS, selector)
2025-08-19 17:01:26.903 DEBUG testcase.ChoosePerPage                   - 2: if (waitForElementVisible(dropdownButton, 10))
2025-08-19 17:01:38.108 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - Unable to find the element located by 'By.cssSelector: div[role="combobox"]'. Please recheck the objects properties to make sure the desired element is located. 
2025-08-19 17:01:38.108 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Failed to find element with id ''. Try using Self-healing.
2025-08-19 17:01:38.110 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Index 0 out of bounds for length 0
2025-08-19 17:01:38.111 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Cannot find elements when the selector is null
2025-08-19 17:01:38.112 WARN  k.k.c.w.k.b.WaitForElementVisibleKeyword - Web element with id: '' located by 'div[role="combobox"]' not found
2025-08-19 17:01:38.116 DEBUG testcase.ChoosePerPage                   - 6: if (!(dropdownFound))
2025-08-19 17:01:38.119 DEBUG testcase.ChoosePerPage                   - 1: throw new java.lang.Exception(無法找到下拉選單按鈕)
2025-08-19 17:01:38.122 DEBUG testcase.ChoosePerPage                   - 2: catch (Exception e)
2025-08-19 17:01:38.124 DEBUG testcase.ChoosePerPage                   - 1: println(選擇每頁50筆失敗: $e.getMessage())
選擇每頁50筆失敗: 無法找到下拉選單按鈕
2025-08-19 17:01:38.151 DEBUG testcase.ChoosePerPage                   - 2: try
2025-08-19 17:01:38.152 DEBUG testcase.ChoosePerPage                   - 1: println("嘗試備用方案...")
嘗試備用方案...
2025-08-19 17:01:38.155 DEBUG testcase.ChoosePerPage                   - 2: dropdownBackup = new com.kms.katalon.core.testobject.TestObject()
2025-08-19 17:01:38.156 DEBUG testcase.ChoosePerPage                   - 3: dropdownBackup.addProperty("css", EQUALS, "[role="combobox"]")
2025-08-19 17:01:38.158 DEBUG testcase.ChoosePerPage                   - 4: if (waitForElementVisible(dropdownBackup, 5))
2025-08-19 17:01:44.352 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - Unable to find the element located by 'By.cssSelector: [role="combobox"]'. Please recheck the objects properties to make sure the desired element is located. 
2025-08-19 17:01:44.352 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Failed to find element with id ''. Try using Self-healing.
2025-08-19 17:01:44.357 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Index 0 out of bounds for length 0
2025-08-19 17:01:44.358 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Cannot find elements when the selector is null
2025-08-19 17:01:44.360 WARN  k.k.c.w.k.b.WaitForElementVisibleKeyword - Web element with id: '' located by '[role="combobox"]' not found
2025-08-19 17:01:44.363 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-19 17:01:44.371 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 17:01:44.371 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-19 17:01:44.479 DEBUG testcase.EditCoordinates                 - 1: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "148485")
2025-08-19 17:02:15.531 WARN  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Failed to find element with id 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'. Try using Self-healing.
2025-08-19 17:02:16.726 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - Unable to find the element located by 'By.xpath: //input[@id='mui-359']'. Please recheck the objects properties to make sure the desired element is located. 
2025-08-19 17:02:17.397 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Failed Locator: '//input[@id='mui-359']'.
2025-08-19 17:02:18.304 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Failed Locator: '//div[@id='root']/div/div[2]/div/div[3]/div/table/tbody/tr[49]/td[3]/div/div/input'.
2025-08-19 17:02:18.602 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Failed Locator: '//tr[49]/td[3]/div/div/input'.
2025-08-19 17:02:18.885 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - [SELF-HEALING] Failed Locator: '//input[@placeholder = '請輸入經度 (TWD97)' and @type = 'number' and @id = 'mui-359']'.
2025-08-19 17:02:19.910 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - Unable to find the element located by 'By.xpath: //*[@placeholder = '請輸入經度 (TWD97)' and @type = 'number' and @id = 'mui-359']'. Please recheck the objects properties to make sure the desired element is located. 
2025-08-19 17:02:20.950 INFO  c.k.k.c.webui.common.WebUiCommonHelper   - Unable to find the element located by 'By.cssSelector: #mui-359'. Please recheck the objects properties to make sure the desired element is located. 
2025-08-19 17:02:21.184 ERROR c.k.k.core.keyword.internal.KeywordMain  - ❌ Unable to set text '148485' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x' (Root cause: com.kms.katalon.core.exception.StepFailedException: Unable to set text '148485' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at EditCoordinates.run(EditCoordinates:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x' located by 'internal:role=row[name="新北勢 119-1"i] >> internal:attr=[placeholder="請輸入經度 (TWD97)"i]' not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:26)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.doCall(SetTextKeyword.groovy:53)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.call(SetTextKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at Script1755487233288.run(Script1755487233288.groovy:21)
	... 15 more
)
2025-08-19 17:02:21.190 ERROR c.k.katalon.core.main.TestCaseExecutor   - ❌ Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.
Reason:
com.kms.katalon.core.exception.StepFailedException: Unable to set text '148485' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.setText(SetTextKeyword.groovy:86)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword.execute(SetTextKeyword.groovy:39)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.setText(WebUiBuiltInKeywords.groovy:1056)
	at EditCoordinates.run(EditCoordinates:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: com.kms.katalon.core.webui.exception.WebElementNotFoundException: Web element with id: 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x' located by 'internal:role=row[name="新北勢 119-1"i] >> internal:attr=[placeholder="請輸入經度 (TWD97)"i]' not found
	at com.kms.katalon.core.webui.common.WebUiCommonHelper.findWebElement(WebUiCommonHelper.java:1458)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:27)
	at com.kms.katalon.core.webui.keyword.internal.WebUIAbstractKeyword.findWebElement(WebUIAbstractKeyword.groovy:26)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.doCall(SetTextKeyword.groovy:53)
	at com.kms.katalon.core.webui.keyword.builtin.SetTextKeyword$_setText_closure1.call(SetTextKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	... 20 more

2025-08-19 17:02:21.197 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-19 17:02:21.204 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-19 17:02:21.204 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SaveAndVerify
2025-08-19 17:02:21.346 DEBUG testcase.SaveAndVerify                   - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_save"))
2025-08-19 17:02:54.390 ERROR c.k.k.core.keyword.internal.KeywordMain  - ❌ Unable to click on object 'Object Repository/Mandarin/v1_web/Gis/button_save' (Root cause: com.kms.katalon.core.exception.StepFailedException: Unable to click on object 'Object Repository/Mandarin/v1_web/Gis/button_save'
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at SaveAndVerify.run(SaveAndVerify:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: org.openqa.selenium.ElementClickInterceptedException: element click intercepted: Element <button class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium css-1hw9j7s" tabindex="-1" type="button" disabled="">...</button> is not clickable at point (851, 831). Other element would receive the click: <div class="MuiBox-root css-e82o1n">...</div>
  (Session info: chrome=138.0.7204.185)
Build info: version: '4.28.1', revision: '73f5ad48a2'
System info: os.name: 'Windows 11', os.arch: 'amd64', os.version: '10.0', java.version: '17.0.14'
Driver info: com.kms.katalon.selenium.driver.CChromeDriver
Command: [f4889763a3d355dde04b3373e74a525e, clickElement {id=f.D87F838160F4ECCFD7F37128432F48DC.d.7DC46C5CDC26DD15DDDFBBF23A96792A.e.129}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.185, chrome: {chromedriverVersion: 138.0.7204.92 (f079b9bc781e..., userDataDir: C:\Users\<USER>\AppData\Loca...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49244}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: windows, proxy: Proxy(), se:cdp: ws://localhost:49244/devtoo..., se:cdpVersion: 138.0.7204.185, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: ignore, webSocketUrl: ws://localhost:34242/sessio..., webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[CChromeDriver: chrome on windows (f4889763a3d355dde04b3373e74a525e)] -> xpath: (//button[@type='button'])[12]]
Session ID: f4889763a3d355dde04b3373e74a525e
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:215)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:216)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:174)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)
	at org.openqa.selenium.remote.RemoteWebElement.execute(RemoteWebElement.java:223)
	at org.openqa.selenium.remote.RemoteWebElement.click(RemoteWebElement.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor26.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.call(WebDriverDecorator.java:315)
	at org.openqa.selenium.support.decorators.DefaultDecorated.call(DefaultDecorated.java:48)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.lambda$createProxyFactory$3(WebDriverDecorator.java:405)
	at net.bytebuddy.renamed.java.lang.Object$ByteBuddy$mlaGwFki.click(Unknown Source)
	at org.openqa.selenium.WebElement$click.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy:82)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:68)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at Script1755487289260.run(Script1755487289260.groovy:21)
	... 15 more
)
2025-08-19 17:02:54.394 ERROR c.k.katalon.core.main.TestCaseExecutor   - ❌ Test Cases/Mandarin/v1_web/Gis/SaveAndVerify FAILED.
Reason:
com.kms.katalon.core.exception.StepFailedException: Unable to click on object 'Object Repository/Mandarin/v1_web/Gis/button_save'
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.stepFailed(WebUIKeywordMain.groovy:118)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.click(ClickKeyword.groovy:75)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.execute(ClickKeyword.groovy:40)
	at com.kms.katalon.core.keyword.internal.KeywordExecutor.executeKeywordForPlatform(KeywordExecutor.groovy:64)
	at com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.click(WebUiBuiltInKeywords.groovy:696)
	at SaveAndVerify.run(SaveAndVerify:21)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755594025591.run(TempTestSuite1755594025591.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
Caused by: org.openqa.selenium.ElementClickInterceptedException: element click intercepted: Element <button class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium css-1hw9j7s" tabindex="-1" type="button" disabled="">...</button> is not clickable at point (851, 831). Other element would receive the click: <div class="MuiBox-root css-e82o1n">...</div>
  (Session info: chrome=138.0.7204.185)
Build info: version: '4.28.1', revision: '73f5ad48a2'
System info: os.name: 'Windows 11', os.arch: 'amd64', os.version: '10.0', java.version: '17.0.14'
Driver info: com.kms.katalon.selenium.driver.CChromeDriver
Command: [f4889763a3d355dde04b3373e74a525e, clickElement {id=f.D87F838160F4ECCFD7F37128432F48DC.d.7DC46C5CDC26DD15DDDFBBF23A96792A.e.129}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 138.0.7204.185, chrome: {chromedriverVersion: 138.0.7204.92 (f079b9bc781e..., userDataDir: C:\Users\<USER>\AppData\Loca...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:49244}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: windows, proxy: Proxy(), se:cdp: ws://localhost:49244/devtoo..., se:cdpVersion: 138.0.7204.185, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: ignore, webSocketUrl: ws://localhost:34242/sessio..., webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[CChromeDriver: chrome on windows (f4889763a3d355dde04b3373e74a525e)] -> xpath: (//button[@type='button'])[12]]
Session ID: f4889763a3d355dde04b3373e74a525e
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:215)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:216)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:174)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)
	at org.openqa.selenium.remote.RemoteWebElement.execute(RemoteWebElement.java:223)
	at org.openqa.selenium.remote.RemoteWebElement.click(RemoteWebElement.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor26.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.call(WebDriverDecorator.java:315)
	at org.openqa.selenium.support.decorators.DefaultDecorated.call(DefaultDecorated.java:48)
	at org.openqa.selenium.support.decorators.WebDriverDecorator.lambda$createProxyFactory$3(WebDriverDecorator.java:405)
	at net.bytebuddy.renamed.java.lang.Object$ByteBuddy$mlaGwFki.click(Unknown Source)
	at org.openqa.selenium.WebElement$click.call(Unknown Source)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy:82)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword.clickUntilSuccessWithTimeout(ClickKeyword.groovy)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.doCall(ClickKeyword.groovy:68)
	at com.kms.katalon.core.webui.keyword.builtin.ClickKeyword$_click_closure1.call(ClickKeyword.groovy)
	at com.kms.katalon.core.webui.keyword.internal.WebUIKeywordMain.runKeyword(WebUIKeywordMain.groovy:35)
	... 20 more

2025-08-19 17:02:54.399 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SaveAndVerify
2025-08-19 17:02:54.696 INFO  c.k.katalon.core.main.TestSuiteExecutor  - --------------------
2025-08-19 17:02:54.696 INFO  c.k.katalon.core.main.TestSuiteExecutor  - END Test Suites/Mandarin/v1/Gis
2025-08-19 17:02:54.696 INFO  c.k.katalon.core.main.TestSuiteExecutor  - ====================
