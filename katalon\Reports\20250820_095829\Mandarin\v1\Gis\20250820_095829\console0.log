Warning: NLS unused message: QTEST in: com.kms.katalon.constants.globalMessages
Warning: NLS missing message: QTEST_NAME in: com.kms.katalon.constants.globalMessages
2025-08-20 09:58:32.254 INFO  c.k.katalon.core.main.TestSuiteExecutor  - START Test Suites/Mandarin/v1/Gis
2025-08-20 09:58:32.379 INFO  c.k.katalon.core.main.TestSuiteExecutor  - userFullName = Lin Kai Yee
2025-08-20 09:58:32.380 INFO  c.k.katalon.core.main.TestSuiteExecutor  - projectName = katalon
2025-08-20 09:58:32.381 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostName = saw63 - host.docker.internal
2025-08-20 09:58:32.381 INFO  c.k.katalon.core.main.TestSuiteExecutor  - os = Windows 11 64bit
2025-08-20 09:58:32.381 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostAddress = ************
2025-08-20 09:58:32.382 INFO  c.k.katalon.core.main.TestSuiteExecutor  - katalonVersion = ********
2025-08-20 09:58:32.661 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-20 09:58:32.757 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 20, 2025 9:58:34 上午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-20 09:58:34.513 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 9e9e743081a218d900af9bbfa88a98f7
2025-08-20 09:58:34.516 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-20 09:58:34.517 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-20 09:58:34.517 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-20 09:58:34.526 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-20 09:58:35.066 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 09:58:35.066 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-20 09:58:35.134 DEBUG testcase.GoToGis                         - 1: openBrowser("")
2025-08-20 09:58:35.139 WARN  c.k.k.core.webui.driver.DriverFactory    - A browser is already opened. Closing browser and opening a new one
2025-08-20 09:58:35.360 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-20 09:58:35.362 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 20, 2025 9:58:36 上午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-20 09:58:36.204 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = b86771a5c22319e9e743a40f9e23f804
2025-08-20 09:58:36.205 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-20 09:58:36.206 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-20 09:58:36.206 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-20 09:58:36.207 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-20 09:58:36.232 DEBUG testcase.GoToGis                         - 2: navigateToUrl("https://land2.daoyidh.com/Gis")
2025-08-20 09:58:36.548 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-20 09:58:36.553 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 09:58:36.553 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-20 09:58:36.652 DEBUG testcase.LoginWithEmail                  - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_"))
2025-08-20 09:58:37.871 DEBUG testcase.LoginWithEmail                  - 2: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail"))
2025-08-20 09:58:38.405 DEBUG testcase.LoginWithEmail                  - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Email_email"), "<EMAIL>")
2025-08-20 09:58:39.210 DEBUG testcase.LoginWithEmail                  - 4: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Next"))
2025-08-20 09:58:39.507 DEBUG testcase.LoginWithEmail                  - 5: setEncryptedText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Password_password"), "0D41p6Ct1VJ35FCiyaw3Gw==")
2025-08-20 09:58:40.421 DEBUG testcase.LoginWithEmail                  - 6: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign In"))
2025-08-20 09:58:40.707 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-20 09:58:40.709 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 09:58:40.709 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-20 09:58:40.771 DEBUG testcase.SearchTextInGis                 - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button__1"))
2025-08-20 09:58:43.160 DEBUG testcase.SearchTextInGis                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input__mui-3"), "新北勢")
2025-08-20 09:58:43.774 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-20 09:58:43.779 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 09:58:43.779 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-20 09:58:43.862 DEBUG testcase.ChoosePerPage                   - 1: dropdownButton = new com.kms.katalon.core.testobject.TestObject()
2025-08-20 09:58:43.873 DEBUG testcase.ChoosePerPage                   - 2: dropdownButton.addProperty("css", EQUALS, "div[role="button"]")
2025-08-20 09:58:43.879 DEBUG testcase.ChoosePerPage                   - 3: click(dropdownButton)
2025-08-20 09:58:44.284 DEBUG testcase.ChoosePerPage                   - 4: option50 = new com.kms.katalon.core.testobject.TestObject()
2025-08-20 09:58:44.286 DEBUG testcase.ChoosePerPage                   - 5: option50.addProperty("css", EQUALS, "li.MuiMenuItem-root[data-value="50"]")
2025-08-20 09:58:44.288 DEBUG testcase.ChoosePerPage                   - 6: waitForElementVisible(option50, 5)
2025-08-20 09:58:44.828 DEBUG testcase.ChoosePerPage                   - 7: click(option50)
2025-08-20 09:58:45.253 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-20 09:58:45.259 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 09:58:45.259 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-20 09:58:45.310 DEBUG testcase.EditCoordinates                 - 1: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "148485")
2025-08-20 09:58:45.756 DEBUG testcase.EditCoordinates                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "2406567 ")
2025-08-20 09:58:46.261 DEBUG testcase.EditCoordinates                 - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "148485")
2025-08-20 09:58:46.670 DEBUG testcase.EditCoordinates                 - 4: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "2406567 ")
2025-08-20 09:58:47.102 DEBUG testcase.EditCoordinates                 - 5: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination"))
2025-08-20 09:58:47.753 DEBUG testcase.EditCoordinates                 - 6: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "148485")
2025-08-20 09:58:48.203 DEBUG testcase.EditCoordinates                 - 7: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "2406567 ")
2025-08-20 09:58:48.636 DEBUG testcase.EditCoordinates                 - 8: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination_1"))
2025-08-20 09:58:49.261 DEBUG testcase.EditCoordinates                 - 9: verifyElementVisible("Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint")
2025-08-20 09:58:49.282 ERROR c.k.katalon.core.main.TestCaseExecutor   - ❌ Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.
Reason:
groovy.lang.MissingMethodException: No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)
	at EditCoordinates.run(EditCoordinates:33)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755655109207.run(TempTestSuite1755655109207.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)

2025-08-20 09:58:49.323 ERROR c.k.katalon.core.main.TestCaseExecutor   - ❌ Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.
Reason:
groovy.lang.MissingMethodException: No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)
	at EditCoordinates.run(EditCoordinates:33)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755655109207.run(TempTestSuite1755655109207.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)

2025-08-20 09:58:49.348 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-20 09:58:49.351 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 09:58:49.351 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SaveAndVerify
2025-08-20 09:58:49.404 DEBUG testcase.SaveAndVerify                   - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_save"))
2025-08-20 09:58:49.747 DEBUG testcase.SaveAndVerify                   - 2: waitForElementVisible(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button"), 5)
2025-08-20 09:58:50.205 DEBUG testcase.SaveAndVerify                   - 3: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button"))
2025-08-20 09:58:50.540 DEBUG testcase.SaveAndVerify                   - 4: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "value"), "148485")
2025-08-20 09:58:54.367 DEBUG testcase.SaveAndVerify                   - 5: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "value"), "2406567")
2025-08-20 09:58:54.399 DEBUG testcase.SaveAndVerify                   - 6: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "value"), "148485")
2025-08-20 09:58:54.427 DEBUG testcase.SaveAndVerify                   - 7: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "value"), "2406567")
2025-08-20 09:58:54.458 DEBUG testcase.SaveAndVerify                   - 8: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination"))
2025-08-20 09:58:54.880 DEBUG testcase.SaveAndVerify                   - 9: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "value"), "148485")
2025-08-20 09:58:54.966 DEBUG testcase.SaveAndVerify                   - 10: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "value"), "2406567")
2025-08-20 09:58:54.996 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SaveAndVerify
2025-08-20 09:58:55.213 INFO  c.k.katalon.core.main.TestSuiteExecutor  - --------------------
2025-08-20 09:58:55.213 INFO  c.k.katalon.core.main.TestSuiteExecutor  - END Test Suites/Mandarin/v1/Gis
2025-08-20 09:58:55.213 INFO  c.k.katalon.core.main.TestSuiteExecutor  - ====================
