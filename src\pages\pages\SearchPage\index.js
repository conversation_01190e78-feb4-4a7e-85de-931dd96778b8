/* eslint-disable import/no-cycle */
import React, { useContext, useEffect } from "react";

// material-ui
import { Box, Grid, Stack } from "@mui/material";

// css
import "./SearchPage.scss";

//
import SearchInput from "./subComponents/SearchInput";
import ClassifyItem from "./subComponents/ClassifyItem";
import ClearAll from "./subComponents/ClearAll";
import ChipItems from "./subComponents/ChipItems";
import ResultField from "./subComponents/ResultField";
import { StoreContext } from "../../../store/StoreProvider";
import { Api, readOntoData } from "../../../api/land/Api";
import Act from "../../../store/actions";

function SearchPage() {
  const [, dispatch] = useContext(StoreContext);

  useEffect(() => {
    const apiStr = Api.getSearchColNames();
    readOntoData(apiStr).then((result) => {
      const collator = new Intl.Collator("zh-Hant", {
        usage: "sort",
        sensitivity: "base",
      });
      const groupedMap = new Map();

      result.data.forEach((element) => {
        const {
          predicate,
          field = "",
          classtype = "unknown",
          classLabel: elementClassLabel,
        } = element;
        const fieldLabel = field.trim();
        const classType = (classtype || "unknown").trim();
        const classLabel = ((elementClassLabel ?? classType) || classType).trim();
        const mapKey = `${classType}`;

        if (!groupedMap.has(mapKey)) {
          groupedMap.set(mapKey, {
            classType,
            classLabel,
            items: [],
          });
        }

        const targetGroup = groupedMap.get(mapKey);
        targetGroup.items.push({
          label: fieldLabel,
          value: predicate,
          select: true,
        });
      });

      const tmpSearchColList = Array.from(groupedMap.values()).map((group) => ({
        ...group,
        items: group.items.sort((a, b) => collator.compare(a.label, b.label)),
      }));

      tmpSearchColList.sort((a, b) => collator.compare(a.classLabel, b.classLabel));

      dispatch({
        type: Act.SET_SEARCHCOLLIST,
        payload: tmpSearchColList,
      });
    });
  }, []);

  return (
    <Box className="SearchPage">
      <Box
        className="SearchPage__layout"
        sx={{
          display: "flex",
          flexDirection: { xs: "column", md: "row" },
          alignItems: { xs: "stretch", md: "flex-start" },
          gap: { xs: 2, md: 3 },
        }}
      >
        <Box className="SearchPage__filtersWrapper">
          <ClassifyItem />
        </Box>
        <Box className="SearchPage__contentWrapper" sx={{ flex: 1, minWidth: 0 }}>
          <Stack spacing={3} className="SearchPage__content">
            <Box className="SearchPage__controls">
              <Stack
                direction={{ xs: "column", sm: "row" }}
                spacing={2}
                alignItems={{ xs: "stretch", sm: "flex-end" }}
              >
                <Box sx={{ flex: 1 }}>
                  <SearchInput />
                </Box>
                <ClearAll />
              </Stack>
              <ChipItems />
            </Box>
            <Box className="resultField">
              <Grid container justifyContent="center" sx={{ height: "100%" }}>
                <ResultField />
              </Grid>
            </Box>
          </Stack>
        </Box>
      </Box>
    </Box>
  );
}

export default SearchPage;
