// Create LandID
import { Api, createOntoData, readOntoData } from "../api/land/Api";

const createLandID = (landName, landSerialNumber) => {
  const type = "Land";
  const typePrefix = "LAD";

  const apiStr = Api.getEventIDMaxNum()
    .replace("{type}", type)
    .replace("{typePrefix}", typePrefix);

  return new Promise((resolve, reject) => {
    readOntoData(apiStr)
      .then((result) => {
        const newID = `${typePrefix}${parseInt(result.data[0].maxNum, 10) + 1}`;
        const entrySrc = {
          graph: "south",
          classType: type,
          srcId: newID,
          value: { landName, landSerialNumber },
        };
        createOntoData(Api.restfulCRUD(), entrySrc)
          .then((res) => {
            if (res.state) {
              resolve(newID);
            }
          })
          .catch((err) => reject(err));
      })
      .catch((err) => reject(err));
  });
};
export default createLandID;
