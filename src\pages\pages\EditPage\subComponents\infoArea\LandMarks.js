import React, { useContext, useEffect, useState } from "react";

// material ui
import { Grid, TextField, Typography } from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";

// react-beautiful-dnd
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

//
import { StoreContext } from "../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../utils";
import { Api, readOntoData } from "../../../../../api/land/Api";
import Act from "../../../../../store/actions";
import { landMarkPlaceHolder } from "../../common/phDes";
import {
  getColOrder,
  disableFontColor,
  disableEditFunction,
  eachRuleRequiredCheck,
  checkAllStartDate,
} from "../../common";
import evtType from "../../../../../utils/evtType";

// components
import AddButton from "./AddButton";
import RemoveButton from "./RemoveButton";
import CustomLoading from "../../../../../Component/CustomLoading/CustomLoading";
import AddStrDialog from "../PopWindow/AddStrDialog";
import CauseAutocomplete from "./subComponents/CauseAutocomplete";
import CateAutocomplete from "./subComponents/CateAutocomplete";
import StartDateInput from "./subComponents/StartDateInput";

function LandMarks() {
  const [state, dispatch] = useContext(StoreContext);
  const { landMarkInfo, infoLoading, landId } = state.edit;
  const [checkDateRepeat, setCheckDateRepeat] = useState([]); // 判斷有無重複日期

  useEffect(() => {
    if (isEmpty(landId)) {
      dispatch({
        type: Act.SET_LANDMARKINFO,
        payload: [],
      });
      return;
    }

    const apiStr = Api.getLandMarkID().replace("{landId}", landId);

    // 打開Info Loading circle
    dispatch({
      type: Act.SET_INFOLOADING,
      payload: true,
    });

    readOntoData(apiStr).then((result) => {
      if (!isEmpty(result.data)) {
        const LMIds = result.data.map((element) => element.landMarkId);
        const LMApiStr = Api.getLandMarkEvents().replace("{ids}", LMIds.join());
        readOntoData(LMApiStr).then(async (LMEvents) => {
          // console.log("LMEvents ", LMEvents);
          // LMEvents轉成下列格式:
          // [
          //   {LMK1: [{ ... }, { ... } ... { ... }]},
          //   {LMK2: [{ ... }, { ... } ... { ... }]},
          // ]

          // 先依照landMarkNumber建立空的物件
          const tmpResultData = JSON.parse(JSON.stringify(LMEvents.data));
          const idObjs = tmpResultData.reduce((acc, { landMarkId }) => {
            acc[landMarkId] = landMarkId;
            return acc;
          }, {});

          const initLMK = Object.keys(idObjs).map((key) => ({ [key]: [] }));
          // 每個LMK event 欄位順序
          let colOrder = await getColOrder("LMK");
          colOrder = colOrder.filter(({ property }) => property !== "comment");

          let tmpLMKs = JSON.parse(JSON.stringify(initLMK));
          initLMK.forEach((lmk) => {
            const lmkId = Object.keys(lmk)[0];
            colOrder.forEach((element) => {
              const findAllObj = tmpResultData.filter(
                (eventObj) =>
                  eventObj.landMarkId === lmkId &&
                  eventObj.predicate === element.property
              );
              // const findObj = tmpResultData.find(eventObj => eventObj.landMarkId === lmkId && eventObj.predicate === element.property);
              const findLMK = tmpLMKs.find(
                (obj) => Object.keys(obj)[0] === lmkId
              );
              if (findAllObj.length > 0) {
                const allValue = findAllObj.reduce(
                  (cur, next) => [...cur, next.value],
                  []
                );
                const tmpObj = JSON.parse(JSON.stringify(findAllObj[0]));
                if (element.property === "cause") {
                  tmpObj.value = allValue[0].split("，");
                } else {
                  [tmpObj.value] = allValue;
                }
                tmpObj.object = element.label; // 把欄位名稱統一改成colOrder的命名
                Object.values(findLMK)[0].push(tmpObj);
              } else {
                Object.values(findLMK)[0].push({
                  object: element.label,
                  value: element.property === "cause" ? [] : "",
                  predicate: element.property,
                  landMarkId: lmkId,
                });
              }
            });
          });

          tmpLMKs = tmpLMKs.map((element) => Object.values(element)[0]);
          // console.log("tmpLMKs ", tmpLMKs);
          // 初始化checkDateRepeat
          const tmpArr = [];
          for (let i = 0; i < tmpLMKs.length; i += 1) {
            tmpArr.push(false);
          }
          setCheckDateRepeat(tmpArr);

          // 排序Events
          const sortTmpLMKs = tmpLMKs.sort((cur, next) => {
            const findCurOd =
              cur.find((el) => el.predicate === "landMarkNumber")?.value ||
              tmpLMKs.length;
            const findNextOd =
              next.find((el) => el.predicate === "landMarkNumber")?.value ||
              tmpLMKs.length;

            return findCurOd - findNextOd;
          });
          dispatch({
            type: Act.SET_LANDMARKINFO,
            payload: sortTmpLMKs,
          });

          // 關閉Info Loading circle
          dispatch({
            type: Act.SET_INFOLOADING,
            payload: false,
          });
        });
      } else {
        dispatch({
          type: Act.SET_LANDMARKINFO,
          payload: [],
        });

        // 關閉Info Loading circle
        dispatch({
          type: Act.SET_INFOLOADING,
          payload: false,
        });
      }
    });
  }, [landId]);

  const onDragEnd = (result) => {
    const startIndex = result.source.index;
    const endIndex = result.destination.index;
    const tmpLandMarkInfo = JSON.parse(JSON.stringify(landMarkInfo));
    const tmpInfo = JSON.parse(JSON.stringify(tmpLandMarkInfo[startIndex]));
    tmpLandMarkInfo[startIndex] = tmpLandMarkInfo[endIndex];
    tmpLandMarkInfo[endIndex] = tmpInfo;

    // 變更次序互換
    const findNum1 = tmpLandMarkInfo[startIndex].find(
      (element) => element.predicate === "landMarkNumber"
    );
    const findNum2 = tmpLandMarkInfo[endIndex].find(
      (element) => element.predicate === "landMarkNumber"
    );
    const tmpNum = findNum1.value;
    findNum1.value = findNum2.value;
    findNum2.value = tmpNum;

    dispatch({
      type: Act.SET_LANDMARKINFO,
      payload: tmpLandMarkInfo,
    });
  };

  // 原因選擇"免租"、"除租"，landRent自動填為0
  const autoSetLandRent = (tmpLandMarkInfo, item) => {
    const tmpInfo = JSON.parse(JSON.stringify(tmpLandMarkInfo));
    const specialCause = [
      "免租",
      "除租",
      "荒地成免租",
      "再荒免租",
      "地目變換除租",
    ];
    const curTmpCause = Object.values(tmpInfo[item.index]).find(
      (element) => element.predicate === "cause"
    ).value;
    const tmpArr = curTmpCause.filter(
      (cause) =>
        specialCause.filter((spCuase) => cause.indexOf(spCuase) >= 0).length > 0
    );
    if (tmpArr.length > 0) {
      const findLandRent = Object.values(tmpInfo[item.index]).find(
        (element) => element.predicate === "landRent"
      );
      if (findLandRent) {
        findLandRent.value = "0";
      }
    }
    return tmpInfo;
  };

  // 原因選擇"荒地成免租，再荒免租"，landGrades"則別"0，landCategory"地目"填"荒地”
  const autoSetLandGraAndCat = (tmpLandMarkInfo, item) => {
    const tmpInfo = JSON.parse(JSON.stringify(tmpLandMarkInfo));
    const specialCause = ["荒地成免租", "再荒免租"];
    const curTmpCause = Object.values(tmpInfo[item.index]).find(
      (element) => element.predicate === "cause"
    ).value;
    const tmpArr = curTmpCause.filter(
      (cause) =>
        specialCause.filter((spCuase) => cause.indexOf(spCuase) >= 0).length > 0
    );
    if (tmpArr.length > 0) {
      const findLandGrades = Object.values(tmpInfo[item.index]).find(
        (element) => element.predicate === "landGrades"
      );
      if (findLandGrades) {
        findLandGrades.value = "0";
      }

      const findLandCategory = Object.values(tmpInfo[item.index]).find(
        (element) => element.predicate === "landCategory"
      );
      if (findLandCategory) {
        findLandCategory.value = "荒地";
      }
    }
    return tmpInfo;
  };

  const handleChange = (inputValue, item) => {
    let tmpLandMarkInfo = JSON.parse(JSON.stringify(landMarkInfo));
    const findCol = Object.values(tmpLandMarkInfo[item.index]).find(
      (element) => element.predicate === item.predicate
    );
    if (findCol) {
      findCol.value = inputValue;
      if (item.predicate === "cause") {
        tmpLandMarkInfo = autoSetLandRent(tmpLandMarkInfo, item);
        tmpLandMarkInfo = autoSetLandGraAndCat(tmpLandMarkInfo, item);
      }

      if (item.predicate === "hasStartDate") {
        setCheckDateRepeat(
          checkAllStartDate(
            tmpLandMarkInfo,
            item,
            checkDateRepeat,
            "landMarkId"
          )
        );
      }

      dispatch({
        type: Act.SET_LANDMARKINFO,
        payload: tmpLandMarkInfo,
      });
    }
  };

  return (
    <Grid item container className="LandMarks">
      <Grid
        item
        container
        flexDirection="row"
        justifyContent="space-between"
        className="header"
      >
        <Grid
          item
          xs={2}
          container
          alignItems="center"
          display="flex"
          className="headerItem"
        >
          <Typography variant="h5">土地標示變更</Typography>
        </Grid>
        {!disableEditFunction(state) && (
          <Grid
            item
            container
            xs={3}
            justifyContent="flex-end"
            className="headerItem"
          >
            <AddButton itemType="LMK" />
          </Grid>
        )}
      </Grid>
      {infoLoading ? (
        <Grid container style={{ marginTop: "20px" }}>
          <CustomLoading />
        </Grid>
      ) : (
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="droppableId">
            {(provided) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                style={{ width: "100%" }}
              >
                {landMarkInfo.map((LMInfo, index) => (
                  <Draggable
                    draggableId={`draggable_${index}`}
                    index={index}
                    key={index}
                    isDragDisabled={disableEditFunction(state)}
                  >
                    {(provided2) => (
                      <Grid
                        item
                        container
                        className="LMKItem"
                        ref={provided2.innerRef}
                        {...provided2.draggableProps}
                        {...provided2.dragHandleProps}
                      >
                        {!disableEditFunction(state) && (
                          <Grid item xs={1} className="colName">
                            <MenuIcon />
                          </Grid>
                        )}
                        <Grid
                          item
                          container
                          xs={!disableEditFunction(state) ? 9 : 12}
                          direction="column"
                          className="LMKinfoArea"
                        >
                          {Object.values(LMInfo).map((info, infoIndex) => (
                            <Grid item container key={infoIndex}>
                              <Grid item xs={3} className="labelName">
                                {info.object}
                              </Grid>
                              <Grid item xs={9}>
                                {info.predicate === "cause" && (
                                  <CauseAutocomplete
                                    info={info}
                                    handleChange={handleChange}
                                    index={index}
                                  />
                                )}
                                {info.predicate === "landCategory" && (
                                  <CateAutocomplete
                                    info={info}
                                    handleChange={handleChange}
                                    index={index}
                                  />
                                )}
                                {info.predicate === "hasStartDate" && (
                                  <StartDateInput
                                    info={info}
                                    handleChange={handleChange}
                                    index={index}
                                    type={evtType.LandMark}
                                  />
                                )}
                                {![
                                  "cause",
                                  "landCategory",
                                  "hasStartDate",
                                ].includes(info.predicate) && (
                                  <TextField
                                    disabled={
                                      disableEditFunction(state) ||
                                      !Object.keys(
                                        landMarkPlaceHolder
                                      ).includes(info.predicate)
                                    }
                                    variant="standard"
                                    fullWidth
                                    value={info.value}
                                    placeholder={
                                      landMarkPlaceHolder[info.predicate]
                                    }
                                    onChange={(e) => {
                                      const newData = { ...info, index };
                                      handleChange(e.target.value, newData);
                                    }}
                                    multiline
                                    inputProps={disableFontColor}
                                    error={
                                      eachRuleRequiredCheck(state, info, index)
                                        .check
                                    }
                                    helperText={
                                      eachRuleRequiredCheck(state, info, index)
                                        .helpText
                                    }
                                  />
                                )}
                              </Grid>
                            </Grid>
                          ))}
                        </Grid>
                        {!disableEditFunction(state) && (
                          <Grid
                            item
                            container
                            xs={1}
                            className="addOrRemoveCol"
                          >
                            <Grid item className="iconPos">
                              <RemoveButton itemType="LMK" index={index} />
                            </Grid>
                          </Grid>
                        )}
                      </Grid>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      )}
      <AddStrDialog />
    </Grid>
  );
}

export default LandMarks;
