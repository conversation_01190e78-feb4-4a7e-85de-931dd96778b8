import Act from "../actions";

const defaultWebStyle = {
  lightColor: "rgba(255,255,255,1.0)",
  darkColor: "rgba(108, 81, 81, 1.0)",
  header: {
    font: {
      color: "rgba(108,81,81,1.0)",
      lightColor: "rgba(255,255,255,1.0)",
      darkColor: "rgba(108,81,81,1.0)",
      fontSize: "1.1em",
    },
    bg: {
      color: "rgba(255,255,255,1.0)",
      lightColor: "rgba(255,255,255,1.0)",
      darkColor: "rgba(108,81,81,1.0)",
    },
  },
  main: {
    font: {
      color: "rgba(108,81,81,1.0)",
      lightColor: "rgba(255,255,255,1.0)",
      darkColor: "rgba(108,81,81,1.0)",
      fontSize: "3em",
    },
    bg: {
      color: "rgba(255,255,255,1.0)",
      lightColor: "rgba(255,255,255,1.0)",
      darkColor: "rgba(108,81,81,1.0)",
    },
  },
  footer: {
    font: {
      color: "rgba(255,255,255,1.0)",
      lightColor: "rgba(255,255,255,1.0)",
      darkColor: "rgba(108,81,81,1.0)",
      fontSize: "1.1em",
    },
    bg: {
      color: "rgba(108,81,81,1.0)",
      lightColor: "rgba(255,255,255,1.0)",
      darkColor: "rgba(108,81,81,1.0)",
    },
  },
};

const initState = {
  webStyle: defaultWebStyle,
  image: {
    desktop: {
      background: "",
      banner: "",
      logo: "",
    },
    mobile: {
      background: "",
      banner: "",
      logo: "",
    },
  },
  imageToken: {
    desktop: {
      background: "",
      banner: "",
      logo: "",
    },
    mobile: {
      background: "",
      banner: "",
      logo: "",
    },
  },
  swgJSON: {
    storage: "",
  },
  swgJSONfile: null,
  production: "true", // 預設為 true
  refreshAnonymousToken: true,
  database: null,
  loading: false, // 抓取API要顯示loading圖案
};

const mainReducer = (state = initState, action) => {
  switch (action.type) {
    case Act.SET_WEB_STYLE:
      return { ...state, webStyle: action.payload };
    case Act.SET_DEFAULT_WEB_STYLE:
      return { ...initState, webStyle: defaultWebStyle };
    case Act.SET_IMAGE:
      return { ...state, image: action.payload };
    case Act.SET_IMAGE_TOKEN:
      return { ...state, imageToken: action.payload };
    case Act.SET_IMAGE_DESK_BG:
      return {
        ...state,
        imageToken: {
          ...state.imageToken,
          desktop: {
            ...state.imageToken.desktop,
            background: action.payload,
          },
        },
      };
    case Act.SET_IMAGE_DESK_BANNER:
      return {
        ...state,
        imageToken: {
          ...state.imageToken,
          desktop: {
            ...state.imageToken.desktop,
            banner: action.payload,
          },
        },
      };
    case Act.SET_IMAGE_DESK_LOGO:
      return {
        ...state,
        imageToken: {
          ...state.imageToken,
          desktop: {
            ...state.imageToken.desktop,
            logo: action.payload,
          },
        },
      };
    case Act.SET_IMAGE_MOBILE_BG:
      return {
        ...state,
        imageToken: {
          ...state.imageToken,
          mobile: {
            ...state.imageToken.mobile,
            background: action.payload,
          },
        },
      };
    case Act.SET_IMAGE_MOBILE_BANNER:
      return {
        ...state,
        imageToken: {
          ...state.imageToken,
          mobile: {
            ...state.imageToken.mobile,
            banner: action.payload,
          },
        },
      };
    case Act.SET_IMAGE_MOBILE_LOGO:
      return {
        ...state,
        imageToken: {
          ...state.imageToken,
          mobile: {
            ...state.imageToken.mobile,
            logo: action.payload,
          },
        },
      };
    case Act.SET_SWG_JSON:
      return {
        ...state,
        swgJSON: action.payload,
      };
    case Act.SET_SWG_JSON_FILE:
      return {
        ...state,
        swgJSONfile: action.payload,
      };
    case Act.SET_PRODUCTION:
      return {
        ...state,
        production: action.payload,
      };
    case Act.REFRESH_ANONYMOUS_TOKEN:
      return {
        ...state,
        refreshAnonymousToken: action.payload,
      };
    case Act.SET_DATABASE:
      return {
        ...state,
        database: action.payload,
      };
    case Act.SET_LOADING:
      return {
        ...state,
        loading: action.payload,
      };
    default:
      return state;
  }
};

export default mainReducer;
