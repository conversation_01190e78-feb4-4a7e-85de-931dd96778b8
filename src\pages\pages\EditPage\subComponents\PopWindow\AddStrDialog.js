import React, {useContext, useState} from 'react';

import {
    Dialog,
    DialogTitle,
    DialogContent,
    TextField,
    DialogActions,
    Button
} from "@mui/material";
import {StoreContext} from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

function AddStrDialog() {
    const [state, dispatch] = useContext(StoreContext);
    const { addStrDialogParam, landMarkInfo } = state.edit;
    const [landSNStr, setLandSNStr] = useState("");

    const handleClose = () => {
        if (landSNStr.trim() !== "") {
            const { newData } = addStrDialogParam;
            // 將最後字串放到landMarkInfo的cause
            let tmpLandMarkInfo = JSON.parse(JSON.stringify(landMarkInfo));
            const findCol = Object.values(tmpLandMarkInfo[newData.index]).find(element => element.predicate === newData.predicate);
            if (findCol) {
                findCol.value.push(`${addStrDialogParam.type}${landSNStr}`);
                dispatch({
                    type: Act.SET_LANDMARKINFO,
                    payload: tmpLandMarkInfo
                });
            }
        }


        dispatch({
            type: Act.SET_ADDSTRDIALOGPARAM,
            payload: {
                open: false,
                type: "",
                newData: {}
            }
        });
        setLandSNStr("");
    }

    return (
        <Dialog
            open={addStrDialogParam.open}
            onClose={handleClose}
            fullWidth={true}
            maxWidth="md"
        >
            <DialogTitle>{`${addStrDialogParam.type}土地編號`}</DialogTitle>
            <DialogContent>
                <TextField
                    autoFocus
                    label="土地編號"
                    fullWidth
                    variant="standard"
                    onChange={(event) => {
                        setLandSNStr(event.target.value);
                    }}
                    placeholder="土地有多筆時要全部列出，以半形逗號分開"
                />
            </DialogContent>
            <DialogActions>
                <Button onClick={handleClose}>取消</Button>
                <Button onClick={handleClose}>確定</Button>
            </DialogActions>
        </Dialog>
    );
}

export default AddStrDialog;