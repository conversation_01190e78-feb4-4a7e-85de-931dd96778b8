import React, { useEffect, useState, useMemo } from "react";

// material ui
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import PersonIcon from "@mui/icons-material/Person";
import ListItemText from "@mui/material/ListItemText";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import Collapse from "@mui/material/Collapse";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import WorkHistoryIcon from "@mui/icons-material/WorkHistory";
import Pagination from "@mui/material/Pagination";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Divider from "@mui/material/Divider";

// api
import { Api, readOntoData } from "../../../../api/land/Api";

// mock data
import {
  USE_MOCK_DATA,
  generateMockUserList,
  generateMockUserRecords,
  mockApiCall,
} from "../../../../utils/mockHistoryData";

/**
 * 使用者工作紀錄元件
 * 顯示所有使用者及其工作記錄，支援雙層 pagination
 */
function UserClassify() {
  // 第一層：使用者列表狀態
  const [allUsers, setAllUsers] = useState([]); // 所有使用者
  const [userPage, setUserPage] = useState(1); // 使用者列表當前頁碼
  const USERS_PER_PAGE = 15; // 每頁顯示的使用者數量

  // 第二層：每個使用者的記錄分頁狀態 (key: userName, value: pageNumber)
  const [recordPages, setRecordPages] = useState({});
  const RECORDS_PER_PAGE = 10; // 每頁顯示的記錄數量

  // 載入使用者列表
  useEffect(() => {
    const fetchUsers = async () => {
      if (USE_MOCK_DATA) {
        // 使用假資料
        const mockData = await mockApiCall(() => generateMockUserList(50));
        const usersWithState = mockData.data.map((el) => ({
          ...el,
          open: false,
          infoData: [],
          totalRecords: 0,
        }));
        setAllUsers(usersWithState);
      } else {
        // 使用真實 API
        const apiStr = Api.getHistoryUserList();
        const res = await readOntoData(apiStr);
        const usersWithState = res.data.map((el) => ({
          ...el,
          open: false,
          infoData: [],
          totalRecords: 0,
        }));
        setAllUsers(usersWithState);
      }
    };
    fetchUsers();
  }, []);

  // 計算當前頁面要顯示的使用者
  const paginatedUsers = useMemo(() => {
    const startIndex = (userPage - 1) * USERS_PER_PAGE;
    const endIndex = startIndex + USERS_PER_PAGE;
    return allUsers.slice(startIndex, endIndex);
  }, [allUsers, userPage]);

  // 計算總頁數
  const totalUserPages = Math.ceil(allUsers.length / USERS_PER_PAGE);

  // 處理使用者點擊（展開/收合）
  const handleClick = async (userName) => {
    const tmpList = [...allUsers];
    const findObj = tmpList.find((el) => el.user === userName);
    
    if (findObj) {
      findObj.open = !findObj.open;

      if (findObj.open && findObj.infoData.length === 0) {
        // 第一次展開時載入資料
        if (USE_MOCK_DATA) {
          // 使用假資料
          const mockData = await mockApiCall(() =>
            generateMockUserRecords(userName, 35)
          );
          findObj.infoData = mockData.data;
          findObj.totalRecords = mockData.data.length;
        } else {
          // 使用真實 API
          const apiStr = Api.getHistoryUserListInfo().replace(
            "{userName}",
            userName
          );
          const tmpRes = await readOntoData(apiStr);
          findObj.infoData = tmpRes.data;
          findObj.totalRecords = tmpRes.data.length;
        }
        // 初始化該使用者的記錄頁碼
        setRecordPages((prev) => ({ ...prev, [userName]: 1 }));
      }
      
      setAllUsers(tmpList);
    }
  };

  // 處理記錄分頁變更
  const handleRecordPageChange = (userName, newPage) => {
    setRecordPages((prev) => ({ ...prev, [userName]: newPage }));
  };

  // 獲取當前頁面要顯示的記錄
  const getPaginatedRecords = (infoData, userName) => {
    const currentPage = recordPages[userName] || 1;
    const startIndex = (currentPage - 1) * RECORDS_PER_PAGE;
    const endIndex = startIndex + RECORDS_PER_PAGE;
    return infoData.slice(startIndex, endIndex);
  };

  return (
    <Box>
      {/* 統計資訊 */}
      <Box
        sx={{
          p: 2,
          mb: 2,
          backgroundColor: "var(--color-surface-alt)",
          borderRadius: 1,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="body2" color="text.secondary">
          共 <strong>{allUsers.length}</strong> 位使用者
        </Typography>
        <Typography variant="body2" color="text.secondary">
          第 {userPage} / {totalUserPages} 頁
        </Typography>
      </Box>

      {/* 使用者列表 */}
      <List component="nav">
        {paginatedUsers.map((el) => {
          const { user, open, infoData, totalRecords } = el;
          const currentRecordPage = recordPages[user] || 1;
          const totalRecordPages = Math.ceil(totalRecords / RECORDS_PER_PAGE);
          const paginatedRecords = getPaginatedRecords(infoData, user);

          return (
            <React.Fragment key={user}>
              <ListItemButton onClick={() => handleClick(user)}>
                <ListItemIcon>
                  <PersonIcon />
                </ListItemIcon>
                <ListItemText
                  primary={user}
                  secondary={
                    totalRecords > 0 ? `${totalRecords} 筆記錄` : null
                  }
                />
                {open ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton>
              <Collapse in={open}>
                <Box sx={{ backgroundColor: "var(--color-surface-alt)", p: 2 }}>
                  {paginatedRecords.length > 0 ? (
                    <>
                      <List component="div" disablePadding>
                        {paginatedRecords.map((info) => (
                          <React.Fragment key={info.hisId}>
                            <ListItem
                              sx={{ pl: 4 }}
                              style={{
                                display: "grid",
                                gridTemplateColumns: "50px 10% 10% 1fr",
                              }}
                            >
                              <ListItemIcon>
                                <WorkHistoryIcon />
                              </ListItemIcon>
                              <ListItemText primary={info.time} sx={{ mr: 2 }} />
                              <ListItemText primary={info.landStr} />
                              <ListItemText primary={info.action} />
                            </ListItem>
                            <Divider variant="inset" component="li" />
                          </React.Fragment>
                        ))}
                      </List>

                      {/* 記錄分頁控制器 */}
                      {totalRecordPages > 1 && (
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "center",
                            mt: 2,
                          }}
                        >
                          <Pagination
                            count={totalRecordPages}
                            page={currentRecordPage}
                            onChange={(event, value) =>
                              handleRecordPageChange(user, value)
                            }
                            size="small"
                            color="primary"
                          />
                        </Box>
                      )}
                    </>
                  ) : (
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ pl: 4, py: 2 }}
                    >
                      暫無工作記錄
                    </Typography>
                  )}
                </Box>
              </Collapse>
            </React.Fragment>
          );
        })}
      </List>

      {/* 使用者列表分頁控制器 */}
      {totalUserPages > 1 && (
        <Box sx={{ display: "flex", justifyContent: "center", mt: 3, mb: 2 }}>
          <Pagination
            count={totalUserPages}
            page={userPage}
            onChange={(event, value) => setUserPage(value)}
            color="primary"
          />
        </Box>
      )}

      {/* 空狀態 */}
      {allUsers.length === 0 && (
        <Box sx={{ textAlign: "center", py: 4 }}>
          <Typography variant="body1" color="text.secondary">
            暫無使用者工作記錄
          </Typography>
        </Box>
      )}
    </Box>
  );
}

export default UserClassify;
