import React, { useContext, useMemo } from "react";
import {
  Box,
  Chip,
  Checkbox,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
  Alert,
} from "@mui/material";
import PaletteIcon from '@mui/icons-material/Palette';
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import { updateUser } from "../../../../Component/Authenticate/firebase/realtimeDatabase";
import {
  collectPagesByRoles,
  getRoleMetadata,
  normalizeRoleKey,
} from "../roleConfig";
import { canManageUsers } from "../../../../utils/permissionUtils";
import { isProduction, getUserRolesArrayByEnvironment } from "../../../../utils/environmentUtils";

function ValidUser() {
  const [state, dispatch] = useContext(StoreContext);
  const { userInfo, roles } = state.authority;
  const currentUser = state.user; // 獲取當前登入用戶
  
  // 權限檢查：確認當前使用者是否有管理使用者的權限
  const hasManagePermission = canManageUsers(currentUser);
  
  // 檢查當前用戶是否為 developer（根據環境讀取正確的角色欄位）
  const isCurrentUserDeveloper = currentUser && 
    getUserRolesArrayByEnvironment(currentUser).includes('developer');

  const preparedRoleHeaders = useMemo(
    () =>
      roles.map((role) => {
        const meta = getRoleMetadata(role);
        return {
          original: role,
          normalized: normalizeRoleKey(role),
          label: meta.label || role,
          description: meta.description || "",
        };
      }),
    [roles]
  );

  const handleClick = (user, role) => {
    // 權限檢查：只有 admin 可以編輯使用者權限
    if (!hasManagePermission) {
      // eslint-disable-next-line no-alert
      alert('僅 Admin 可編輯使用者權限');
      return;
    }

    const tmpUserInfo = JSON.parse(JSON.stringify(userInfo));
    const findUser = tmpUserInfo.find((element) => element.uid === user.uid);
    if (findUser) {
      // 根據環境決定要讀寫哪個欄位
      const isProd = isProduction();
      const roleField = isProd ? 'role' : 'roleDev';
      
      // 讀取當前角色，與 Checkbox checked 邏輯保持一致
      // 測試站：如果 roleDev 為空，fallback 到 role
      const currentRoleString = isProd 
        ? (findUser.role || '') 
        : (findUser.roleDev || findUser.role || '');
      
      const tmpRole = currentRoleString.split(",");
      const normalizedRole = normalizeRoleKey(role);
      if (currentRoleString.indexOf(normalizedRole) >= 0) {
        const tmpIndex = tmpRole.indexOf(normalizedRole);
        tmpRole.splice(tmpIndex, 1);
      } else {
        tmpRole.push(normalizedRole);
      }
      const updatedRoleString = tmpRole.filter(Boolean).join();
      
      // 根據環境只更新對應的欄位
      // 正式站: 只更新 role
      // 測試站: 只更新 roleDev
      findUser[roleField] = updatedRoleString;
      
      // 如果是測試站且 roleDev 原本是空的，需要確保更新 roleDev
      if (!isProd && !findUser.roleDev) {
        findUser.roleDev = updatedRoleString;
      }
      
      dispatch({
        type: Act.SET_USERINFO,
        payload: tmpUserInfo,
      });

      updateUser(findUser.uid, findUser);
    }
  };

  return (
    <>
      {/* 權限提示 */}
      {!hasManagePermission && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          <strong>唯讀模式：</strong>僅 Admin 可編輯使用者權限。您目前只能檢視權限設定。
        </Alert>
      )}

      {/* 權限層級說明 */}
      <Box sx={{ 
        mb: 2, 
        p: 2, 
        backgroundColor: 'var(--color-surface-alt)', 
        borderRadius: 2,
        border: '1px solid var(--color-border)'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <PaletteIcon sx={{ fontSize: '1.25rem', color: 'var(--color-primary)' }} />
          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
            權限層級色彩說明：
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip 
              label="Reader 以上" 
              size="small" 
              sx={{ 
                backgroundColor: 'var(--color-surface-alt)', 
                color: 'var(--color-text-primary)',
                border: '1px solid var(--color-border)',
                '& span': {
                  color: 'var(--color-text-primary)',
                },
              }} 
            />
            <Typography variant="caption" color="text.secondary">→</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip 
              label="Editor 以上" 
              size="small" 
              sx={{ 
                backgroundColor: 'var(--color-primary)', 
                color: '#fff',
                '& span': {
                  color: '#fff',
                },
              }} 
            />
            <Typography variant="caption" color="text.secondary">→</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip 
              label="僅 Admin/Dev" 
              size="small" 
              sx={{ 
                backgroundColor: 'var(--color-danger)', 
                color: '#fff',
                '& span': {
                  color: '#fff',
                },
              }} 
            />
          </Box>
        </Box>
      </Box>

      <TableContainer component={Paper} className="ValidUser">
        <Table>
        <TableHead className="tableHead">
          <TableRow>
            <TableCell align="left">使用者</TableCell>
            <TableCell align="left">可使用頁面 / 功能</TableCell>
            {preparedRoleHeaders.map((header) => (
              <TableCell key={header.normalized} align="center">
                {header.description ? (
                  <Tooltip title={header.description} arrow>
                    <span>{header.label}</span>
                  </Tooltip>
                ) : (
                  header.label
                )}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {userInfo
            .filter((info) => {
              let exist = false;
              roles.forEach((role) => {
                if (info.role.indexOf(role.toLowerCase()) >= 0) {
                  exist = true;
                }
              });
              return exist;
            })
            .map((user, index) => {
              // 根據環境讀取對應的角色欄位
              const isProd = isProduction();
              const currentRoleString = isProd ? (user.role || '') : (user.roleDev || user.role || '');
              
              const roleKeys = currentRoleString
                .split(",")
                .map((item) => normalizeRoleKey(item))
                .filter(Boolean);
              const pages = collectPagesByRoles(roleKeys);

              return (
                <TableRow key={index}>
                  <TableCell align="left">
                    <Typography className="AuthorityPage__userName">
                      {user.displayName}
                    </Typography>
                    {user.email && (
                      <Typography variant="body2" color="text.secondary">
                        {user.email}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell align="left">
                    {pages.length > 0 ? (
                      <Box className="AuthorityPage__featureList">
                        {(() => {
                          // 重新定義權限層級和顏色系統（使用全域配色）
                          const roleHierarchy = {
                            admin: { 
                              level: 4, 
                              label: 'Admin', 
                              bgColor: 'var(--color-danger)', // 使用危險色表示最高權限
                              textColor: '#fff',
                              hoverColor: '#c62828'
                            },
                            developer: { 
                              level: 4, 
                              label: 'Developer', 
                              bgColor: 'var(--color-danger)', 
                              textColor: '#fff',
                              hoverColor: '#c62828'
                            },
                            editor: { 
                              level: 2, 
                              label: 'Editor', 
                              bgColor: 'var(--color-primary)', // 使用主色
                              textColor: '#fff',
                              hoverColor: 'var(--color-primary-hover)'
                            },
                            reader: { 
                              level: 1, 
                              label: 'Reader', 
                              bgColor: 'var(--color-surface-alt)', // 使用次要背景色
                              textColor: 'var(--color-text-primary)',
                              hoverColor: 'var(--color-border-strong)'
                            },
                            anonymous: { 
                              level: 0, 
                              label: 'Anonymous', 
                              bgColor: 'var(--color-text-muted)', 
                              textColor: '#fff',
                              hoverColor: '#9e9e9e'
                            }
                          };

                          // 獲取頁面的最低要求權限層級（最重要的改動）
                          const getPageMinLevel = (page) => Math.min(...page.via.map(roleLabel => {
                            const roleKey = roleLabel.toLowerCase();
                            return roleHierarchy[roleKey]?.level || 999;
                          }));

                          // 創建頁面與最低權限的映射
                          const uniquePages = pages.reduce((acc, page) => {
                            const minLevel = getPageMinLevel(page);
                            const roleKey = Object.keys(roleHierarchy).find(
                              key => roleHierarchy[key].level === minLevel
                            );
                            
                            if (roleKey && !acc.find(p => p.key === page.key)) {
                              acc.push({
                                ...page,
                                minRoleKey: roleKey,
                                minLevel
                              });
                            }
                            return acc;
                          }, []);

                          // 按最低權限層級排序並渲染
                          return uniquePages
                            .sort((a, b) => b.minLevel - a.minLevel) // 高權限在前
                            .map((page) => {
                              const roleConfig = roleHierarchy[page.minRoleKey];
                              const descriptionText = page.description || page.label;
                              let levelText = 'All Users';
                              if (roleConfig.level === 4) levelText = '僅 Admin/Dev';
                              else if (roleConfig.level === 2) levelText = 'Editor 以上';
                              else if (roleConfig.level === 1) levelText = 'Reader 以上';
                              else if (roleConfig.level === 0) levelText = '公開功能';
                              const viaText = `（${levelText}）`;
                              const tooltipTitle = `${descriptionText}${viaText}`;

                              return (
                                <Tooltip key={page.key} arrow title={tooltipTitle}>
                                  <span>
                                    <Chip 
                                      label={page.label} 
                                      size="small" 
                                      sx={{
                                        backgroundColor: roleConfig.bgColor,
                                        color: roleConfig.textColor,
                                        '& span': {
                                          color: roleConfig.textColor,
                                        },
                                        fontWeight: roleConfig.level === 4 ? 600 : 500,
                                        border: roleConfig.level === 1 ? '1px solid var(--color-border)' : 'none',
                                        '& .MuiChip-label': {
                                          fontSize: { xs: '0.75rem', sm: '0.8125rem' }
                                        },
                                        '&:hover': {
                                          backgroundColor: roleConfig.hoverColor,
                                          transform: 'translateY(-1px)',
                                          boxShadow: 'var(--shadow-surface)'
                                        }
                                      }}
                                    />
                                  </span>
                                </Tooltip>
                              );
                            });
                        })()}
                      </Box>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        尚無對應頁面或功能
                      </Typography>
                    )}
                  </TableCell>
                  {preparedRoleHeaders.map((header) => {
                    // 計算 tooltip 文字
                    let tooltipTitle = "";
                    if (!hasManagePermission) {
                      tooltipTitle = "僅 Admin 可編輯使用者權限";
                    } else if (header.normalized === 'developer' && !isCurrentUserDeveloper) {
                      tooltipTitle = "只有 Developer 用戶可以編輯 Developer 權限";
                    }
                    
                    return (
                    <TableCell key={header.normalized} align="center">
                      <Tooltip 
                        title={tooltipTitle}
                        arrow
                      >
                        <span>
                          <Checkbox
                            color="default"
                            checked={(() => {
                              const isProd = isProduction();
                              const currentRoleString = isProd ? (user.role || '') : (user.roleDev || user.role || '');
                              return currentRoleString.indexOf(header.normalized) >= 0;
                            })()}
                            onClick={() => handleClick(user, header.original)}
                            disabled={
                              !hasManagePermission || 
                              (header.normalized === 'developer' && !isCurrentUserDeveloper)
                            }
                            sx={{
                              '&.Mui-disabled': {
                                color: 'action.disabled',
                                opacity: 0.5,
                              },
                              '&.Mui-disabled.Mui-checked': {
                                color: 'action.disabled',
                              }
                            }}
                          />
                        </span>
                      </Tooltip>
                    </TableCell>
                    );
                  })}
                </TableRow>
              );
            })}
        </TableBody>
      </Table>
    </TableContainer>
    </>
  );
}

export default ValidUser;
