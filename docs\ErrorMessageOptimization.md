# 錯誤訊息優化說明

## 🎯 優化目標

原本的錯誤訊息：
```
5.20220811土地臺帳新格式_庭羽_範圍：屏東1_Gary整理.xlsx - 表單: 完成進度 檢查結果有誤，請參照下列資訊修改資料，請修改表格資料後重新執行上傳。 標頭順序有誤，請依照固定標頭順序填寫。。 標頭內容有誤。。 表單: 屏東1-庭羽 檢查結果有誤，請參照下列資訊修改資料，請修改表格資料後重新執行上傳。 標頭順序有誤，請依照固定標頭順序填寫。。 標頭內容有誤。。 H4, [3...
```

**問題**：
- ❌ 看不出具體哪裡錯誤
- ❌ 不知道如何修正
- ❌ 訊息太長且混亂
- ❌ 缺少明確的指引

---

## ✅ 優化後的錯誤訊息

### 1. 結構化錯誤資訊

現在系統會顯示：

#### 📊 總覽
```
驗證失敗
發現 5 個錯誤，請修正後重新上傳
💡 提示：請查看下方詳細錯誤資訊，或下載「範本」參考正確格式
```

#### 📋 表頭格式錯誤（可展開）
```
┌─ 表頭格式錯誤 [5 個錯誤]
│
├─ 錯誤 1: 第 3 欄：預期為「Land-->boxNumber」，實際為「Land->boxNumber」
│  欄位位置：第 3 欄
│  預期：Land-->boxNumber
│  實際：Land->boxNumber
│  💡 建議：請檢查箭頭符號，應為「-->」而非「->」
│
├─ 錯誤 2: 缺少 2 個必要欄位
│  缺少的欄位：
│  ┌────────────────────────────────┐
│  │ 土地標示起始日期                │
│  │ 土地權利起始日期                │
│  └────────────────────────────────┘
│
├─ 錯誤 3: 欄位數量不足：預期 21 個欄位，實際只有 19 個
│
└─ 💡 解決方法：請下載「範本」或「範例檔案」，確保表頭格式完全正確
```

#### 📊 資料內容錯誤（可展開）
```
┌─ 資料內容錯誤 [3 個錯誤]
│
├─ [工作表: 屏東1-庭羽] [第 5 列] [來源]
│  來源欄位格式錯誤：只能填寫 0-5 之間的整數
│  當前值：7
│
├─ [工作表: 屏東1-庭羽] [第 8 列] [典權]
│  典權欄位格式錯誤：只能填寫 Y 或 N
│  當前值：yes
│
└─ [工作表: 屏東1-庭羽] [第 12 列] [土地標示起始日期]
│  日期格式錯誤：必須為 YYYY-MM-DD 格式
│  當前值：2022/08/15
```

---

## 🎨 視覺化改進

### 使用 Accordion（手風琴）分類
- **表頭格式錯誤**：紅色邊框，ViewColumnIcon 圖標
- **資料內容錯誤**：紅色邊框，TableChartIcon 圖標

### 使用 Chip 標籤
- 🏷️ 工作表名稱：灰色 Chip
- 🏷️ 列號：藍色 Chip
- 🏷️ 欄位名稱：輪廓型 Chip
- 🏷️ 錯誤數量：紅色 Chip

### 使用不同顏色
- 🔴 錯誤（error）：紅色，ErrorIcon
- 🟡 警告（warning）：黃色，WarningIcon
- 🔵 提示（info）：藍色，InfoIcon

---

## 📝 具體錯誤訊息範例

### 表頭順序錯誤
```
第 5 欄：預期為「Land-->landName」，實際為「Land-->abstract」
欄位位置：第 5 欄
預期：Land-->landName
實際：Land-->abstract
```

### 表頭內容錯誤
```
第 3 欄的欄位 ID「Land->boxNumber」不在標準欄位列表中
💡 建議：您是否要輸入「Land-->boxNumber」？
```

### 缺少必要欄位
```
缺少 3 個必要欄位
缺少的欄位：
┌─────────────────────────────┐
│ 典藏地 (Land-->collectionPlace) │
│ 經手人 (Land-->operator)        │
│ 箱號 (Land-->boxNumber)        │
└─────────────────────────────┘
```

### 欄位數量不符
```
欄位數量不足：預期 21 個欄位，實際只有 18 個
```

---

## 🔧 使用者操作流程

### 原本流程
1. 上傳檔案
2. 看到一大串錯誤訊息 😵
3. 不知道怎麼改
4. 放棄或聯繫管理員

### 優化後流程
1. 上傳檔案
2. 看到清楚的錯誤統計：「發現 5 個錯誤」
3. 點開「表頭格式錯誤」查看詳情
4. 看到：
   - 第 3 欄：箭頭符號錯誤（`->` 應改為 `-->`）
   - 缺少 2 個欄位：土地標示起始日期、土地權利起始日期
5. 根據提示修正 Excel
6. 或直接下載「範本」重新填寫
7. 重新上傳 ✅

---

## 💡 智慧提示功能

### 1. 模糊比對建議
當使用者輸入錯誤的欄位名稱時，系統會嘗試猜測：

```
第 8 欄的欄位 ID「Land->source」不在標準欄位列表中
💡 建議：您是否要輸入「Land-->source」？
```

### 2. 常見錯誤提示
```
來源欄位格式錯誤：只能填寫 0-5 之間的整數
當前值：6

💡 來源代碼說明：
0: 原始文件
1: 影印本
2: 掃描檔
3: 轉錄本
4: 二手資料
5: 其他
```

### 3. 範本下載提示
每個錯誤區塊底部都有：
```
💡 解決方法：請下載「範本」或「範例檔案」，確保表頭格式完全正確
```

---

## 🎯 錯誤限制

為了避免訊息過長，資料內容錯誤最多顯示 **50 個**：

```
顯示前 50 個錯誤，總共 127 個錯誤。
請先修正上述錯誤後重新上傳。
```

---

## 📱 響應式設計

- 在手機上，Chip 標籤會自動換行
- Accordion 內容會適應螢幕寬度
- 字體大小會根據裝置調整

---

## 🚀 技術實現

### 檔案結構
```
ImportDataPage/
├── index.js                    # 主頁面
├── subComponents/
│   ├── ValidationErrors.js     # 錯誤訊息元件（新）
│   └── ValidationErrors.scss   # 錯誤訊息樣式（新）
└── utils/
    └── excelHeader/
        ├── checkHeaderOrder.js # 改進：回傳結構化錯誤
        └── checkHeaderCell.js  # 改進：回傳結構化錯誤
```

### 錯誤資料結構
```javascript
{
  type: 'header_order' | 'header_content' | 'data_error',
  sheetName: '屏東1-庭羽',
  columnIndex: 3,
  rowNumber: 5,
  columnName: '來源',
  expected: 'Land-->source',
  actual: 'Land->source',
  message: '第 3 欄：預期為「Land-->source」，實際為「Land->source」',
  suggestion: '您是否要輸入「Land-->source」？',
  severity: 'error' | 'warning',
  missing: [...],  // 缺少的欄位
  extra: [...]     // 多餘的欄位
}
```

---

## ✅ 優化成果

### 改進前 vs 改進後

| 項目 | 改進前 | 改進後 |
|------|--------|--------|
| 錯誤可讀性 | ❌ 混亂 | ✅ 清晰 |
| 具體位置 | ❌ 不明確 | ✅ 精確到列和欄 |
| 修正建議 | ❌ 無 | ✅ 提供智慧建議 |
| 視覺呈現 | ❌ 純文字 | ✅ 分類、圖標、顏色 |
| 錯誤分類 | ❌ 無 | ✅ 表頭/資料分開 |
| 使用者友善度 | ❌ 低 | ✅ 高 |

---

**最後更新**: 2025-10-01  
**版本**: 2.0.0
