import React, { useState } from "react";

// material ui
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";

// scss
import "./HistoryPage.scss";

// components
import DateClassify from "./subComponents/DateClassify";
import UserClassify from "./subComponents/UserClassify";

function HistoryPage() {
  const tabList = [
    { label: "使用者工作紀錄", component: UserClassify },
    { label: "歷史紀錄", component: DateClassify },
  ];

  const [value, setValue] = useState(0);

  return (
    <div className="mainBox_shadowMain">
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <Tabs
          value={value}
          onChange={(evt, idx) => setValue(idx)}
          variant="scrollable"
          indicatorColor="primary"
        >
          {tabList.map((item, index) => (
            <Tab label={item.label} key={index} />
          ))}
        </Tabs>
      </Box>
      {tabList
        .filter((item, index) => value === index)
        .map((item, index) => (
          <item.component key={index} />
        ))}
    </div>
  );
}

export default HistoryPage;
