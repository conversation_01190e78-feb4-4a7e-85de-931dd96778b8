<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Gis" time="22.646" tests="6" failures="0" errors="1">
   <testsuite name="Gis" tests="6" failures="0" errors="1" time="22.646" skipped="0" timestamp="2025-08-20T01:58:32.350Z" hostname="saw63 - host.docker.internal" id="Test Suites/Mandarin/v1/Gis">
      <properties>
         <property name="deviceName" value=""/>
         <property name="devicePlatform"/>
         <property name="logFolder" value="C:\\project\\land-web\\katalon\\Reports\\20250820_095829\\Mandarin\\v1\\Gis\\20250820_095829"/>
         <property name="logFiles" value="C:\\project\\land-web\\katalon\\Reports\\20250820_095829\\Mandarin\\v1\\Gis\\20250820_095829\\console0.log, C:\\project\\land-web\\katalon\\Reports\\20250820_095829\\Mandarin\\v1\\Gis\\20250820_095829\\execution0.log"/>
         <property name="attachments" value=""/>
         <property name="hostName" value="saw63 - host.docker.internal"/>
         <property name="os" value="Windows 11 64bit"/>
         <property name="katalonVersion" value="********"/>
         <property name="browser" value="Chrome 138.0.7204.185"/>
         <property name="userFullName" value="Lin Kai Yee"/>
         <property name="hostAddress" value="************"/>
         <property name="sessionId" value="b86771a5c22319e9e743a40f9e23f804"/>
         <property name="projectName" value="katalon"/>
         <property name="seleniumVersion" value="4.28.1"/>
         <property name="proxyInformation" value="ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList=&quot;&quot;, isApplyToDesiredCapabilities=true }"/>
         <property name="platform" value="Windows 11"/>
      </properties>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/GoToGis" time="1.483" classname="Test Cases/Mandarin/v1_web/Gis/GoToGis" status="PASSED">
         <system-out><![CDATA[20-08-2025T09:58:35 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/GoToGis: Test Cases/Mandarin/v1_web/Gis/GoToGis

20-08-2025T09:58:35 - [TEST_STEP][PASSED] - openBrowser(""): Browser is opened with url: ''

20-08-2025T09:58:35 - [MESSAGE][WARNING] - A browser is already opened. Closing browser and opening a new one

20-08-2025T09:58:35 - [MESSAGE][INFO] - Starting 'Chrome' driver

20-08-2025T09:58:35 - [MESSAGE][INFO] - Action delay is set to 0 milliseconds

20-08-2025T09:58:36 - [MESSAGE][PASSED] - Browser is opened with url: ''

20-08-2025T09:58:36 - [TEST_STEP][PASSED] - navigateToUrl("https://land2.daoyidh.com/Gis"): Navigate to 'https://land2.daoyidh.com/Gis' successfully

20-08-2025T09:58:36 - [MESSAGE][PASSED] - Navigate to 'https://land2.daoyidh.com/Gis' successfully]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/LoginWithEmail" time="4.154" classname="Test Cases/Mandarin/v1_web/Gis/LoginWithEmail" status="PASSED">
         <system-out><![CDATA[20-08-2025T09:58:36 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/LoginWithEmail: Test Cases/Mandarin/v1_web/Gis/LoginWithEmail

20-08-2025T09:58:36 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_' is clicked on

20-08-2025T09:58:37 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_' is clicked on

20-08-2025T09:58:37 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail' is clicked on

20-08-2025T09:58:38 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail' is clicked on

20-08-2025T09:58:38 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Email_email"), "<EMAIL>"): Text '<EMAIL>' is set on object 'Object Repository/Mandarin/v1_web/Gis/input_Email_email'

20-08-2025T09:58:39 - [MESSAGE][PASSED] - Text '<EMAIL>' is set on object 'Object Repository/Mandarin/v1_web/Gis/input_Email_email'

20-08-2025T09:58:39 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Next")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_Next' is clicked on

20-08-2025T09:58:39 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_Next' is clicked on

20-08-2025T09:58:39 - [TEST_STEP][PASSED] - setEncryptedText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Password_password"), "0D41p6Ct1VJ35FCiyaw3Gw=="): Text ****** has been set on object 'Object Repository/Mandarin/v1_web/Gis/input_Password_password'

20-08-2025T09:58:40 - [MESSAGE][PASSED] - Text ****** has been set on object 'Object Repository/Mandarin/v1_web/Gis/input_Password_password'

20-08-2025T09:58:40 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign In")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign In' is clicked on

20-08-2025T09:58:40 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_Sign In' is clicked on]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/SearchTextInGis" time="3.066" classname="Test Cases/Mandarin/v1_web/Gis/SearchTextInGis" status="PASSED">
         <system-out><![CDATA[20-08-2025T09:58:40 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/SearchTextInGis: Test Cases/Mandarin/v1_web/Gis/SearchTextInGis

20-08-2025T09:58:40 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button__1")): Object: 'Object Repository/Mandarin/v1_web/Gis/button__1' is clicked on

20-08-2025T09:58:43 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button__1' is clicked on

20-08-2025T09:58:43 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input__mui-3"), "新北勢"): Text '新北勢' is set on object 'Object Repository/Mandarin/v1_web/Gis/input__mui-3'

20-08-2025T09:58:43 - [MESSAGE][PASSED] - Text '新北勢' is set on object 'Object Repository/Mandarin/v1_web/Gis/input__mui-3']]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/ChoosePerPage" time="1.474" classname="Test Cases/Mandarin/v1_web/Gis/ChoosePerPage" status="PASSED">
         <system-out><![CDATA[20-08-2025T09:58:43 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/ChoosePerPage: Test Cases/Mandarin/v1_web/Gis/ChoosePerPage

20-08-2025T09:58:43 - [TEST_STEP][PASSED] - dropdownButton = new com.kms.katalon.core.testobject.TestObject(): null

20-08-2025T09:58:43 - [TEST_STEP][PASSED] - dropdownButton.addProperty("css", EQUALS, "div[role="button"]"): null

20-08-2025T09:58:43 - [TEST_STEP][PASSED] - click(dropdownButton): Object: '' is clicked on

20-08-2025T09:58:44 - [MESSAGE][PASSED] - Object: '' is clicked on

20-08-2025T09:58:44 - [TEST_STEP][PASSED] - option50 = new com.kms.katalon.core.testobject.TestObject(): null

20-08-2025T09:58:44 - [TEST_STEP][PASSED] - option50.addProperty("css", EQUALS, "li.MuiMenuItem-root[data-value="50"]"): null

20-08-2025T09:58:44 - [TEST_STEP][PASSED] - waitForElementVisible(option50, 5): Object '' is visible

20-08-2025T09:58:44 - [MESSAGE][PASSED] - Object '' is visible

20-08-2025T09:58:44 - [TEST_STEP][PASSED] - click(option50): Object: '' is clicked on

20-08-2025T09:58:45 - [MESSAGE][PASSED] - Object: '' is clicked on]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/EditCoordinates" time="4.089" classname="Test Cases/Mandarin/v1_web/Gis/EditCoordinates" status="ERROR">
         <error type="ERROR" message="Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.&#xa;Reason:&#xa;groovy.lang.MissingMethodException: No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]&#xa;Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)&#xa;	at EditCoordinates.run(EditCoordinates:33)&#xa;	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)&#xa;	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)&#xa;	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)&#xa;	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)&#xa;	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)&#xa;	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)&#xa;	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)&#xa;	at TempTestSuite1755655109207.run(TempTestSuite1755655109207.groovy:35)&#xa;	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)&#xa;	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)&#xa;	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)&#xa;"/>
         <system-out><![CDATA[20-08-2025T09:58:45 - [TEST_CASE][ERROR] - Test Cases/Mandarin/v1_web/Gis/EditCoordinates: Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.
Reason:
groovy.lang.MissingMethodException: No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)
	at EditCoordinates.run(EditCoordinates:33)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755655109207.run(TempTestSuite1755655109207.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)


20-08-2025T09:58:45 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "148485"): Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'

20-08-2025T09:58:45 - [MESSAGE][PASSED] - Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x'

20-08-2025T09:58:45 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "2406567 "): Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y'

20-08-2025T09:58:46 - [MESSAGE][PASSED] - Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y'

20-08-2025T09:58:46 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "148485"): Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x'

20-08-2025T09:58:46 - [MESSAGE][PASSED] - Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x'

20-08-2025T09:58:46 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "2406567 "): Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y'

20-08-2025T09:58:47 - [MESSAGE][PASSED] - Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y'

20-08-2025T09:58:47 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination")): Object: 'Object Repository/Mandarin/v1_web/Gis/pagination' is clicked on

20-08-2025T09:58:47 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/pagination' is clicked on

20-08-2025T09:58:47 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "148485"): Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x'

20-08-2025T09:58:48 - [MESSAGE][PASSED] - Text '148485' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x'

20-08-2025T09:58:48 - [TEST_STEP][PASSED] - setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "2406567 "): Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y'

20-08-2025T09:58:48 - [MESSAGE][PASSED] - Text '2406567 ' is set on object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y'

20-08-2025T09:58:48 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination_1")): Object: 'Object Repository/Mandarin/v1_web/Gis/pagination_1' is clicked on

20-08-2025T09:58:49 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/pagination_1' is clicked on

20-08-2025T09:58:49 - [TEST_STEP][ERROR] - verifyElementVisible("Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint"): Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.
Reason:
groovy.lang.MissingMethodException: No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)
	at EditCoordinates.run(EditCoordinates:33)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755655109207.run(TempTestSuite1755655109207.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)


20-08-2025T09:58:49 - [MESSAGE][ERROR] - Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.
Reason:
groovy.lang.MissingMethodException: No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)
	at EditCoordinates.run(EditCoordinates:33)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755655109207.run(TempTestSuite1755655109207.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)]]></system-out>
         <system-err><![CDATA[20-08-2025T09:58:45 - [TEST_CASE][ERROR] - Test Cases/Mandarin/v1_web/Gis/EditCoordinates: Test Cases/Mandarin/v1_web/Gis/EditCoordinates FAILED.
Reason:
groovy.lang.MissingMethodException: No signature of method: static com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords.verifyElementVisible() is applicable for argument types: (String) values: [Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint]
Possible solutions: verifyElementVisible(com.kms.katalon.core.testobject.TestObject), verifyElementVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject), verifyElementNotVisible(com.kms.katalon.core.testobject.TestObject, com.kms.katalon.core.model.FailureHandling)
	at EditCoordinates.run(EditCoordinates:33)
	at com.kms.katalon.core.main.ScriptEngine.run(ScriptEngine.java:194)
	at com.kms.katalon.core.main.ScriptEngine.runScriptAsRawText(ScriptEngine.java:119)
	at com.kms.katalon.core.main.TestCaseExecutor.runScript(TestCaseExecutor.java:485)
	at com.kms.katalon.core.main.TestCaseExecutor.doExecute(TestCaseExecutor.java:476)
	at com.kms.katalon.core.main.TestCaseExecutor.processExecutionPhase(TestCaseExecutor.java:455)
	at com.kms.katalon.core.main.TestCaseExecutor.accessMainPhase(TestCaseExecutor.java:447)
	at com.kms.katalon.core.main.TestCaseExecutor.execute(TestCaseExecutor.java:321)
	at com.kms.katalon.core.common.CommonExecutor.accessTestCaseMainPhase(CommonExecutor.java:71)
	at com.kms.katalon.core.main.TestSuiteExecutor.accessTestSuiteMainPhase(TestSuiteExecutor.java:160)
	at com.kms.katalon.core.main.TestSuiteExecutor.execute(TestSuiteExecutor.java:107)
	at com.kms.katalon.core.main.TestCaseMain.startTestSuite(TestCaseMain.java:195)
	at TempTestSuite1755655109207.run(TempTestSuite1755655109207.groovy:35)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)]]></system-err>
      </testcase>
      <testcase name="Test Cases/Mandarin/v1_web/Gis/SaveAndVerify" time="5.645" classname="Test Cases/Mandarin/v1_web/Gis/SaveAndVerify" status="PASSED">
         <system-out><![CDATA[20-08-2025T09:58:49 - [TEST_CASE][PASSED] - Test Cases/Mandarin/v1_web/Gis/SaveAndVerify: Test Cases/Mandarin/v1_web/Gis/SaveAndVerify

20-08-2025T09:58:49 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_save")): Object: 'Object Repository/Mandarin/v1_web/Gis/button_save' is clicked on

20-08-2025T09:58:49 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/button_save' is clicked on

20-08-2025T09:58:49 - [TEST_STEP][PASSED] - waitForElementVisible(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button"), 5): Object 'Object Repository/Mandarin/v1_web/Gis/save_modal_save_button' is visible

20-08-2025T09:58:50 - [MESSAGE][PASSED] - Object 'Object Repository/Mandarin/v1_web/Gis/save_modal_save_button' is visible

20-08-2025T09:58:50 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button")): Object: 'Object Repository/Mandarin/v1_web/Gis/save_modal_save_button' is clicked on

20-08-2025T09:58:50 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/save_modal_save_button' is clicked on

20-08-2025T09:58:50 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "value"), "148485"): Actual object '148485' and expected object '148485' are equal

20-08-2025T09:58:54 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x' is: '148485'

20-08-2025T09:58:54 - [MESSAGE][PASSED] - Actual object '148485' and expected object '148485' are equal

20-08-2025T09:58:54 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "value"), "2406567"): Actual object '2406567' and expected object '2406567' are equal

20-08-2025T09:58:54 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y' is: '2406567'

20-08-2025T09:58:54 - [MESSAGE][PASSED] - Actual object '2406567' and expected object '2406567' are equal

20-08-2025T09:58:54 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "value"), "148485"): Actual object '148485' and expected object '148485' are equal

20-08-2025T09:58:54 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x' is: '148485'

20-08-2025T09:58:54 - [MESSAGE][PASSED] - Actual object '148485' and expected object '148485' are equal

20-08-2025T09:58:54 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "value"), "2406567"): Actual object '2406567' and expected object '2406567' are equal

20-08-2025T09:58:54 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y' is: '2406567'

20-08-2025T09:58:54 - [MESSAGE][PASSED] - Actual object '2406567' and expected object '2406567' are equal

20-08-2025T09:58:54 - [TEST_STEP][PASSED] - click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination")): Object: 'Object Repository/Mandarin/v1_web/Gis/pagination' is clicked on

20-08-2025T09:58:54 - [MESSAGE][PASSED] - Object: 'Object Repository/Mandarin/v1_web/Gis/pagination' is clicked on

20-08-2025T09:58:54 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "value"), "148485"): Actual object '148485' and expected object '148485' are equal

20-08-2025T09:58:54 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x' is: '148485'

20-08-2025T09:58:54 - [MESSAGE][PASSED] - Actual object '148485' and expected object '148485' are equal

20-08-2025T09:58:54 - [TEST_STEP][PASSED] - verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "value"), "2406567"): Actual object '2406567' and expected object '2406567' are equal

20-08-2025T09:58:54 - [MESSAGE][PASSED] - Attribute 'value' of object 'Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y' is: '2406567'

20-08-2025T09:58:54 - [MESSAGE][PASSED] - Actual object '2406567' and expected object '2406567' are equal]]></system-out>
         <system-err><![CDATA[]]></system-err>
      </testcase>
      <system-out><![CDATA[20-08-2025T09:58:32 - [TEST_STEP][PASSED] - Start listener action : beforeSuite: Current window maximized

20-08-2025T09:58:32 - [TEST_STEP][PASSED] - openBrowser(""): Browser is opened with url: ''

20-08-2025T09:58:32 - [MESSAGE][INFO] - Starting 'Chrome' driver

20-08-2025T09:58:32 - [MESSAGE][INFO] - Action delay is set to 0 milliseconds

20-08-2025T09:58:34 - [MESSAGE][PASSED] - Browser is opened with url: ''

20-08-2025T09:58:34 - [TEST_STEP][PASSED] - maximizeWindow(): Current window maximized

20-08-2025T09:58:34 - [MESSAGE][PASSED] - Current window maximized

20-08-2025T09:58:32 - [TEST_SUITE][ERROR] - Gis: null

20-08-2025T09:58:54 - [TEST_STEP][PASSED] - Start listener action : afterSuite: Browser is closed

20-08-2025T09:58:54 - [TEST_STEP][PASSED] - closeBrowser(): Browser is closed

20-08-2025T09:58:55 - [MESSAGE][PASSED] - Browser is closed]]></system-out>
      <system-err><![CDATA[20-08-2025T09:58:32 - [TEST_SUITE][ERROR] - Gis: null]]></system-err>
   </testsuite>
</testsuites>
