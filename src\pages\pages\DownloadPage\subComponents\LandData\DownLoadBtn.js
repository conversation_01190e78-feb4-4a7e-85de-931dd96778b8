import React, { useContext, useState, useEffect } from "react";

// material ui
import Stack from "@mui/material/Stack";
import Button from "@mui/material/Button";
import CircularProgress from "@mui/material/CircularProgress";

// components
import ExcelMultiSheet from "../../../../../Component/ExcelMultiSheet/ExcelMultiSheet";

// store
import { StoreContext } from "../../../../../store/StoreProvider";

// utils
import { readOntoData } from "../../../../../api/land/Api";
import { addFirstLMEventYear, getLSNList, getSearchAPI } from "../../common";
import { sortingNumberWithBar } from "../../../SearchPage/common";
import { isEmpty } from "../../../../../utils";
import evtType from "../../../../../utils/evtType";

function DownLoadBtn() {
  const [state] = useContext(StoreContext);
  const {
    dataFormType,
    startYear,
    endYear,
    dwLandName,
    landType,
    dwLandSerialNumber,
  } = state.download;
  const [excelData, setExcelData] = useState({});
  const [loading, setLoading] = useState(false);
  const [fileName, setFileName] = useState("");

  useEffect(() => {
    setExcelData({});
    const findLandType = landType.find((obj) => obj.select);
    const findFormType = dataFormType.find((el) => el.check);
    if (!dwLandName || !findLandType || !findFormType) return;

    let dateStr = "";
    let fileNameSec2 = "";
    switch (findFormType.type) {
      case "type1":
        dateStr = `${startYear}-${endYear}`;
        fileNameSec2 = `${dwLandName}各筆${findLandType.label}之變化`;
        break;
      case "type2":
        dateStr = `${startYear}-${endYear}`;
        fileNameSec2 = `${dwLandName}${dwLandSerialNumber}${findLandType.label}之變化`;
        break;
      default:
        dateStr = `${startYear}`;
        fileNameSec2 = `${dwLandName}各筆${findLandType.label}之變化`;
        break;
    }

    setFileName(`${dateStr} ${fileNameSec2}`);
  }, [
    dwLandName,
    landType,
    dataFormType,
    dwLandSerialNumber,
    startYear,
    endYear,
  ]);

  const handleClick = async () => {
    const apiStr = getSearchAPI(state.download);
    if (!apiStr) return;
    setLoading(true);
    const findFormType = dataFormType.find((el) => el.check);
    const tmpLSNList = await getLSNList(dwLandName);
    readOntoData(apiStr).then((result) => {
      /** excelData欄位說明
       * order: 每個頁籤排序-數字
       * headers: 每個頁籤裡面的標頭資料-陣列
       * data: 每個頁籤裡面的內容資料-陣列
       * */
      let tmpResultData = JSON.parse(JSON.stringify(result.data));
      const tmpExcelData = {};
      if (findFormType.type === "type1") {
        tmpResultData = addFirstLMEventYear(tmpResultData);
        const findType = landType.find((el) => el.select);
        // sheetName: excel分頁類別
        let sheetNames = [];
        if (findType.property === evtType.LandMark) {
          sheetNames = [
            { label: "登記（變更）原因", predicate: "cause" },
            { label: "地目＋則別", predicate: "landGC" },
            { label: "土地面積", predicate: "landArea" },
            { label: "地租", predicate: "landRent" },
          ];
        } else {
          sheetNames = [
            { label: "登記（變更）原因", predicate: "cause" },
            { label: "業主", predicate: "owner" },
          ];
        }

        /** headers欄位說明:
         * id: data(資料內容)對照使用-字串
         * label: 下載後顯示在表格的標頭文字-字串
         * seq: 標頭欄位排序-數字
         * */
        const headers = [{ id: "landSerialNumber", label: "地號", seq: 0 }];
        const tmpStartYear = parseInt(startYear, 10);
        for (
          let yearAdd = 0;
          tmpStartYear + yearAdd <= parseInt(endYear, 10);
          yearAdd += 1
        ) {
          headers.push({
            id: `${tmpStartYear + yearAdd}`,
            label: `${tmpStartYear + yearAdd}`,
            seq: yearAdd,
          });
        }

        // 根據landSerialNumber排序
        tmpResultData.sort((cur, next) => sortingNumberWithBar(cur, next));

        /** data欄位說明:
         *  [id]: 對照headers id欄位內容
         * */
        sheetNames.forEach((el, index) => {
          // 從result.data找相對應的資料
          const tmpData = [];
          tmpLSNList.forEach((sn) => {
            const tmpObj = {};
            let tmpValue = ""; // 紀錄landGC、landArea、landRent前面的值
            headers.forEach((header, idx) => {
              if (idx === 0) {
                tmpObj[header.id] = sn.landSerialNumber;
              } else {
                const findObj = tmpResultData.find(
                  (obj) =>
                    sn.landSerialNumber === obj.landSerialNumber &&
                    header.label === obj.year
                );
                if (el.predicate !== "cause") {
                  /** 面積、地目＋等則、地租、業主需要內插資料 */
                  if (findObj && findObj[el.predicate]) {
                    tmpValue = findObj[el.predicate];
                  }
                  tmpObj[header.label] = tmpValue;
                } else {
                  tmpObj[header.label] = findObj
                    ? findObj[el.predicate] || ""
                    : "";
                }
              }
            });
            tmpData.push(tmpObj);
          });
          tmpExcelData[el.label] = {
            order: index,
            data: tmpData,
            headers,
          };
        });
      } else if (findFormType.type === "type2") {
        /** headers欄位說明:
         * id: data(資料內容)對照使用-字串
         * label: 下載後顯示在表格的標頭文字-字串
         * seq: 標頭欄位排序-數字
         * */
        const headers = [{ id: "item", label: "", seq: 0 }];
        const tmpStartYear = parseInt(startYear, 10);
        for (
          let yearAdd = 0;
          tmpStartYear + yearAdd <= parseInt(endYear, 10);
          yearAdd += 1
        ) {
          headers.push({
            id: `${tmpStartYear + yearAdd}`,
            label: `${tmpStartYear + yearAdd}`,
            seq: yearAdd,
          });
        }

        // 根據landMarkNumber排序
        tmpResultData.sort(
          (cur, next) => cur.landMarkNumber - next.landMarkNumber
        );

        /** data欄位說明:
         *  [id]: 對照headers id欄位內容
         * */
        const tmpData = [];
        let firstCol = []; // 第一欄資料
        const findType = landType.find((el) => el.select);
        if (findType.property === evtType.LandMark) {
          firstCol = [
            { label: "登記（變更）原因", predicate: "cause" },
            { label: "地目＋則別", predicate: "landGC" },
            { label: "土地面積", predicate: "landArea" },
            { label: "地租", predicate: "landRent" },
          ];
        } else {
          firstCol = [
            { label: "登記（變更）原因", predicate: "cause" },
            { label: "業主", predicate: "owner" },
          ];
        }

        firstCol.forEach((item) => {
          const tmpObj = {};
          headers.forEach((header, index) => {
            if (index === 0) {
              tmpObj[header.id] = item.label;
            } else {
              const findData = tmpResultData.find(
                (el) => el.year === header.id
              );
              tmpObj[header.id] = findData
                ? findData[item.predicate] || ""
                : "";
            }
          });
          tmpData.push(tmpObj);
        });

        tmpExcelData["工作表1"] = {
          order: 0,
          data: tmpData,
          headers,
        };
      } else if (findFormType.type === "type3") {
        /** headers欄位說明:
         * id: data(資料內容)對照使用-字串
         * label: 下載後顯示在表格的標頭文字-字串
         * seq: 標頭欄位排序-數字
         * */
        let headers = [];
        const findType = landType.find((el) => el.select);
        if (findType.property === evtType.LandMark) {
          headers = [
            { id: "landSerialNumber", label: "地號", seq: 0 },
            { id: "cause", label: "登記（變更）原因", seq: 1 },
            { id: "landGC", label: "地目＋則別", seq: 2 },
            { id: "landArea", label: "土地面積", seq: 3 },
            { id: "landRent", label: "地租", seq: 4 },
          ];
        } else {
          headers = [
            { id: "landSerialNumber", label: "地號", seq: 0 },
            { id: "cause", label: "登記（變更）原因", seq: 1 },
            { id: "owner", label: "業主", seq: 2 },
          ];
        }

        // 根據landSerialNumber排序
        tmpResultData.sort((cur, next) => sortingNumberWithBar(cur, next));

        /** data欄位說明:
         *  [id]: 對照headers id欄位內容
         * */
        const tmpData = [];
        tmpLSNList.forEach((sn) => {
          const tmpObj = {};
          headers.forEach((header, index) => {
            if (index === 0) {
              tmpObj[header.id] = sn.landSerialNumber;
            } else {
              const findData = tmpResultData.find(
                (el) =>
                  el.landSerialNumber === sn.landSerialNumber &&
                  el.year === startYear
              );
              tmpObj[header.id] = findData ? findData[header.id] || "" : "";
            }
          });
          tmpData.push(tmpObj);
        });

        tmpExcelData["工作表1"] = {
          order: 0,
          data: tmpData,
          headers,
        };
      }
      setExcelData(tmpExcelData);
      setLoading(false);
    });
  };

  return (
    <Stack 
      direction={{ xs: 'column', sm: 'row' }}
      spacing={2}
      alignItems={{ xs: 'stretch', sm: 'center' }}
      sx={{ 
        mt: 3,
        p: 2,
        backgroundColor: 'var(--color-surface-alt)',
        borderRadius: 2,
        border: '1px solid var(--color-border-light)',
      }}
    >
      <Button
        variant="contained"
        size="large"
        onClick={handleClick}
        color="primary"
        disabled={!isEmpty(excelData) || loading}
        sx={{
          minWidth: 160,
          px: 4,
          py: 1.5,
          fontSize: '1rem',
          fontWeight: 600,
          borderRadius: 2,
          boxShadow: 2,
          textTransform: 'none',
          '&:hover': {
            boxShadow: 4,
            transform: 'translateY(-1px)',
          },
          transition: 'all 0.2s ease',
        }}
      >
        {loading ? (
          <>
            <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
            產生中...
          </>
        ) : (
          "產生資料"
        )}
      </Button>
      {!isEmpty(excelData) && (
        <ExcelMultiSheet filename={fileName} excelData={excelData} />
      )}
    </Stack>
  );
}

export default DownLoadBtn;
