import React, { useState } from "react";

// material ui
import Box from "@mui/material/Box";
import Tab from "@mui/material/Tab";
import Tabs from "@mui/material/Tabs";

//
import "./DownloadPage.scss";

//
import LandData from "./subComponents/LandData/index";
import EditorLand from "./subComponents/LandEditor/index";

function DownloadPage() {
  const tabList = [
    { label: "指定土地內容", component: LandData },
    { label: "編輯者編輯內容", component: EditorLand },
  ];

  const [value, setValue] = useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <div className="mainBox_shadowMain DownloadPage">
      <Box 
        sx={{ 
          borderBottom: 1, 
          borderColor: "divider",
          backgroundColor: "background.paper",
          borderRadius: "12px 12px 0 0",
        }}
      >
        <Tabs
          value={value}
          onChange={handleChange}
          variant="scrollable"
          indicatorColor="primary"
          textColor="primary"
          sx={{
            "& .MuiTab-root": {
              textTransform: "none",
              fontWeight: 600,
              fontSize: "1rem",
              minHeight: 64,
            },
          }}
        >
          {tabList.map((item, index) => (
            <Tab 
              label={item.label} 
              key={index}
              sx={{
                "&.Mui-selected": {
                  color: "primary.main",
                },
              }}
            />
          ))}
        </Tabs>
      </Box>
      <Box 
        sx={{
          backgroundColor: "background.default",
          minHeight: "400px",
          borderRadius: "0 0 12px 12px",
        }}
      >
        {tabList
          .filter((item, index) => value === index)
          .map((item, index) => (
            <item.component key={index} />
          ))}
      </Box>
    </div>
  );
}

export default DownloadPage;
