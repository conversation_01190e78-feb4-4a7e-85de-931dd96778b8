{"Name": "Chrome", "userFullName": "<PERSON>", "projectName": "katalon", "projectDir": "C:/project/land-web/katalon", "host": {"hostName": "saw63 - host.docker.internal", "os": "Windows 11 64bit", "hostPort": 54992, "hostAddress": "************"}, "execution": {"general": {"autoApplyNeighborXpaths": false, "ignorePageLoadTimeoutException": false, "timeCapsuleEnabled": false, "executionProfile": "default", "excludeKeywords": ["verifyElementPresent", "verifyElementNotPresent"], "canvasTextExtractionEnabled": false, "flutterAppTestingEnabled": false, "xpathsPriority": [{"left": "xpath:attributes", "right": true}, {"left": "xpath:idRelative", "right": true}, {"left": "dom:name", "right": true}, {"left": "xpath:link", "right": true}, {"left": "xpath:neighbor", "right": true}, {"left": "xpath:href", "right": true}, {"left": "xpath:img", "right": true}, {"left": "xpath:position", "right": true}, {"left": "xpath:customAttributes", "right": true}], "timeout": 30, "actionDelay": 0, "methodsPriorityOrder": [{"left": "XPATH", "right": true}, {"left": "SMART_LOCATOR", "right": true}, {"left": "BASIC", "right": true}, {"left": "CSS", "right": true}, {"left": "IMAGE", "right": true}], "proxy": "{\"proxyOption\":\"NO_PROXY\",\"proxyServerType\":\"HTTP\",\"username\":\"\",\"password\":\"\",\"proxyServerAddress\":\"\",\"proxyServerPort\":0,\"exceptionList\":\"\",\"applyToDesiredCapabilities\":true}", "defaultFailureHandling": "STOP_ON_FAILURE", "terminateDriverAfterTestCase": false, "defaultPageLoadTimeout": 30, "closedShadowDOMEnabled": false, "report": {"takeScreenshotSettings": {"enable": false}, "videoRecorderSettings": {"enable": false, "useBrowserRecorder": true, "videoFormat": "WEBM", "videoQuality": "LOW", "recordAllTestCases": false}, "screenCaptureOption": true, "reportFolder": "C:\\project\\land-web\\katalon\\Reports\\20250820_095829\\Mandarin\\v1\\Gis\\20250820_095829"}, "enablePageLoadTimeout": false, "terminateDriverAfterTestSuite": true, "useActionDelayInSecond": "SECONDS", "testDataInfo": {}, "selfHealingEnabled": true}, "drivers": {"system": {"WebUI": {"chromeDriverPath": "C:\\Users\\<USER>\\.katalon\\packages\\KSE-10.2.3\\configuration\\resources\\drivers\\chromedriver_win32\\chromedriver.exe", "browserType": "CHROME_DRIVER"}}, "preferences": {"WebUI": {}}}, "globalSmartWaitEnabled": true, "smartLocatorEnabled": true, "smartLocatorSettingDefaultEnabled": true, "logTestSteps": true, "hideHostname": false}, "executedEntity": "TestSuite", "id": "Test Suites/Mandarin/v1/Gis", "name": "Gis", "description": "", "source": "C:\\project\\land-web\\katalon\\Test Suites\\Mandarin\\v1\\Gis.ts", "sessionServer.host": "127.0.0.1", "sessionServer.port": 64506, "isDebugLaunchMode": false, "logbackConfigFileLocation": "C:\\Users\\<USER>\\.katalon\\packages\\KSE-10.2.3\\configuration\\org.eclipse.osgi\\128\\0\\.cp\\resources\\logback\\logback-console.xml", "katalon.versionNumber": "10.2.3", "katalon.buildNumber": "0", "runningMode": "GUI", "pluginTestListeners": [], "allowUsingSelfHealing": true, "allowUsingTimeCapsule": true, "allowCustomizeRequestTimeout": true, "allowCustomizeRequestResponseSizeLimit": true, "maxFailedTests": -1, "testops": {}}