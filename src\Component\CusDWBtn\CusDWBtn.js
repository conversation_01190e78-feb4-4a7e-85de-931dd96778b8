import React, { useState } from "react";
import PropTypes from "prop-types";
import { saveAs } from "file-saver";

// material ui
import MenuItem from "@mui/material/MenuItem";
import Button from "@mui/material/Button";
import Menu from "@mui/material/Menu";
import DownloadIcon from "@mui/icons-material/Download";

// utils
import domToImage from "../../utils/domToImage";
import { jsonExcel } from "./dataExport";

// components
import DownloadExcel from "../ExcelMultiSheet/DownloadExcel";

function CusDWBtn({ imgDivId, state }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleImgDW = () => {
    domToImage({ id: imgDivId });
    handleClose();
  };

  const transferData = (expData, headerCols) => {
    const mapHeader = headerCols.reduce((acc, next) => {
      acc[next.id] = next.displayName;
      return acc;
    }, {});

    // Replace Header Data
    return expData.map((elObj) => {
      const tmpObj = {};
      Object.keys(elObj).forEach((key) => {
        tmpObj[mapHeader[key]] = elObj[key];
      });
      return tmpObj;
    });
  };

  const handleTSVDW = () => {
    const { expData, headerCols, fileName } = state;
    const repHdData = transferData(expData, headerCols);

    // Convert the string to a Blob
    const blob = new Blob([jsonExcel(repHdData, "\t")], { type: "text/tsv" });

    // Trigger the download
    saveAs(blob, `${fileName}.tsv`);
  };

  const isXlsxButton = (key, dom) => {
    const { expData, headerCols, fileName } = state;
    const repHdData = transferData(expData, headerCols);
    return key === "downloadXls" && DownloadExcel ? (
      <DownloadExcel
        key={key}
        element={dom}
        data={repHdData}
        fileName={fileName}
      />
    ) : (
      dom
    );
  };

  const btns = [
    {
      key: "downloadImage",
      onClick: () => handleImgDW({ id: imgDivId }),
      label: "下載圖片",
    },
    {
      key: "downloadTsv",
      onClick: () => handleTSVDW(),
      label: "下載Tsv",
    },
    {
      key: "downloadXls", // download click trigger 交給 react-data-export
      label: "下載Excel",
    },
  ];

  return (
    <>
      <Button
        onClick={handleClick}
        variant="outlined"
        color="inherit"
        style={{ marginLeft: "0.5rem" }}
      >
        <DownloadIcon />
      </Button>
      <Menu onClose={handleClose} open={open} anchorEl={anchorEl}>
        {btns.map((el) => {
          const { key, onClick, label } = el;
          return isXlsxButton(
            key,
            <MenuItem key={key} onClick={onClick}>
              {label}
            </MenuItem>
          );
        })}
      </Menu>
    </>
  );
}

CusDWBtn.propTypes = {
  /** download image div id string */
  imgDivId: PropTypes.string,
  /** reducer data */
  state: PropTypes.shape({
    /** 匯出資料 */
    expData: PropTypes.arrayOf(PropTypes.shape({})),
    /** 下載表單對應顯示欄位名稱 */
    headerCols: PropTypes.arrayOf(PropTypes.shape({})),
    /** 匯出檔名 */
    fileName: PropTypes.string,
  }),
};

CusDWBtn.defaultProps = {
  /** download image div id string */
  imgDivId: "SpecChart",
  /** reducer data */
  state: {
    /** 匯出資料 */
    expData: [],
    /** 下載表單對應顯示欄位名稱 */
    headerCols: [],
    /** 匯出檔名 */
    fileName: "output",
  },
};

export default CusDWBtn;
