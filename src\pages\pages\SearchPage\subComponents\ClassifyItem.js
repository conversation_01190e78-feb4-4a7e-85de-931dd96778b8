import React, { useContext, useMemo, useState } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Checkbox,
  Drawer,
  FormControlLabel,
  IconButton,
  Paper,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import TuneIcon from "@mui/icons-material/Tune";
import CloseIcon from "@mui/icons-material/Close";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";

import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

const PANEL_WIDTH = 300;
const COLLAPSED_WIDTH = 72;

const getTotals = (groups) =>
  groups.reduce(
    (acc, group) => {
      const groupTotal = group.items.length;
      const groupSelected = group.items.filter((item) => item.select).length;
      acc.total += groupTotal;
      acc.selected += groupSelected;
      return acc;
    },
    { total: 0, selected: 0 }
  );

function FiltersContent({
  groups,
  totals,
  filterTerm,
  onFilterChange,
  onSelectAll,
  onClearAll,
  onReset,
  onToggleGroup,
  onToggleItem,
  onClose,
}) {
  const hasGroups = groups.length > 0;

  return (
    <Box
      className="filtersContent"
      sx={{
        width: "100%",
        maxWidth: PANEL_WIDTH,
        display: "flex",
        flexDirection: "column",
        height: "100%",
        // Mobile drawer 專用樣式
        ...(onClose && {
          "& .filtersHeader": {
            p: 2,
            pb: 1,
            borderBottom: "1px solid",
            borderColor: "divider",
          },
          "& .filtersContent": {
            px: 2,
            py: 1,
          },
          "& .filtersList": {
            px: 1,
          },
          "& .filtersFooter": {
            p: 2,
            pt: 1,
            borderTop: "1px solid",
            borderColor: "divider",
            mt: "auto",
          },
        }),
      }}
      role="presentation"
    >
      {/* 頭部區域 */}
      <Box className="filtersHeader">
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 1 }}>
          <Typography 
            className="filterTitle"
            variant="h6"
            sx={{ 
              fontWeight: 600,
              fontSize: "1.1rem",
              color: "text.primary"
            }}
          >
            搜尋欄位
          </Typography>
          {onClose ? (
            <IconButton 
              size="small" 
              onClick={onClose}
              sx={{
                backgroundColor: "grey.100",
                "&:hover": {
                  backgroundColor: "grey.200",
                },
              }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          ) : null}
        </Stack>
        <Typography 
          className="filterStats"
          variant="body2"
          color="text.secondary"
          sx={{ mb: 2 }}
        >
          已選 {totals.selected} / {totals.total} 個欄位
        </Typography>
        <TextField
          fullWidth
          size="small"
          placeholder="搜尋欄位名稱"
          value={filterTerm}
          onChange={(event) => onFilterChange(event.target.value)}
          sx={{ 
            "& .MuiOutlinedInput-root": {
              borderRadius: 2,
            },
          }}
        />
      </Box>
      {/* 中間內容區域 */}
      <Box
        className="filtersContent"
        sx={{
          flex: 1,
          overflowY: "auto",
          overflowX: "hidden",
          minHeight: 0, // 重要：讓 flex 子元素可以縮小
        }}
      >
        <Box 
          className="filtersList"
          sx={{
            // Mobile drawer 專用間距
            ...(onClose && {
              px: 0,
              py: 1,
            }),
          }}
        >
        {hasGroups ? (
          groups.map((group) => {
            const groupSelected = group.items.filter((item) => item.select).length;
            const groupTotal = group.items.length;
            const isIndeterminate = groupSelected > 0 && groupSelected < groupTotal;
            const isAllSelected = groupSelected === groupTotal;

            return (
              <Accordion 
                key={group.classType} 
                defaultExpanded={false}
                disableGutters
                sx={{ 
                  mb: 1,
                  '&:before': { display: 'none' },
                  '&.Mui-expanded': { mb: 1 },
                }}
              >
                <AccordionSummary 
                  expandIcon={<ExpandMoreIcon />} 
                  data-filter-group={group.classType}
                  sx={{
                    minHeight: 48,
                    '&.Mui-expanded': { minHeight: 48 },
                  }}
                >
                  <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ width: "100%" }}>
                    <Box>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                        {group.classLabel}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        已選 {groupSelected} / {groupTotal}
                      </Typography>
                    </Box>
                    <Checkbox
                      checked={isAllSelected}
                      indeterminate={isIndeterminate}
                      size="small"
                      onClick={(event) => {
                        event.stopPropagation();
                        event.preventDefault();
                        onToggleGroup(group.classType, !isAllSelected);
                      }}
                    />
                  </Stack>
                </AccordionSummary>
                <AccordionDetails sx={{ pt: 0, px: 2, pb: 1 }}>
                  <Stack spacing={0.5}>
                    {group.items.map((item) => (
                      <FormControlLabel
                        key={item.value}
                        control={
                          <Checkbox
                            size="small"
                            checked={item.select}
                            onChange={() => onToggleItem(group.classType, item.value)}
                          />
                        }
                        label={
                          <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                            {item.label}
                          </Typography>
                        }
                        sx={{ ml: 0 }}
                      />
                    ))}
                  </Stack>
                </AccordionDetails>
              </Accordion>
            );
          })
        ) : (
          <Typography variant="body2" color="text.secondary" textAlign="center">
            沒有符合「{filterTerm}」的欄位
          </Typography>
        )}
        </Box>
      </Box>
      
      {/* 底部操作區域 */}
      <Box className="filtersFooter">
        <Stack 
          className="footerActions"
          direction="row"
          spacing={1.5}
          sx={{
            // Mobile drawer 專用樣式
            ...(onClose && {
              flexWrap: "wrap",
              "& .MuiButton-root": {
                borderRadius: 2,
                textTransform: "none",
                fontWeight: 600,
              },
            }),
          }}
        >
          <Button
            variant="contained"
            size="medium"
            onClick={() => onSelectAll(true)}
            sx={{ flex: 1, minWidth: 0 }}
          >
            全選
          </Button>
          <Button
            variant="outlined"
            size="medium"
            onClick={onClearAll}
            sx={{ flex: 1, minWidth: 0 }}
          >
            清空
          </Button>
          <Button
            variant="text"
            size="medium"
            onClick={onReset}
            color="secondary"
            sx={{ flexBasis: "100%", mt: 1 }}
          >
            重設所有選擇
          </Button>
        </Stack>
      </Box>
    </Box>
  );
}

function ClassifyItem() {
  const [state, dispatch] = useContext(StoreContext);
  const { searchColList } = state.search;
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [filterTerm, setFilterTerm] = useState("");
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [collapsed, setCollapsed] = useState(false);

  const totals = useMemo(() => getTotals(searchColList), [searchColList]);

  const filteredGroups = useMemo(() => {
    const keyword = filterTerm.trim().toLowerCase();
    if (!keyword) {
      return searchColList;
    }
    return searchColList
      .map((group) => {
        const filteredItems = group.items.filter((item) =>
          item.label.toLowerCase().includes(keyword)
        );
        return { ...group, items: filteredItems };
      })
      .filter((group) => group.items.length > 0);
  }, [filterTerm, searchColList]);

  const handleSelectAll = (nextSelectState) => {
    const nextList = searchColList.map((group) => ({
      ...group,
      items: group.items.map((item) => ({ ...item, select: nextSelectState })),
    }));

    dispatch({
      type: Act.SET_SEARCHCOLLIST,
      payload: nextList,
    });
  };

  const handleReset = () => {
    setFilterTerm("");
    handleSelectAll(true);
  };

  const handleToggleGroup = (classType, nextSelectState) => {
    const nextList = searchColList.map((group) => {
      if (group.classType !== classType) {
        return group;
      }
      return {
        ...group,
        items: group.items.map((item) => ({ ...item, select: nextSelectState })),
      };
    });

    dispatch({
      type: Act.SET_SEARCHCOLLIST,
      payload: nextList,
    });
  };

  const handleToggleItem = (classType, value) => {
    const nextList = searchColList.map((group) => {
      if (group.classType !== classType) {
        return group;
      }
      return {
        ...group,
        items: group.items.map((item) =>
          item.value === value ? { ...item, select: !item.select } : item
        ),
      };
    });

    dispatch({
      type: Act.SET_SEARCHCOLLIST,
      payload: nextList,
    });
  };

  const content = (
    <FiltersContent
      groups={filteredGroups}
      totals={totals}
      filterTerm={filterTerm}
      onFilterChange={setFilterTerm}
      onSelectAll={handleSelectAll}
      onClearAll={() => handleSelectAll(false)}
      onReset={handleReset}
      onToggleGroup={handleToggleGroup}
      onToggleItem={handleToggleItem}
      onClose={isMobile ? () => setDrawerOpen(false) : () => setCollapsed(true)}
    />
  );

  if (isMobile) {
    return (
      <Box className="ClassifyItem">
        <Button
          fullWidth
          variant="outlined"
          startIcon={<TuneIcon />}
          onClick={() => setDrawerOpen(true)}
          sx={{
            py: 1.5,
            px: 2,
            borderRadius: 2,
            textTransform: "none",
            fontWeight: 600,
            fontSize: "1rem",
            border: "1px solid",
            borderColor: "divider",
            backgroundColor: "background.paper",
            color: "text.primary",
            "&:hover": {
              backgroundColor: "action.hover",
              borderColor: "primary.main",
              "& .MuiSvgIcon-root": {
                color: "primary.main",
              },
            },
            "& .MuiSvgIcon-root": {
              fontSize: "1.2rem",
            },
          }}
        >
          篩選欄位（已選 {totals.selected}{totals.total > 0 ? ` / ${totals.total}` : ''}）
        </Button>
        <Drawer
          anchor="right"
          open={drawerOpen}
          onClose={() => setDrawerOpen(false)}
          PaperProps={{ 
            sx: { 
              width: PANEL_WIDTH,
              display: "flex",
              flexDirection: "column",
              height: "100%",
            }
          }}
        >
          {content}
        </Drawer>
      </Box>
    );
  }

  if (collapsed) {
    return (
      <Paper
        className="ClassifyItem filtersPanel collapsed"
        elevation={1}
        sx={{ 
          p: 1.5, 
          width: COLLAPSED_WIDTH, 
          height: "fit-content",
          display: "flex", 
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          gap: 1,
        }}
      >
        <Tooltip title="展開搜尋欄位" placement="right">
          <IconButton 
            color="primary" 
            onClick={() => setCollapsed(false)}
            sx={{
              p: 1.5,
              backgroundColor: "primary.main",
              color: "white",
              borderRadius: 2,
              "&:hover": {
                backgroundColor: "primary.dark",
                transform: "scale(1.05)",
              },
              transition: "all 0.2s ease",
            }}
          >
            <KeyboardDoubleArrowRightIcon />
          </IconButton>
        </Tooltip>
        <Typography 
          variant="caption" 
          color="text.secondary" 
          sx={{ 
            textAlign: "center",
            writingMode: "vertical-rl",
            fontSize: "0.7rem",
            lineHeight: 1.2,
          }}
        >
          {totals.selected}/{totals.total}
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper
      className="ClassifyItem filtersPanel"
      elevation={2}
      sx={{ 
        p: 0, 
        width: PANEL_WIDTH,
        height: "calc(100vh - 120px)",
        display: "flex",
        flexDirection: "column",
        overflow: "hidden",
        position: "relative",
      }}
    >
      {/* 收合按鈕 */}
      <Box sx={{ position: "absolute", top: 12, right: 12, zIndex: 1 }}>
        <Tooltip title="收合搜尋欄位" placement="left">
          <IconButton 
            size="small" 
            onClick={() => setCollapsed(true)}
            sx={{
              backgroundColor: "background.paper",
              boxShadow: 1,
              "&:hover": {
                backgroundColor: "grey.100",
              },
            }}
          >
            <KeyboardDoubleArrowLeftIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>
      {content}
    </Paper>
  );
}

export default ClassifyItem;
