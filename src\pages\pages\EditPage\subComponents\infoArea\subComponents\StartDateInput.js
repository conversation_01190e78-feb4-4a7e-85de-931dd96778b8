import React, { useContext, useEffect, useMemo, useState } from "react";

// material ui
import TextField from "@mui/material/TextField";

// utils
import { disableEditFunction, disableFontColor } from "../../../common";
import {
  landMarkPlaceHolder,
  landRightsPlaceHolder,
} from "../../../common/phDes";
import { StoreContext } from "../../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../../utils";
import evtType from "../../../../../../utils/evtType";

const extractYear = (value) => {
  if (!value) return "";
  const [yearPart] = `${value}`.split('-');
  return yearPart.replace(/[^0-9]/g, '').slice(0, 4);
};

function StartDateInput({ info, handleChange, index, type }) {
  const [state] = useContext(StoreContext);
  const { landMarkInfo, landRightInfo } = state.edit;
  const [checkDateRepeat, setCheckDateRepeat] = useState([]); // 判斷有無重複日期
  const [landEvtInfo, setLandEvtInfo] = useState([]);
  const [helperText, setHelperText] = useState("");
  const [plArr, setPlArr] = useState({});
  const [inputValue, setInputValue] = useState(extractYear(info.value));

  useEffect(() => {
    if (type === evtType.LandMark) {
      setLandEvtInfo(landMarkInfo);
      setHelperText("土地標示變更登記日期重複");
      setPlArr(landMarkPlaceHolder);
    } else if (type === evtType.LandRights) {
      setLandEvtInfo(landRightInfo);
      setHelperText("土地權利變更登記日期重複");
      setPlArr(landRightsPlaceHolder);
    }
  }, [type, landMarkInfo, landRightInfo]);

  useEffect(() => {
    if (isEmpty(landEvtInfo)) return;
    // 初始化checkDateRepeat
    const tmpArr = Array.from({ length: landEvtInfo.length }, () => false);
    setCheckDateRepeat(tmpArr);
  }, [landEvtInfo]);

  useEffect(() => {
    setInputValue(extractYear(info.value));
  }, [info.value]);

  const isDisabled = disableEditFunction(state);

  const placeholder = useMemo(
    () => plArr[info.predicate] || "請輸入西元年",
    [plArr, info.predicate]
  );

  return (
    <TextField
      disabled={isDisabled}
      value={inputValue}
      variant="standard"
      fullWidth
      placeholder={placeholder}
      onChange={(event) => {
        const rawDigits = event.target.value.replace(/[^0-9]/g, "").slice(0, 4);
        setInputValue(rawDigits);
        const newData = { ...info, index };
        const newValue = rawDigits ? `${rawDigits}-00-00` : "";
        handleChange(newValue, newData);
      }}
      inputProps={{
        ...disableFontColor,
        inputMode: "numeric",
        pattern: "[0-9]*",
        maxLength: 4,
      }}
      error={Boolean(checkDateRepeat[index])}
      helperText={checkDateRepeat[index] ? helperText : ""}
    />
  );
}

export default StartDateInput;
