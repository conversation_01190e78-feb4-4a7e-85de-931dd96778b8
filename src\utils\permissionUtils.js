/**
 * 權限控制工具函數
 * 
 * 用途：
 * 1. 定義操作權限類型（查看、編輯、刪除、匯入、匯出、管理使用者）
 * 2. 根據使用者角色 + 環境判斷是否可執行特定操作
 * 3. 支援多選角色格式（如 "admin,editor,developer"）
 * 
 * 權限判斷邏輯：
 * - Admin 最高優先：有完整權限，忽略其他角色
 * - Developer 環境限制：正式站不可編輯/刪除/匯入，測試站可以
 * - 其他角色累加：取最高權限（editor > reader > anonymous）
 */

import { isProduction, getUserRolesArrayByEnvironment } from './environmentUtils';
import role from '../config/App-role';

/**
 * 操作權限類型
 */
export const OPERATION = {
  VIEW: 'view',              // 查看
  EDIT: 'edit',              // 編輯
  DELETE: 'delete',          // 刪除
  IMPORT: 'import',          // 匯入
  EXPORT: 'export',          // 匯出
  MANAGE_USERS: 'manage_users',  // 管理使用者
};

/**
 * 檢查使用者是否可以執行特定操作（支援多選角色）
 * 
 * @param {Object} user - 使用者物件
 * @param {string} operation - 操作類型（OPERATION 中的值）
 * @returns {boolean}
 * 
 * @example
 * // 單選角色
 * const user1 = { role: "admin" };
 * canPerformOperation(user1, OPERATION.EDIT);  // true
 * 
 * // 多選角色
 * const user2 = { role: "developer,editor", roleDev: "admin,developer" };
 * // 正式站: developer 限制優先，不可編輯
 * // 測試站: admin 權限最高，可編輯
 */
export const canPerformOperation = (user, operation) => {
  if (!user) return false;
  
  // 取得使用者的所有角色（陣列）
  const roles = getUserRolesArrayByEnvironment(user);
  const isProd = isProduction();
  
  // 1. Admin 在所有環境都有完整權限
  if (roles.includes(role.admin)) {
    return true;
  }
  
  // 2. Developer 權限根據環境區分
  if (roles.includes(role.developer)) {
    switch (operation) {
      case OPERATION.VIEW:
        return true;  // 所有環境都可查看
        
      case OPERATION.EDIT:
        // 正式站：不可編輯資料庫
        // 測試站：可編輯
        return !isProd;
        
      case OPERATION.DELETE:
        // 正式站：不可刪除
        // 測試站：可刪除
        return !isProd;
        
      case OPERATION.IMPORT:
        // 正式站：禁止匯入
        // 測試站：允許匯入
        return !isProd;
        
      case OPERATION.EXPORT:
        // 所有環境都可匯出
        return true;
        
      case OPERATION.MANAGE_USERS:
        // 所有環境都不可管理使用者（僅 admin）
        // return false;
        return !isProd;
        
      default:
        return false;
    }
  }
  
  // 3. Editor 權限
  if (roles.includes(role.editor)) {
    switch (operation) {
      case OPERATION.VIEW:
      case OPERATION.EDIT:
        return true;
      case OPERATION.DELETE:
      case OPERATION.IMPORT:
      case OPERATION.EXPORT:
      case OPERATION.MANAGE_USERS:
        return false;
      default:
        return false;
    }
  }
  
  // 4. Reader 權限
  if (roles.includes(role.reader)) {
    return operation === OPERATION.VIEW;
  }
  
  // 5. Anonymous 或其他
  return false;
};

/**
 * 檢查是否可以編輯（快捷方法）
 * @param {Object} user - 使用者物件
 * @returns {boolean}
 */
export const canEdit = (user) => canPerformOperation(user, OPERATION.EDIT);

/**
 * 檢查是否可以刪除（快捷方法）
 * @param {Object} user - 使用者物件
 * @returns {boolean}
 */
export const canDelete = (user) => canPerformOperation(user, OPERATION.DELETE);

/**
 * 檢查是否可以匯入資料（快捷方法）
 * @param {Object} user - 使用者物件
 * @returns {boolean}
 */
export const canImport = (user) => canPerformOperation(user, OPERATION.IMPORT);

/**
 * 檢查是否可以匯出資料（快捷方法）
 * @param {Object} user - 使用者物件
 * @returns {boolean}
 */
export const canExport = (user) => canPerformOperation(user, OPERATION.EXPORT);

/**
 * 檢查是否可以管理使用者（快捷方法）
 * @param {Object} user - 使用者物件
 * @returns {boolean}
 */
export const canManageUsers = (user) => canPerformOperation(user, OPERATION.MANAGE_USERS);

/**
 * 檢查是否可以查看（快捷方法）
 * @param {Object} user - 使用者物件
 * @returns {boolean}
 */
export const canView = (user) => canPerformOperation(user, OPERATION.VIEW);

/**
 * 取得操作權限說明文字
 * 
 * @param {Object} user - 使用者物件
 * @param {string} operation - 操作類型
 * @returns {string} 權限說明文字
 * 
 * @example
 * const user = { role: "developer" };
 * // 在正式站
 * getPermissionMessage(user, OPERATION.EDIT);
 * // 回傳: "Developer 角色在正式站僅能查看資料，不可編輯"
 */
export const getPermissionMessage = (user, operation) => {
  const roles = getUserRolesArrayByEnvironment(user);
  const isProd = isProduction();
  const envLabel = isProd ? '正式站' : '測試站';
  
  // Admin 有完整權限
  if (roles.includes(role.admin)) {
    return `您擁有 Admin 權限，可執行所有操作`;
  }
  
  // Developer 在正式站的限制
  if (roles.includes(role.developer) && isProd) {
    const messages = {
      [OPERATION.EDIT]: `Developer 角色在${envLabel}僅能查看資料，不可編輯`,
      [OPERATION.DELETE]: `Developer 角色在${envLabel}僅能查看資料，不可刪除`,
      [OPERATION.IMPORT]: `Developer 角色在${envLabel}禁止匯入資料`,
      [OPERATION.MANAGE_USERS]: `僅 Admin 可管理使用者權限`,
    };
    return messages[operation] || `您沒有執行此操作的權限`;
  }
  
  // 其他角色的一般訊息
  const generalMessages = {
    [OPERATION.EDIT]: `您的角色 (${roles.join(', ')}) 沒有編輯權限`,
    [OPERATION.DELETE]: `您的角色 (${roles.join(', ')}) 沒有刪除權限`,
    [OPERATION.IMPORT]: `您的角色 (${roles.join(', ')}) 沒有匯入權限`,
    [OPERATION.EXPORT]: `您的角色 (${roles.join(', ')}) 沒有匯出權限`,
    [OPERATION.MANAGE_USERS]: `僅 Admin 可管理使用者權限`,
  };
  
  return generalMessages[operation] || `您沒有執行此操作的權限`;
};

/**
 * 取得權限提示訊息（包含環境資訊）
 * 
 * @param {Object} user - 使用者物件
 * @returns {Object} { show: boolean, message: string, severity: 'info' | 'warning' | 'error' }
 * 
 * @example
 * const user = { role: "developer" };
 * const hint = getPermissionHint(user);
 * // 正式站: { show: true, message: "您在正式站僅能查看資料...", severity: "warning" }
 * // 測試站: { show: false, message: "", severity: "info" }
 */
export const getPermissionHint = (user) => {
  const roles = getUserRolesArrayByEnvironment(user);
  const isProd = isProduction();
  
  // Admin 不顯示提示
  if (roles.includes(role.admin)) {
    return {
      show: false,
      message: '',
      severity: 'info',
    };
  }
  
  // Developer 在正式站顯示警告
  if (roles.includes(role.developer) && isProd) {
    return {
      show: true,
      message: '您在正式站僅能查看資料，無法進行編輯、刪除或匯入操作。若需要完整權限，請切換至測試站。',
      severity: 'warning',
    };
  }
  
  // 其他情況不顯示
  return {
    show: false,
    message: '',
    severity: 'info',
  };
};

/**
 * 取得角色的中文顯示名稱
 * 
 * @param {string} roleKey - 角色 key（如 "admin", "editor"）
 * @returns {string} 中文名稱
 */
export const getRoleDisplayName = (roleKey) => {
  const roleNames = {
    admin: '管理員',
    editor: '編輯者',
    reader: '查閱者',
    developer: '開發者',
    anonymous: '訪客',
  };
  return roleNames[roleKey] || roleKey;
};

/**
 * 取得使用者的角色顯示文字（支援多選）
 * 
 * @param {Object} user - 使用者物件
 * @returns {string} 角色顯示文字（如 "管理員、編輯者"）
 * 
 * @example
 * const user = { role: "admin,editor" };
 * getUserRoleDisplayText(user);  // "管理員、編輯者"
 */
export const getUserRoleDisplayText = (user) => {
  const roles = getUserRolesArrayByEnvironment(user);
  return roles.map(r => getRoleDisplayName(r)).join('、');
};

/**
 * 檢查操作是否需要權限確認對話框
 * 
 * @param {string} operation - 操作類型
 * @returns {boolean}
 */
export const requiresConfirmation = (operation) => {
  const dangerousOperations = [
    OPERATION.DELETE,
    OPERATION.IMPORT,
    OPERATION.MANAGE_USERS,
  ];
  return dangerousOperations.includes(operation);
};

/**
 * 取得操作的確認訊息
 * 
 * @param {string} operation - 操作類型
 * @returns {string}
 */
export const getConfirmationMessage = (operation) => {
  const messages = {
    [OPERATION.DELETE]: '確定要刪除此資料嗎？此操作無法復原。',
    [OPERATION.IMPORT]: '確定要匯入資料嗎？這會修改資料庫內容。',
    [OPERATION.MANAGE_USERS]: '確定要修改使用者權限嗎？',
  };
  return messages[operation] || '確定要執行此操作嗎？';
};
