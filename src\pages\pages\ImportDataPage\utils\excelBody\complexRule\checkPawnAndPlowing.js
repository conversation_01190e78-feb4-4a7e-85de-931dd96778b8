import { specRow } from "../../../config";
import checkLandBasic from "../basicRule/checkLandBasic";
import checkLimitList from "../basicRule/checkLimitList";

const checkPawnAndPlowing = (cell, worksheet, checkList) => {
  let tmpStr = "";

  tmpStr = checkLandBasic(cell, worksheet);

  if (!tmpStr) {
    const colLabel = worksheet
      .getRow(specRow.labelIdx)
      .getCell(cell.fullAddress.col).value;
    const headerRow = worksheet.getRow(specRow.idIdx).values; // first row
    const row = worksheet.getRow(cell.fullAddress.row); // current row

    // 要特別檢查的欄位，source只能是 "2" 或 "3" 的情況才能填此欄位
    const checkSource = ["Land-->source"]
      .map((val) => {
        const idx = headerRow.findIndex((el) => el === val);
        if (idx !== -1) {
          return row.values[idx] ? row.values[idx].toString() : "";
        }
        return "-1";
      })
      .every((val) => ["2", "3"].includes(val));

    if (cell.value && !checkSource) {
      const reason = `欄位"現存資料"只能是"2"或"3"的情況才能填此欄位`;
      tmpStr += `${cell.address}, [${cell.value}], 欄位:${colLabel}，${reason}。\n`;
    }
  }

  if (!tmpStr) {
    tmpStr = checkLimitList(cell, worksheet, checkList);
  }

  return tmpStr;
};

export default checkPawnAndPlowing;
