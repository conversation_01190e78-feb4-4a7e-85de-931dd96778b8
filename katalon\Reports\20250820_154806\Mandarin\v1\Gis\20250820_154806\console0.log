Warning: NLS unused message: QTEST in: com.kms.katalon.constants.globalMessages
Warning: NLS missing message: QTEST_NAME in: com.kms.katalon.constants.globalMessages
2025-08-20 15:48:09.769 INFO  c.k.katalon.core.main.TestSuiteExecutor  - START Test Suites/Mandarin/v1/Gis
2025-08-20 15:48:09.906 INFO  c.k.katalon.core.main.TestSuiteExecutor  - userFullName = Lin Kai Yee
2025-08-20 15:48:09.906 INFO  c.k.katalon.core.main.TestSuiteExecutor  - projectName = katalon
2025-08-20 15:48:09.907 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostName = saw63 - host.docker.internal
2025-08-20 15:48:09.907 INFO  c.k.katalon.core.main.TestSuiteExecutor  - os = Windows 11 64bit
2025-08-20 15:48:09.907 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostAddress = ************
2025-08-20 15:48:09.908 INFO  c.k.katalon.core.main.TestSuiteExecutor  - katalonVersion = ********
2025-08-20 15:48:10.362 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-20 15:48:10.475 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 20, 2025 3:48:12 下午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-20 15:48:13.110 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 97e75b4f9585793af6c713839d063c4a
2025-08-20 15:48:13.112 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-20 15:48:13.113 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-20 15:48:13.114 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-20 15:48:13.127 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-20 15:48:14.085 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 15:48:14.085 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-20 15:48:14.229 DEBUG testcase.GoToGis                         - 1: openBrowser("")
2025-08-20 15:48:14.235 WARN  c.k.k.core.webui.driver.DriverFactory    - A browser is already opened. Closing browser and opening a new one
2025-08-20 15:48:14.515 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-20 15:48:14.518 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 20, 2025 3:48:15 下午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-20 15:48:15.747 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 6774d752169462fd45decc212dc9889d
2025-08-20 15:48:15.748 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-20 15:48:15.748 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-20 15:48:15.749 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-20 15:48:15.750 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-20 15:48:15.798 DEBUG testcase.GoToGis                         - 2: navigateToUrl("https://land2.daoyidh.com/Gis")
2025-08-20 15:48:16.220 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-20 15:48:16.231 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 15:48:16.231 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-20 15:48:16.336 DEBUG testcase.LoginWithEmail                  - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_"))
2025-08-20 15:48:17.979 DEBUG testcase.LoginWithEmail                  - 2: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail"))
2025-08-20 15:48:18.497 DEBUG testcase.LoginWithEmail                  - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Email_email"), "<EMAIL>")
2025-08-20 15:48:19.296 DEBUG testcase.LoginWithEmail                  - 4: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Next"))
2025-08-20 15:48:19.597 DEBUG testcase.LoginWithEmail                  - 5: setEncryptedText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Password_password"), "0D41p6Ct1VJ35FCiyaw3Gw==")
2025-08-20 15:48:20.540 DEBUG testcase.LoginWithEmail                  - 6: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign In"))
2025-08-20 15:48:20.838 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-20 15:48:20.842 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 15:48:20.842 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-20 15:48:20.883 DEBUG testcase.SearchTextInGis                 - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button__1"))
2025-08-20 15:48:23.382 DEBUG testcase.SearchTextInGis                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input__mui-3"), "新北勢")
2025-08-20 15:48:25.100 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-20 15:48:25.104 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 15:48:25.104 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-20 15:48:25.176 DEBUG testcase.ChoosePerPage                   - 1: dropdownButton = new com.kms.katalon.core.testobject.TestObject()
2025-08-20 15:48:25.183 DEBUG testcase.ChoosePerPage                   - 2: dropdownButton.addProperty("css", EQUALS, "div[role="button"]")
2025-08-20 15:48:25.195 DEBUG testcase.ChoosePerPage                   - 3: click(dropdownButton)
2025-08-20 15:48:25.599 DEBUG testcase.ChoosePerPage                   - 4: option50 = new com.kms.katalon.core.testobject.TestObject()
2025-08-20 15:48:25.604 DEBUG testcase.ChoosePerPage                   - 5: option50.addProperty("css", EQUALS, "li.MuiMenuItem-root[data-value="50"]")
2025-08-20 15:48:25.606 DEBUG testcase.ChoosePerPage                   - 6: waitForElementVisible(option50, 5)
2025-08-20 15:48:26.066 DEBUG testcase.ChoosePerPage                   - 7: click(option50)
2025-08-20 15:48:26.507 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-20 15:48:26.510 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 15:48:26.510 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-20 15:48:26.567 DEBUG testcase.EditCoordinates                 - 1: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/119-1_twd97_x"), "148485")
2025-08-20 15:48:27.111 DEBUG testcase.EditCoordinates                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/119-1_twd97_y"), "2406567 ")
2025-08-20 15:48:27.724 DEBUG testcase.EditCoordinates                 - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/1261_twd97_x"), "148485")
2025-08-20 15:48:28.203 DEBUG testcase.EditCoordinates                 - 4: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/1261_twd97_y"), "2406567 ")
2025-08-20 15:48:28.713 DEBUG testcase.EditCoordinates                 - 5: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination"))
2025-08-20 15:48:29.366 DEBUG testcase.EditCoordinates                 - 6: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/249_twd97_x"), "148485")
2025-08-20 15:48:29.807 DEBUG testcase.EditCoordinates                 - 7: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/249_twd97_y"), "2406567 ")
2025-08-20 15:48:30.345 DEBUG testcase.EditCoordinates                 - 8: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination_1"))
2025-08-20 15:48:30.990 DEBUG testcase.EditCoordinates                 - 9: verifyElementVisible(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/span_unsave_hint"))
2025-08-20 15:48:31.023 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-20 15:48:31.026 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 15:48:31.026 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SaveAndVerify
2025-08-20 15:48:31.071 DEBUG testcase.SaveAndVerify                   - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_save"))
2025-08-20 15:48:31.531 DEBUG testcase.SaveAndVerify                   - 2: waitForElementVisible(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button"), 5)
2025-08-20 15:48:32.013 DEBUG testcase.SaveAndVerify                   - 3: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button"))
2025-08-20 15:48:32.362 DEBUG testcase.SaveAndVerify                   - 4: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/119-1_twd97_x"), "value"), "148485")
2025-08-20 15:48:37.736 DEBUG testcase.SaveAndVerify                   - 5: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/119-1_twd97_y"), "value"), "2406567")
2025-08-20 15:48:37.771 DEBUG testcase.SaveAndVerify                   - 6: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/1261_twd97_x"), "value"), "148485")
2025-08-20 15:48:37.804 DEBUG testcase.SaveAndVerify                   - 7: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/1261_twd97_y"), "value"), "2406567")
2025-08-20 15:48:37.839 DEBUG testcase.SaveAndVerify                   - 8: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination"))
2025-08-20 15:48:38.282 DEBUG testcase.SaveAndVerify                   - 9: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/249_twd97_x"), "value"), "148485")
2025-08-20 15:48:38.328 DEBUG testcase.SaveAndVerify                   - 10: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Spy_element/249_twd97_y"), "value"), "2406567")
2025-08-20 15:48:38.357 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SaveAndVerify
2025-08-20 15:48:38.576 INFO  c.k.katalon.core.main.TestSuiteExecutor  - --------------------
2025-08-20 15:48:38.576 INFO  c.k.katalon.core.main.TestSuiteExecutor  - END Test Suites/Mandarin/v1/Gis
2025-08-20 15:48:38.576 INFO  c.k.katalon.core.main.TestSuiteExecutor  - ====================
