.Footer {
  width: 100%;
  padding: 16px 24px;
  background-color: var(--color-surface);
  border-top: 1px solid var(--color-border);
  margin-top: auto; // 使 footer 自動推到底部
  
  .Footer__content {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .Footer__copyright {
    display: flex;
    align-items: center;
    gap: 8px;
    
    // 響應式調整
    @media (max-width: 600px) {
      font-size: 0.875rem;
    }
  }
}