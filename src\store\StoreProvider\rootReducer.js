// combine
import combineReducer from "./combineReducer";

// reducer
import userReducer from "../reducers/userReducer";
import testReducer from "../reducers/testReducer";
import searchPageReducer from "../reducers/searchPageReducer";
import mainReducer from "../reducers/mainReducer";
import editPageReducer from "../reducers/editPageReducer";
import authorityPageReducer from "../reducers/authorityPageReducer";
import downloadPageReducer from "../reducers/downloadPageReducer";
import importReducer from "../reducers/importReducer";

// all in one
const reducers = combineReducer({
  user: userReducer,
  test: testReducer,
  search: searchPageReducer,
  main: mainReducer,
  edit: editPageReducer,
  authority: authorityPageReducer,
  download: downloadPageReducer,
  importData: importReducer,
});

export default reducers;
