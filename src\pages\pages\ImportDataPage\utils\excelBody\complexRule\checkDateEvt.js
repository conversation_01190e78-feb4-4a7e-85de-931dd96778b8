import { specRow } from "../../../config";
import evtType from "../../../../../../utils/evtType";

/**
 * 檢查日期事件
 * 注意：資料型別檢查（整數驗證）改由後端處理
 * 前端只保留業務邏輯檢查（重複日期）
 */
const checkDateEvt = (cell, worksheet, redStartDate) => {
  let tmpStr = "";

  const colLabel = worksheet
    .getRow(specRow.labelIdx)
    .getCell(cell.fullAddress.col).value;

  // 移除型別檢查，只保留業務邏輯檢查
  if (cell.value) {
    // 判斷同一筆土地，在標示變更或權力變更事件中有無重複日期
    const idSplitSymbol = "-->";
    const landEvtType = worksheet
      .getRow(specRow.idIdx)
      .getCell(cell.fullAddress.col)
      .value.split(idSplitSymbol)[2];

    if (landEvtType === evtType.LandMark) {
      if (!redStartDate.lm.includes(cell.value)) {
        redStartDate.lm.push(cell.value);
      } else {
        const reason = "同土地的土地標示登記（變更）時間中，不能存在相同年分";
        tmpStr += `${cell.address}, [${cell.value}], 欄位:${colLabel}，${reason}。\n`;
      }
    } else if (landEvtType === evtType.LandRights) {
      if (!redStartDate.lr.includes(cell.value)) {
        redStartDate.lr.push(cell.value);
      } else {
        const reason = "同土地的土地權力登記（變更）時間中，不能存在相同年分";
        tmpStr += `${cell.address}, [${cell.value}], 欄位:${colLabel}，${reason}。\n`;
      }
    }
  }

  return tmpStr;
};

export default checkDateEvt;
