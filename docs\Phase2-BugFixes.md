# Bug 修正記錄 - Phase 2

## 🐛 Bug #1: GisPage 儲存按鈕無法顯示 alert

### 問題描述
**報告時間**: 2025-10-02  
**報告者**: 使用者回饋  
**環境**: 正式站 (REACT_APP_ENV=production)  
**角色**: Developer

**問題**:
在 GisPage，使用 developer 角色登入正式站，點擊「儲存變更」按鈕**沒有任何 alert 顯示**。

**預期行為**:
應該顯示 alert 提示：「Developer 角色在正式站僅能查看資料，不可編輯」

---

### 根本原因分析

**原始錯誤程式碼** (`src/pages/pages/GisPage/index.js`):
```javascript
<Button
  variant="contained"
  color="primary"
  onClick={() => {
    if (!hasEditPermission) {
      alert(getPermissionMessage(user, OPERATION.EDIT));
      return;
    }
    setSaveModalOpen(true);
  }}
  disabled={!hasEditPermission || Object.keys(editedRows).length === 0 || ...}
>
  儲存變更
</Button>
```

**問題**:
1. 按鈕的 `disabled` 屬性包含了 `!hasEditPermission` 條件
2. 當 developer 在正式站時，`hasEditPermission = false`
3. **按鈕被設為 disabled，無法點擊**
4. `onClick` 事件永遠不會觸發，alert 永遠不會顯示

**邏輯錯誤**:
```
hasEditPermission = false
→ disabled = true
→ 按鈕無法點擊
→ onClick 不執行
→ alert 不顯示 ❌
```

---

### 修正方案

**修正後程式碼**:
```javascript
<Button
  variant="contained"
  color="primary"
  onClick={() => {
    if (!hasEditPermission) {
      alert(getPermissionMessage(user, OPERATION.EDIT));
      return;
    }
    setSaveModalOpen(true);
  }}
  disabled={Object.keys(editedRows).length === 0 || Object.values(errors).some(Boolean) || saving}
  // ↑ 移除 !hasEditPermission 條件
>
  儲存變更
</Button>
```

**變更說明**:
- ✅ 移除 `disabled` 中的 `!hasEditPermission` 條件
- ✅ 保留其他 disabled 條件（沒有修改、有錯誤、儲存中）
- ✅ 權限檢查完全由 `onClick` 內的邏輯處理

**修正後邏輯**:
```
hasEditPermission = false
→ disabled = false (只要有 editedRows)
→ 按鈕可以點擊 ✅
→ onClick 執行
→ 檢查 !hasEditPermission
→ 顯示 alert ✅
```

---

### 設計決策

**為什麼不用 disabled？**

方案 A (原始錯誤):
```javascript
disabled={!hasEditPermission || ...}
```
- ❌ 使用者無法互動
- ❌ 不知道為什麼按鈕是灰色的
- ❌ 沒有任何提示訊息

方案 B (修正後):
```javascript
disabled={...其他條件}  // 不包含權限
onClick={() => {
  if (!hasEditPermission) {
    alert('權限不足');  // 明確告知原因
    return;
  }
}}
```
- ✅ 使用者可以點擊按鈕
- ✅ 點擊後顯示明確的錯誤訊息
- ✅ 更好的使用者體驗

**UX 原則**:
> 與其讓按鈕 disabled 讓使用者困惑，不如讓使用者點擊並顯示明確的錯誤訊息

---

### 影響範圍

**受影響頁面**:
- ✅ GisPage (已修正)

**需要檢查的其他頁面**:
- ✅ EditPage - SaveButton: 按鈕 `disabled={disable}`，由 `lockUser` 控制，不受權限影響 ✓
- ✅ EditPage - EditButton: 按鈕 `disabled={disable}`，由 `lockUser` 控制，不受權限影響 ✓
- ✅ ImportDataPage: 頁面級阻擋，不存在按鈕 disabled 問題 ✓
- ✅ AuthorityPage: Checkbox 正確使用 `disabled={!hasManagePermission}`（預期行為）✓

**結論**: 只有 GisPage 有此問題，其他頁面的權限控制邏輯正確。

---

### 測試驗證

**測試步驟**:
1. 設定環境為正式站: `REACT_APP_ENV=production`
2. 使用 developer 帳號登入
3. 前往 GisPage
4. 修改任何座標值（製造 editedRows）
5. 點擊「儲存變更」按鈕

**預期結果**:
```
✅ 按鈕可以點擊（不是 disabled）
✅ 顯示 alert: "Developer 角色在正式站僅能查看資料，不可編輯"
✅ Modal 不會開啟
✅ 資料不會儲存
```

**實際結果** (修正後):
- ✅ Alert 正常顯示
- ✅ 權限檢查運作正常

---

### 經驗教訓

**教訓 1: disabled vs onClick 檢查**
- **錯誤做法**: 在 `disabled` 中包含權限檢查
  - 結果：使用者無法互動，沒有回饋
- **正確做法**: 在 `onClick` 中進行權限檢查並顯示訊息
  - 結果：使用者得到明確的錯誤提示

**教訓 2: 測試覆蓋度**
- Phase 2 實作時應該立即測試每個頁面
- 不要等到所有頁面完成才測試
- 建立測試清單並逐一驗證

**教訓 3: disabled 的使用場景**
- ✅ **適合用 disabled**: 資料未載入、表單未完成、正在儲存中
- ❌ **不適合用 disabled**: 權限不足（應該讓使用者點擊並顯示訊息）

---

### 相關文件

- Phase 2 完成總結: `docs/Phase2-Summary.md`
- GisPage 原始碼: `src/pages/pages/GisPage/index.js`
- 權限工具: `src/utils/permissionUtils.js`

---

### 修正 Commit 訊息建議

```
fix(GisPage): 修正 developer 在正式站無法看到權限警告的問題

問題：
- GisPage 儲存按鈕的 disabled 屬性包含 !hasEditPermission
- 導致 developer 在正式站時按鈕被 disabled，無法點擊
- onClick 中的 alert 永遠不會執行

修正：
- 移除 disabled 中的 !hasEditPermission 條件
- 保留其他 disabled 條件（無修改、有錯誤、儲存中）
- 權限檢查完全由 onClick 內的邏輯處理

影響：
- 修正後 developer 在正式站點擊儲存時會看到權限警告
- 其他角色和環境不受影響
```

---

## 🔍 未來改進建議

### 1. 統一按鈕權限處理模式
建立可重用的 PermissionButton 組件:
```javascript
<PermissionButton
  permission={canEdit(user)}
  onPermissionDenied={() => alert('權限不足')}
  onClick={handleSave}
  disabled={otherConditions}
>
  儲存變更
</PermissionButton>
```

### 2. 更好的 UX 設計
- 使用 Tooltip 顯示按鈕 disabled 原因
- 使用 Snackbar 取代 alert
- 在按鈕旁顯示權限提示 Icon

### 3. 自動化測試
- 為每個頁面的權限控制撰寫 E2E 測試
- 測試不同角色在不同環境的按鈕行為
- 確保 alert 訊息正確顯示

---

**修正完成時間**: 2025-10-02  
**修正者**: AI Assistant  
**驗證狀態**: ✅ 已修正，等待使用者測試確認
