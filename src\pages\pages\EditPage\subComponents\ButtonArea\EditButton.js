import React, { useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Button } from "@mui/material";
import { StoreContext } from "../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../utils";
import Act from "../../../../../store/actions";
import { Api, updateOntoData } from "../../../../../api/land/Api";
import { cancelEdit, checkBasicInfoById, getLockUser } from "../../common";
import { canEdit, getPermissionMessage, OPERATION } from "../../../../../utils/permissionUtils";

function EditButton({ showPermissionError }) {
  const [state, dispatch] = useContext(StoreContext);
  const { landId, lockUser } = state.edit;
  const { displayName } = state.user;
  const [disable, setDisable] = useState(true);

  useEffect(() => {
    if (!isEmpty(landId)) {
      if (lockUser) {
        if (displayName === lockUser) {
          // 編輯中，本人，顯示取消編輯
          setDisable(false);
        } else {
          // 編輯中，非本人
          setDisable(true);
        }
      } else {
        // 未編輯
        setDisable(false);
      }
    } else {
      // 未選擇地號、地段
      setDisable(true);
    }
  }, [landId, lockUser]);

  const checkStatus = async () => {
    // 權限檢查：確認使用者是否有編輯權限
    if (!canEdit(state.user)) {
      if (showPermissionError) {
        showPermissionError(getPermissionMessage(state.user, OPERATION.EDIT));
      }
      return;
    }

    dispatch({
      type: Act.SET_EDITLOADING,
      payload: true,
    });

    // 確認目前當下land的編輯狀態
    const currentBasicInfo = await checkBasicInfoById(state.edit);
    const findLockUser = currentBasicInfo.find(
      (info) => info.predicate === "lockUser"
    );

    if (findLockUser) {
      // 解鎖
      await cancelEdit(state, displayName, dispatch);
    } else {
      // 上鎖
      const entrySrc = {
        graph: "south",
        classType: "Land",
        srcId: landId,
        value: {},
      };
      const entryDst = {
        graph: "south",
        classType: "Land",
        srcId: landId,
        value: { 
          lockUser: displayName,
          lockStartTime: Date.now().toString()
        },
      };
      updateOntoData(Api.restfulCRUD(), entrySrc, entryDst, -1, 0).then(
        async (result) => {
          if (result) {
            dispatch({
              type: Act.SET_EDITLOADING,
              payload: false,
            });

            // 取得編輯者資料
            dispatch({
              type: Act.SET_LOCKERUSER,
              payload: await getLockUser(state.edit),
            });
          }
        }
      );
      dispatch({
        type: Act.SET_ISLOCK,
        payload: true,
      });
    }
  };

  const showLabel = () => {
    let content;
    if (!disable) {
      content = lockUser ? "取消編輯" : "點擊開始編輯";
    } else {
      content = lockUser ? `${lockUser} 編輯中` : "點擊開始編輯";
    }
    return content;
  };

  return (
    <Button
      className={disable ? "editBtnDisable" : "editBtn"}
      disabled={disable}
      onClick={checkStatus}
    >
      {showLabel()}
    </Button>
  );
}

EditButton.propTypes = {
  showPermissionError: PropTypes.func,
};

EditButton.defaultProps = {
  showPermissionError: null,
};

export default EditButton;
