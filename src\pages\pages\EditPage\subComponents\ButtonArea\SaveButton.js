import React, { useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";

// material ui
import Button from "@mui/material/Button";

// store
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

// utils
import { isEmpty } from "../../../../../utils";
import {
  cancelEdit,
  checkAllRule,
  compareBasicInfo,
  compareExtraBasic,
} from "../../common";
import { compareLandMarkInfo } from "../../common/editLandMark";
import { compareLandRightsInfo } from "../../common/editLandRight";
import { createHistoryEvent } from "../../common/produceHistory";
import { canEdit, getPermissionMessage, OPERATION } from "../../../../../utils/permissionUtils";

function SaveButton({ setAnchorEl, showPermissionError }) {
  const [state, dispatch] = useContext(StoreContext);
  const { landId, isLock, lockUser, landMarkInfo, landRightInfo, LSNList } =
    state.edit;
  const { displayName } = state.user;
  const [disable, setDisable] = useState(true);
  const [otherLSNList, setOtherLSNList] = useState([]);

  useEffect(() => {
    if (!landId || isEmpty(LSNList)) {
      setOtherLSNList([]);
      return;
    }
    const tmpList = LSNList.filter((el) => el.landId !== landId);
    setOtherLSNList(tmpList);
  }, [LSNList, landId]);

  useEffect(() => {
    if (isLock) return;
    // 解除lock狀態要做的事情
    // close popper
    dispatch({
      type: Act.SET_SHOWSAVEBTNPOPPER,
      payload: false,
    });
  }, [isLock]);

  useEffect(() => {
    if (!isEmpty(landId) && displayName === lockUser) {
      setDisable(false);
    } else {
      setDisable(true);
    }
  }, [landId, lockUser]);

  const checkNumber = (tmpInfo, predicate) => {
    const checkNum = tmpInfo.every((arrEl) => {
      const findObj = arrEl.find((el) => el.predicate === predicate);
      if (findObj) {
        return findObj.value !== "";
      }
      return false;
    });

    return checkNum;
  };

  const handleSave = async (event) => {
    // 權限檢查：確認使用者是否有編輯權限
    if (!canEdit(state.user)) {
      if (showPermissionError) {
        showPermissionError(getPermissionMessage(state.user, OPERATION.EDIT));
      }
      return;
    }

    // 檢查都有帶landMarkNumber、landRightsNumber
    // 儲存前，先檢查欄位規則，沒有錯誤才可以儲存
    if (
      checkAllRule(state, otherLSNList) ||
      !checkNumber(landMarkInfo, "landMarkNumber") ||
      !checkNumber(landRightInfo, "landRightsNumber")
    ) {
      setAnchorEl(event.currentTarget);
      dispatch({
        type: Act.SET_SHOWSAVEBTNPOPPER,
        payload: true,
      });
      return;
    }

    dispatch({
      type: Act.SET_EDITLOADING,
      payload: true,
    });

    let tmpHistoryRecord = "";

    // 比對箱號、作業者
    tmpHistoryRecord = await compareExtraBasic(state.edit);

    // 處理basicInfo
    tmpHistoryRecord += await compareBasicInfo(state.edit);

    // 處理landMarkInfo
    tmpHistoryRecord += await compareLandMarkInfo(state.edit);

    // 處理landRightInfo
    tmpHistoryRecord += await compareLandRightsInfo(state.edit, dispatch);

    // console.log("historyRecord ", tmpHistoryRecord);
    if (tmpHistoryRecord) {
      const date = new Date();
      const historyRecord = `${displayName}  ${date.toString()} \n ${tmpHistoryRecord}`;
      await createHistoryEvent(date, historyRecord, displayName, landId);
    }

    // 關閉lock狀態
    cancelEdit(state, displayName, dispatch);
  };

  return (
    <Button
      className={
        isLock && displayName === lockUser ? "saveBtn" : "saveBtnDisable"
      }
      onClick={handleSave}
      disabled={disable}
    >
      儲存
    </Button>
  );
}

SaveButton.propTypes = {
  setAnchorEl: PropTypes.func,
  showPermissionError: PropTypes.func,
};

SaveButton.defaultProps = {
  setAnchorEl: () => {},
  showPermissionError: null,
};

export default SaveButton;
