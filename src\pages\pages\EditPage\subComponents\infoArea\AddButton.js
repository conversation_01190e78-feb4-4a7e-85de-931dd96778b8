import React, { useContext } from "react";
import { Button } from "@mui/material";
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";
import {
  autoCompleteColumn,
  checkLandMarkInfo,
  checkLandRightsInfo,
  getColOrder,
} from "../../common";
import { isEmpty } from "../../../../../utils";

function AddButton({ itemType }) {
  const [state, dispatch] = useContext(StoreContext);
  const { landMarkInfo, landRightInfo } = state.edit;

  const addItem = async () => {
    if (itemType === "LMK") {
      if (!isEmpty(landMarkInfo)) {
        // create empty instance
        let newLankMarkInfo = JSON.parse(JSON.stringify(landMarkInfo[0]));
        newLankMarkInfo = newLankMarkInfo.map((element) => ({
          ...element,
          value: typeof element.value === "string" ? "" : [],
          landMarkId: "",
        }));

        const latestLandMarkInfo = await checkLandMarkInfo(state.edit);
        const maxNum = latestLandMarkInfo
          .filter((element) => element.predicate === "landMarkNumber")
          .map((item) => item.value);
        const maxNum2 = landMarkInfo.map((element) =>
          parseInt(
            element.find((item) => item.predicate === "landMarkNumber").value
          )
        );
        const maxNumber = Math.max(...[...maxNum, ...maxNum2]);
        const num = maxNumber + 1;
        newLankMarkInfo.find(
          (element) => element.predicate === "landMarkNumber"
        ).value = num.toString();

        const tmpLandMarkInfo = JSON.parse(JSON.stringify(landMarkInfo));
        tmpLandMarkInfo.push(newLankMarkInfo);

        dispatch({
          type: Act.SET_LANDMARKINFO,
          payload: tmpLandMarkInfo,
        });
      } else {
        // 第一個LandMarkInfo
        let colOrder = await getColOrder("LMK");
        colOrder = colOrder.filter(({ property }) => property !== "comment");
        const newLankMarkInfo = colOrder.map((element) => ({
          predicate: element.property,
          value:
            element.property === "landMarkNumber"
              ? "1"
              : element.property === "cause"
              ? []
              : "",
          landMarkId: "",
          object: element.label,
        }));
        dispatch({
          type: Act.SET_LANDMARKINFO,
          payload: [newLankMarkInfo],
        });
      }
    } else if (itemType === "LRT") {
      if (!isEmpty(landRightInfo)) {
        // create empty instance
        let newLankRightsInfo = JSON.parse(JSON.stringify(landRightInfo[0]));
        newLankRightsInfo = newLankRightsInfo.map((element) => ({
          ...element,
          value: typeof element.value === "string" ? "" : [],
          landRightsId: "",
        }));

        const latestLandRightsInfo = await checkLandRightsInfo(state.edit);
        const maxNum = latestLandRightsInfo
          .filter((element) => element.predicate === "landRightsNumber")
          .map((item) => item.value);
        const maxNum2 = landRightInfo.map((element) =>
          parseInt(
            element.find((item) => item.predicate === "landRightsNumber").value
          )
        );
        const num = Math.max(...[...maxNum, ...maxNum2]) + 1;
        newLankRightsInfo.find(
          (element) => element.predicate === "landRightsNumber"
        ).value = num.toString();

        const tmpLandRightsInfo = JSON.parse(JSON.stringify(landRightInfo));
        tmpLandRightsInfo.push(newLankRightsInfo);

        dispatch({
          type: Act.SET_LANDRIGHTINFO,
          payload: tmpLandRightsInfo,
        });
      } else {
        // 第一個LandRightInfo
        const colOrder = await getColOrder("LRT");
        const newLankRightsInfo = colOrder.map((element) => ({
          predicate: element.property,
          value:
            element.property === "landRightsNumber"
              ? "1"
              : autoCompleteColumn(element.property)
              ? []
              : "",
          landMarkId: "",
          object: element.label,
        }));
        dispatch({
          type: Act.SET_LANDRIGHTINFO,
          payload: [newLankRightsInfo],
        });
      }
    }
  };

  return (
    <Button onClick={addItem} className="addItemBtn">
      {itemType === "LMK" && "新增土地標示變更欄位"}
      {itemType === "LRT" && "新增土地權利變更欄位"}
    </Button>
  );
}

export default AddButton;
