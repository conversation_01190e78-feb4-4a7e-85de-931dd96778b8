import React, { useContext, useEffect, useState } from "react";
import {
  ClickAwayListener,
  Fade,
  Paper,
  Popper,
  Typography,
} from "@mui/material";
import Box from "@mui/material/Box";
import { StoreContext } from "../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../utils";
import { Api, readOntoData } from "../../../../../api/land/Api";
import { convert2HtmlEntities } from "../../../../../utils/htmlToReact";

const HistoryPopperStyle = {
  width: "35vw",
  height: "70vh",
  zIndex: "10",
};

const HistoryPapperStyle = {
  width: "100%",
  height: "100%",
  padding: "10px",
  overflowY: "scroll",
  border: "1px black solid",
};

function HistoryPopper({
  openHistoryPopper,
  setOpenHistoryPopper,
  historyIconPos,
}) {
  const [state, dispatch] = useContext(StoreContext);
  const { landName, landSerialNumber, basicInfo, isLock } = state.edit;
  const [historyRecord, setHistoryRecord] = useState("");

  useEffect(() => {
    let isMounted = true;
    if (isEmpty(landName) || isEmpty(landSerialNumber)) return;
    const apiStr = Api.getHistoryId()
      .replace("{landName}", landName)
      .replace("{landSerialNumber}", landSerialNumber);

    readOntoData(apiStr).then((result) => {
      // console.log(result.data);
      if (result.data.length > 0) {
        const allHisId = result.data.reduce(
          (cur, next) => [...cur, next.historyId],
          []
        );
        const allHistoryApiStr = Api.getHistoryEvents().replace(
          "{ids}",
          allHisId.join()
        );
        readOntoData(allHistoryApiStr).then((hisRecord) => {
          if (isMounted) {
            if (hisRecord.data.length > 0) {
              let tmpStr = "";
              const filterActions = hisRecord.data.filter(
                (element) => element.object === "action"
              );
              filterActions
                .slice()
                .reverse()
                .forEach((action, index) => {
                  tmpStr += action.value;
                  tmpStr += "\n---\n";
                });
              setHistoryRecord(tmpStr);
            }
          }
        });
      } else {
        setHistoryRecord("");
      }
    });
    return () => (isMounted = false);
  }, [landName, landSerialNumber]);

  return (
    <Popper
      style={HistoryPopperStyle}
      open={openHistoryPopper}
      anchorEl={historyIconPos}
      placement="bottom-end"
      transition
    >
      {({ TransitionProps }) => (
        <ClickAwayListener onClickAway={() => setOpenHistoryPopper(false)}>
          <Fade {...TransitionProps} timeout={350}>
            <Paper style={HistoryPapperStyle}>
              <Typography variant="h3" style={{ margin: "10px" }}>
                History:
              </Typography>
              <Box
                style={{
                  backgroundColor: "#F2F2DC",
                  borderRadius: "8px",
                  padding: "6px 8px",
                }}
              >
                {isEmpty(historyRecord) ? (
                  <Typography variant="h6" style={{ textAlign: "center" }}>
                    No history event
                  </Typography>
                ) : (
                  <div>{convert2HtmlEntities(historyRecord)}</div>
                )}
              </Box>
            </Paper>
          </Fade>
        </ClickAwayListener>
      )}
    </Popper>
  );
}

export default HistoryPopper;
