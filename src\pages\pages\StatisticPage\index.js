import React, { useState, useReducer, useEffect } from "react";

// scss
import "./StatisticPage.scss";

// material ui
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";
import Box from "@mui/material/Box";

// config
import { useHistory, useLocation } from "react-router-dom";
import queryString from "query-string";
import allOptions from "./config/allOptions";

// utils
import { isEmpty } from "../../../utils";
import hisAddSearch from "../../../utils/hisAddSearch";

// component
import CusDWBtn from "../../../Component/CusDWBtn/CusDWBtn";
import statAct from "./config/statAct";
import ShareButton from "../../../Component/ShareButton/ShareButton";

function StatisticPage() {
  const history = useHistory();
  const location = useLocation();
  const [typeOpt, setTypeOpt] = useState({});

  // useReducer for export data info
  const statInit = {
    expData: [],
    headerCols: [],
    fileName: "",
  };

  const [expState, dispatch] = useReducer((tmpState, action) => {
    switch (action.type) {
      case statAct.SET_EXPDATA:
        return { ...tmpState, expData: action.payload };
      case statAct.SET_HEADERCOLS:
        return { ...tmpState, headerCols: action.payload };
      case statAct.SET_FILENAME:
        return { ...tmpState, fileName: action.payload };
      default:
        return tmpState;
    }
  }, statInit);

  const initRender = () => <Box className="BtmArea-init">尚未選擇統計方式</Box>;

  useEffect(() => {
    const { type } = queryString.parse(location.search);
    if (type) {
      const findOpt = allOptions.find((el) => el.type === type);
      setTypeOpt(findOpt);
    }
  }, []);

  const handleChange = (evt, optObj) => {
    setTypeOpt(optObj);
    hisAddSearch(history, { type: optObj.type });
  };

  return (
    <div className="mainBox_shadowMain StatisticPage">
      <Box className="TopArea">
        <Autocomplete
          disablePortal
          options={allOptions}
          renderInput={(params) => (
            <TextField {...params} label="選擇統計資訊方式" />
          )}
          onChange={handleChange}
          style={{ flex: "1 1 auto" }}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          getOptionLabel={(option) => option.label || ""}
          value={typeOpt}
        />
        {!isEmpty(typeOpt) && (
          <>
            <CusDWBtn state={expState} />
            <ShareButton />
          </>
        )}
      </Box>
      <Box className="BtmArea">
        {isEmpty(typeOpt) ? (
          initRender()
        ) : (
          <Box className="BtmArea-content">
            <typeOpt.component statApi={typeOpt.apiStr} dispatch={dispatch} />
          </Box>
        )}
      </Box>
    </div>
  );
}

export default StatisticPage;
