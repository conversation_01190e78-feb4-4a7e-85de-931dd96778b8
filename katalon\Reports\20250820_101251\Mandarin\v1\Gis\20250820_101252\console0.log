Warning: NLS unused message: QTEST in: com.kms.katalon.constants.globalMessages
Warning: NLS missing message: QTEST_NAME in: com.kms.katalon.constants.globalMessages
2025-08-20 10:12:55.708 INFO  c.k.katalon.core.main.TestSuiteExecutor  - START Test Suites/Mandarin/v1/Gis
2025-08-20 10:12:55.801 INFO  c.k.katalon.core.main.TestSuiteExecutor  - userFullName = Lin Kai Yee
2025-08-20 10:12:55.805 INFO  c.k.katalon.core.main.TestSuiteExecutor  - projectName = katalon
2025-08-20 10:12:55.805 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostName = saw63 - host.docker.internal
2025-08-20 10:12:55.805 INFO  c.k.katalon.core.main.TestSuiteExecutor  - os = Windows 11 64bit
2025-08-20 10:12:55.805 INFO  c.k.katalon.core.main.TestSuiteExecutor  - hostAddress = ************
2025-08-20 10:12:55.805 INFO  c.k.katalon.core.main.TestSuiteExecutor  - katalonVersion = ********
2025-08-20 10:12:56.107 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-20 10:12:56.202 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 20, 2025 10:12:57 上午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-20 10:12:57.996 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 4ef896f4413fa54bcac175bb25b9b142
2025-08-20 10:12:58.002 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-20 10:12:58.003 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-20 10:12:58.003 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-20 10:12:58.011 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-20 10:12:58.536 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 10:12:58.536 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-20 10:12:58.605 DEBUG testcase.GoToGis                         - 1: openBrowser("")
2025-08-20 10:12:58.610 WARN  c.k.k.core.webui.driver.DriverFactory    - A browser is already opened. Closing browser and opening a new one
2025-08-20 10:12:58.789 INFO  c.k.k.core.webui.driver.DriverFactory    - Starting 'Chrome' driver
2025-08-20 10:12:58.792 INFO  c.k.k.core.webui.driver.DriverFactory    - Action delay is set to 0 milliseconds
8月 20, 2025 10:12:59 上午 org.openqa.selenium.devtools.CdpVersionFinder findNearestMatch
警告: Unable to find an exact match for CDP version 138, returning the closest version; found: 135; Please update to a Selenium version that supports CDP version 138
2025-08-20 10:12:59.737 INFO  c.k.k.core.webui.driver.DriverFactory    - sessionId = 93ce76b7168566a82179e5a5c1a9ff22
2025-08-20 10:12:59.737 INFO  c.k.k.core.webui.driver.DriverFactory    - browser = Chrome 138.0.7204.185
2025-08-20 10:12:59.737 INFO  c.k.k.core.webui.driver.DriverFactory    - platform = Windows 11
2025-08-20 10:12:59.737 INFO  c.k.k.core.webui.driver.DriverFactory    - seleniumVersion = 4.28.1
2025-08-20 10:12:59.737 INFO  c.k.k.core.webui.driver.DriverFactory    - proxyInformation = ProxyInformation { proxyOption=NO_PROXY, proxyServerType=HTTP, username=, password=********, proxyServerAddress=, proxyServerPort=0, executionList="", isApplyToDesiredCapabilities=true }
2025-08-20 10:12:59.765 DEBUG testcase.GoToGis                         - 2: navigateToUrl("https://land2.daoyidh.com/Gis")
2025-08-20 10:13:00.137 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/GoToGis
2025-08-20 10:13:00.144 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 10:13:00.144 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-20 10:13:00.215 DEBUG testcase.LoginWithEmail                  - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_"))
2025-08-20 10:13:01.613 DEBUG testcase.LoginWithEmail                  - 2: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign in with emailEmail"))
2025-08-20 10:13:02.148 DEBUG testcase.LoginWithEmail                  - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Email_email"), "<EMAIL>")
2025-08-20 10:13:02.963 DEBUG testcase.LoginWithEmail                  - 4: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Next"))
2025-08-20 10:13:03.247 DEBUG testcase.LoginWithEmail                  - 5: setEncryptedText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input_Password_password"), "0D41p6Ct1VJ35FCiyaw3Gw==")
2025-08-20 10:13:04.202 DEBUG testcase.LoginWithEmail                  - 6: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_Sign In"))
2025-08-20 10:13:04.482 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/LoginWithEmail
2025-08-20 10:13:04.486 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 10:13:04.486 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-20 10:13:04.531 DEBUG testcase.SearchTextInGis                 - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button__1"))
2025-08-20 10:13:07.152 DEBUG testcase.SearchTextInGis                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/input__mui-3"), "新北勢")
2025-08-20 10:13:08.884 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SearchTextInGis
2025-08-20 10:13:08.889 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 10:13:08.889 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-20 10:13:09.017 DEBUG testcase.ChoosePerPage                   - 1: dropdownButton = new com.kms.katalon.core.testobject.TestObject()
2025-08-20 10:13:09.032 DEBUG testcase.ChoosePerPage                   - 2: dropdownButton.addProperty("css", EQUALS, "div[role="button"]")
2025-08-20 10:13:09.046 DEBUG testcase.ChoosePerPage                   - 3: click(dropdownButton)
2025-08-20 10:13:09.518 DEBUG testcase.ChoosePerPage                   - 4: option50 = new com.kms.katalon.core.testobject.TestObject()
2025-08-20 10:13:09.522 DEBUG testcase.ChoosePerPage                   - 5: option50.addProperty("css", EQUALS, "li.MuiMenuItem-root[data-value="50"]")
2025-08-20 10:13:09.525 DEBUG testcase.ChoosePerPage                   - 6: waitForElementVisible(option50, 5)
2025-08-20 10:13:10.119 DEBUG testcase.ChoosePerPage                   - 7: click(option50)
2025-08-20 10:13:11.099 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/ChoosePerPage
2025-08-20 10:13:11.104 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 10:13:11.105 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-20 10:13:11.292 DEBUG testcase.EditCoordinates                 - 1: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "148485")
2025-08-20 10:13:12.216 DEBUG testcase.EditCoordinates                 - 2: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "2406567 ")
2025-08-20 10:13:12.834 DEBUG testcase.EditCoordinates                 - 3: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "148485")
2025-08-20 10:13:13.300 DEBUG testcase.EditCoordinates                 - 4: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "2406567 ")
2025-08-20 10:13:13.812 DEBUG testcase.EditCoordinates                 - 5: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination"))
2025-08-20 10:13:14.472 DEBUG testcase.EditCoordinates                 - 6: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "148485")
2025-08-20 10:13:14.894 DEBUG testcase.EditCoordinates                 - 7: setText(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "2406567 ")
2025-08-20 10:13:15.349 DEBUG testcase.EditCoordinates                 - 8: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination_1"))
2025-08-20 10:13:16.008 DEBUG testcase.EditCoordinates                 - 9: verifyElementVisible(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/span_unsave_hint"))
2025-08-20 10:13:16.045 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/EditCoordinates
2025-08-20 10:13:16.050 INFO  c.k.katalon.core.main.TestCaseExecutor   - --------------------
2025-08-20 10:13:16.050 INFO  c.k.katalon.core.main.TestCaseExecutor   - START Test Cases/Mandarin/v1_web/Gis/SaveAndVerify
2025-08-20 10:13:16.076 DEBUG testcase.SaveAndVerify                   - 1: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/button_save"))
2025-08-20 10:13:16.535 DEBUG testcase.SaveAndVerify                   - 2: waitForElementVisible(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button"), 5)
2025-08-20 10:13:16.996 DEBUG testcase.SaveAndVerify                   - 3: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/save_modal_save_button"))
2025-08-20 10:13:17.329 DEBUG testcase.SaveAndVerify                   - 4: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_x"), "value"), "148485")
2025-08-20 10:13:22.599 DEBUG testcase.SaveAndVerify                   - 5: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/119-1_twd97_y"), "value"), "2406567")
2025-08-20 10:13:22.637 DEBUG testcase.SaveAndVerify                   - 6: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_x"), "value"), "148485")
2025-08-20 10:13:22.668 DEBUG testcase.SaveAndVerify                   - 7: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/1261_twd97_y"), "value"), "2406567")
2025-08-20 10:13:22.704 DEBUG testcase.SaveAndVerify                   - 8: click(findTestObject("Object Repository/Mandarin/v1_web/Gis/pagination"))
2025-08-20 10:13:23.138 DEBUG testcase.SaveAndVerify                   - 9: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_x"), "value"), "148485")
2025-08-20 10:13:23.190 DEBUG testcase.SaveAndVerify                   - 10: verifyEqual(getAttribute(findTestObject("Object Repository/Mandarin/v1_web/Gis/Page_/249_twd97_y"), "value"), "2406567")
2025-08-20 10:13:23.220 INFO  c.k.katalon.core.main.TestCaseExecutor   - END Test Cases/Mandarin/v1_web/Gis/SaveAndVerify
2025-08-20 10:13:23.399 INFO  c.k.katalon.core.main.TestSuiteExecutor  - --------------------
2025-08-20 10:13:23.399 INFO  c.k.katalon.core.main.TestSuiteExecutor  - END Test Suites/Mandarin/v1/Gis
2025-08-20 10:13:23.399 INFO  c.k.katalon.core.main.TestSuiteExecutor  - ====================
