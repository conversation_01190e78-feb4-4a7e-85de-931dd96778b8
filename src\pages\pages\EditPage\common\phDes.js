// keyName(對應API回傳的"predicate" property)對應不同的placeholder描述

// 基本資料
const basicPlaceHolder = {
  collectionPlace: "地政事務所名",
  boxNumber: "外箱編號",
  operator: "助理姓名。",
  number: "指該土名範圍中第n筆",
  landName: "",
  landSerialNumber: "",
  abstract: "指土名番號變更過程，輸入｢年份+原因+原土名地號｣",
  source:
    "只有台帳=1，只有日據簿=2，兩者皆有=3，只有其他種資料=4，無資料=0，資料印製有誤=5",
  pawnRight: "上欄為2或3才需輸入，指日據簿乙區有否記錄。有=Y，無＝N",
  plowingRight: "上欄為2或3才需輸入，指日據簿丙區有否記錄。文字：有=Y，無＝N",
  twd97_x: "請輸入經度（TWD97）",
  twd97_y: "請輸入緯度（TWD97）",
  wgs84_long: "由系統自動計算（WGS84經度）",
  wgs84_lat: "由系統自動計算（WGS84緯度）",
};

// 土地標示變更
const landMarkPlaceHolder = {
  landMarkNumber: "指土地標示登記第n次，同一年份合成1次",
  cause:
    "指土地變成該狀態的原因，同一年的事件即便台帳分在兩行也合成1筆，以全形逗號分開。台帳同一行的事件若為不同年份應分成兩筆。",
  hasStartDate:
    "西元年，指土地變成該狀態的年份，原則上輸入發生年而非辦理手續（處分）年，不可重複",
  landGrades:
    "阿拉伯數字，指土地等則。登記原因為荒地成免租、再荒免租時，輸入時需改為0",
  landCategory:
    "指當時土地使用情況，畑要改為旱田。登記原因為荒地成免租、再荒免租時，輸入時需改為荒地",
  landArea: "阿拉伯數字，指土地面積",
  landRent:
    "阿拉伯數字，指土地課稅金額。登記原因為荒地成免租、再荒免租時，輸入時需改為0，新規賦租以前地租皆為0",
};

// 土地權利變更
const landRightsPlaceHolder = {
  landRightsNumber: "指土地權利登記第n次，同一年份合成1次",
  hasStartDate: "西元年，指產權取得、登記或變動的時間",
  cause: "指產權取得、登記或變動的原因。有關承典、住所變更、改名等皆省略不輸入",
  hasOwner:
    "指業主姓名，業主有多人時要全部列出，以半形逗號分開。有管理者時在後方括號（xxx管理）。同一年份的事件合成1筆，輸入該年最終的業主",
};

export { basicPlaceHolder, landMarkPlaceHolder, landRightsPlaceHolder };
