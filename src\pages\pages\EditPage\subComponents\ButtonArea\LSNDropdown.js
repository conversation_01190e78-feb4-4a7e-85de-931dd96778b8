import React, { useContext, useEffect } from "react";

// material ui
import Grid from "@mui/material/Grid";
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";

// utils
import { StoreContext } from "../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../utils";
import { Api, readOntoData } from "../../../../../api/land/Api";
import Act from "../../../../../store/actions";

function LSNDropdown() {
  const [state, dispatch] = useContext(StoreContext);
  const { landName, landSerialNumber, LSNList, isLock, lockUser } = state.edit;
  const { displayName } = state.user;

  useEffect(() => {
    if (isEmpty(LSNList) || isEmpty(landSerialNumber)) return;
    // 設定selctionIndex
    const findIndex = LSNList.findIndex(
      (element) => element.landSerialNumber === landSerialNumber
    );
    dispatch({
      type: Act.SET_SELECTIONINDEX,
      payload: findIndex,
    });
  }, [LSNList, landSerialNumber]);

  useEffect(() => {
    if (isEmpty(landName) || landName === null) return;
    const apiStr = Api.getLSList().replace("{landName}", landName);
    readOntoData(apiStr).then((result) => {
      dispatch({
        type: Act.SET_LSNLIST,
        payload: result.data,
      });
    });
  }, [landName]);

  return (
    <Grid item xs={5}>
      <Autocomplete
        disablePortal
        options={LSNList.map((element) => element.landSerialNumber)}
        renderInput={(params) => <TextField {...params} label="地番（地號）" />}
        value={landSerialNumber}
        onChange={(event, newValue) => {
          let tmpValue = newValue;
          if (newValue === null) {
            tmpValue = "";
          }
          dispatch({
            type: Act.SET_LANDID,
            payload: "",
          });
          dispatch({
            type: Act.SET_LANDSERIALNUMBER,
            payload: tmpValue,
          });
          const findIndex = LSNList.findIndex(
            (element) => element.landSerialNumber === tmpValue
          );
          if (findIndex) {
            dispatch({
              type: Act.SET_SELECTIONINDEX,
              payload: findIndex,
            });
          }
        }}
        disabled={isLock && displayName === lockUser}
      />
    </Grid>
  );
}

export default LSNDropdown;
