import React from 'react';
import PropTypes from 'prop-types';
import Alert from '@mui/material/Alert';
import AlertTitle from '@mui/material/AlertTitle';
import Box from '@mui/material/Box';
import InfoIcon from '@mui/icons-material/Info';
import WarningIcon from '@mui/icons-material/Warning';
import ErrorIcon from '@mui/icons-material/Error';

/**
 * PermissionHint - 頁面級權限提示組件
 * 
 * 用於在頁面頂部顯示使用者當前的權限狀態和限制
 * 
 * @param {Object} props
 * @param {boolean} props.show - 是否顯示提示（預設 true）
 * @param {string} props.title - 提示標題
 * @param {string} props.message - 提示訊息內容
 * @param {string} props.severity - 嚴重程度 ('info' | 'warning' | 'error')，預設 'info'
 * @param {React.ReactNode} props.icon - 自定義圖示
 * @param {Object} props.sx - 自定義樣式
 */
const PermissionHint = ({
  show = true,
  title,
  message,
  severity = 'info',
  icon = null,
  sx = {},
}) => {
  if (!show) {
    return null;
  }

  // 根據 severity 選擇預設圖示
  const getDefaultIcon = () => {
    switch (severity) {
      case 'warning':
        return <WarningIcon fontSize="inherit" />;
      case 'error':
        return <ErrorIcon fontSize="inherit" />;
      case 'info':
      default:
        return <InfoIcon fontSize="inherit" />;
    }
  };

  return (
    <Box
      sx={{
        width: '100%',
        mb: 2,
        ...sx,
      }}
    >
      <Alert
        severity={severity}
        icon={icon || getDefaultIcon()}
        sx={{
          '& .MuiAlert-message': {
            width: '100%',
          },
        }}
      >
        {title && <AlertTitle>{title}</AlertTitle>}
        {message}
      </Alert>
    </Box>
  );
};

PermissionHint.propTypes = {
  show: PropTypes.bool,
  title: PropTypes.string,
  message: PropTypes.string.isRequired,
  severity: PropTypes.oneOf(['info', 'warning', 'error']),
  icon: PropTypes.node,
  sx: PropTypes.shape({}),
};

PermissionHint.defaultProps = {
  show: true,
  title: null,
  severity: 'info',
  icon: null,
  sx: {},
};

export default PermissionHint;
