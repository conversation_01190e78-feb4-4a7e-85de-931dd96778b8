import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";

// d3
import * as d3 from "d3";

// utils
import { isEmpty } from "../../../../../utils";

// refer: https://dataviz.unhcr.org/tools/d3/d3_grouped_column_chart.html
function SpecChart({ data }) {
  const divID = "SpecChart";
  const [svgObj, setSvgObj] = useState({});
  const palette = ["#f44336", "#008CBA"];

  const initChart = () => {
    // 定義畫框邊界
    const inWidth = document.querySelector(".chartDiv").clientWidth;
    const inHeight = document.querySelector(".chartDiv").clientHeight;
    const margin = { top: 30, right: 30, bottom: 70, left: 60 };
    const width = inWidth - margin.left - margin.right;
    const height = inHeight - margin.top - margin.bottom;

    // append the svg object to the body of the page
    const svg = d3
      .select(`#${divID}`)
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top * 2.5})`);

    // Initialize the X axis
    const xAxis = svg
      .append("g")
      .attr("class", "xAxis")
      .attr("transform", `translate(0,${height})`);

    // Initialize the Y axis
    const yAxis = svg.append("g").attr("class", "myYaxis");

    // color palette
    const color = d3
      .scaleOrdinal()
      .domain(["lmCount", "lrCount"])
      .range(palette);

    // set legend
    svg
      .append("rect")
      .attr("x", -margin.left * 0.6)
      .attr("y", -margin.top - 12)
      .attr("width", 13)
      .attr("height", 13)
      .style("fill", color("lmCount"));
    svg
      .append("text")
      .attr("class", "legend")
      .attr("x", -margin.left * 0.6 + 20)
      .attr("y", -margin.top)
      .text("土地標示變更次數");

    svg
      .append("rect")
      .attr("x", -margin.left * 0.6 + 200)
      .attr("y", -margin.top - 12)
      .attr("width", 13)
      .attr("height", 13)
      .style("fill", color("lrCount"));
    svg
      .append("text")
      .attr("class", "legend")
      .attr("x", -margin.left * 0.6 + 220)
      .attr("y", -margin.top)
      .text("土地權利變更次數");

    setSvgObj({ svg, height, width, xAxis, yAxis, color });
  };

  useEffect(() => {
    initChart();
  }, []);

  useEffect(() => {
    if (isEmpty(svgObj)) return;
    const { svg, width, height, xAxis, yAxis, color } = svgObj;
    // Update X axis with data
    const xDomain = data.map((el) => el.landSN);
    const x = d3.scaleBand().range([0, width]).domain(xDomain).padding(0.2);
    xAxis.transition().duration(1000).call(d3.axisBottom(x));

    // Update Y axis with data
    const yDomain = [0, d3.max(data, (d) => Math.max(d.lmCount, d.lrCount))];
    const y = d3.scaleLinear().range([height, 0]).domain(yDomain);
    yAxis.transition().duration(1000).call(d3.axisLeft(y).ticks(10));

    // Data Group Bar Chart
    const transData = data.map((d) => ({
      landSN: d.landSN,
      lmCount: +d.lmCount,
      lrCount: +d.lrCount,
    }));

    // Draw the bars for lmCount
    svg
      .selectAll(".lm-bar")
      .data(transData)
      .join("rect")
      .transition()
      .duration(1000)
      .attr("class", "lm-bar")
      .attr("x", (d) => x(d.landSN))
      .attr("width", x.bandwidth() / 2)
      .attr("y", (d) => y(d.lmCount))
      .attr("height", (d) => height - y(d.lmCount))
      .style("fill", color("lmCount"));

    // Draw landMark count
    svg
      .selectAll(".lm-count")
      .data(transData)
      .join("text") // 顯示數量在柱狀圖上
      .transition()
      .duration(1000)
      .attr("class", "lm-count")
      .attr("x", (d) => x(d.landSN) + x.bandwidth() / 4)
      .attr("y", (d) => y(d.lmCount))
      .attr("text-anchor", "middle")
      .text((d) => d.lmCount);

    // Draw the bars for lrCount
    svg
      .selectAll(".lr-bar")
      .data(transData)
      .join("rect")
      .transition()
      .duration(1000)
      .attr("class", "lr-bar")
      .attr("x", (d) => x(d.landSN) + x.bandwidth() / 2)
      .attr("width", x.bandwidth() / 2)
      .attr("y", (d) => y(d.lrCount))
      .attr("height", (d) => height - y(d.lrCount))
      .style("fill", color("lrCount"));

    // Draw landRights count
    svg
      .selectAll(".lr-count")
      .data(transData)
      .join("text") // 顯示數量在柱狀圖上
      .transition()
      .duration(1000)
      .attr("class", "lr-count")
      .attr("x", (d) => x(d.landSN) + (x.bandwidth() / 4) * 3)
      .attr("y", (d) => y(d.lrCount))
      .attr("text-anchor", "middle")
      .text((d) => d.lrCount);
  }, [data, svgObj]);

  return <div id={divID} style={{ height: "100%" }} />;
}

SpecChart.propTypes = {
  /** 從API取得統計資料 */
  data: PropTypes.arrayOf(
    PropTypes.shape({
      landId: PropTypes.string,
      landSN: PropTypes.string,
      lmCount: PropTypes.string,
      lrCount: PropTypes.string,
    })
  ),
};

SpecChart.defaultProps = {
  /** 從API取得統計資料 */
  data: [],
};
export default SpecChart;
