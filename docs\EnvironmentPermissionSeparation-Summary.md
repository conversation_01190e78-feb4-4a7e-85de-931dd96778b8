# 權限管控環境區分 - 快速參考

## 團隊決策摘要

### 技術方案

1. ✅ **共用 Firebase 專案**：正式站與測試站使用相同的 Firebase 專案
2. ✅ **雙角色機制**：
   - `role`：正式站使用
   - `roleDev`：測試站使用
3. ✅ **Firebase 配置維持硬編碼**：不改為環境變數
4. ✅ **Developer 角色保留**：正式站保留但限制權限

### 權限規則

#### Developer 角色權限對照表

| 功能 | 正式站 | 測試站 |
|-----|-------|-------|
| 查看資料 | ✅ | ✅ |
| 編輯資料 | ❌ | ✅ |
| 刪除資料 | ❌ | ✅ |
| 匯入資料 | ❌ (完全禁止) | ✅ |
| 匯出資料 | ✅ | ✅ |
| 查看權限頁面 | ✅ | ✅ |
| 編輯使用者權限 | ❌ (僅 Admin) | ❌ (僅 Admin) |

## 實作計畫

### Phase 1：基礎架構（1 週）🔴
- 新增 `REACT_APP_ENV` 環境變數
- 建立 `environmentUtils.js` 和 `permissionUtils.js`
- 使用者資料新增 `roleDev` 欄位
- 執行資料遷移腳本

### Phase 2：頁面權限控制（1.5 週）🟠
- EditPage: 正式站 developer 不可編輯
- GisPage: 正式站 developer 不可修改座標
- ImportDataPage: 正式站 developer 完全禁止存取
- AuthorityPage: Developer 僅可查看，不可編輯使用者

### Phase 3：UI/UX 改善（1 週）🟡
- 環境標示（正式站=紅色，測試站=藍色）
- 角色顯示
- 權限提示優化

### Phase 4-5：測試與文檔（1.5 週）🟢
- 單元測試與整合測試
- 使用者驗收測試
- 撰寫文檔

## 核心程式碼片段

### 1. 取得使用者角色（根據環境）

```javascript
import { getUserRoleByEnvironment } from '../utils/environmentUtils';

const currentRole = getUserRoleByEnvironment(user);
// 正式站: 使用 user.role
// 測試站: 使用 user.roleDev
```

### 2. 檢查編輯權限

```javascript
import { canEdit, getPermissionMessage, OPERATION } from '../utils/permissionUtils';

const canUserEdit = canEdit(user);

if (!canUserEdit) {
  alert(getPermissionMessage(user, OPERATION.EDIT));
  return;
}

// 執行編輯邏輯
```

### 3. 檢查匯入權限

```javascript
import { canImport } from '../utils/permissionUtils';

if (!canImport(user)) {
  return (
    <Alert severity="warning">
      Developer 角色在正式站禁止匯入資料
    </Alert>
  );
}
```

### 4. 使用者資料結構

```json
{
  "users": {
    "uid123": {
      "uid": "uid123",
      "email": "<EMAIL>",
      "displayName": "使用者",
      "role": "developer",       // 正式站角色
      "roleDev": "developer"     // 測試站角色
    }
  }
}
```

## 快速檢查清單

### 開發時檢查

- [ ] 使用 `getUserRoleByEnvironment()` 取得角色（不直接用 `user.role`）
- [ ] 編輯功能前檢查 `canEdit(user)`
- [ ] 匯入功能前檢查 `canImport(user)`
- [ ] 管理使用者前檢查 `canManageUsers(user)`
- [ ] 無權限時顯示 `getPermissionMessage()`

### 測試時檢查

- [ ] 正式站 developer 無法編輯資料
- [ ] 正式站 developer 無法匯入資料
- [ ] 測試站 developer 有完整編輯權限
- [ ] 所有環境 admin 有完整權限
- [ ] UI 明確顯示當前環境與角色

### 部署時檢查

- [ ] `.env.production` 設定 `REACT_APP_ENV=production`
- [ ] `.env.development` 設定 `REACT_APP_ENV=development`
- [ ] Firebase 所有使用者都有 `role` 和 `roleDev` 欄位
- [ ] 環境標示正確顯示

## 常見問題

### Q1: 為什麼不建立獨立的測試站 Firebase 專案？
**A**: 團隊決策考量管理成本，選擇共用 Firebase 專案，透過雙角色機制達到權限分離。

### Q2: 如何處理現有使用者？
**A**: 執行資料遷移腳本，自動為所有使用者新增 `roleDev` 欄位，預設值與 `role` 相同。

### Q3: Developer 在正式站完全無法編輯嗎？
**A**: 是的。正式站 developer 可以查看所有頁面，但編輯、刪除、匯入功能都會被禁用。

### Q4: 如何給某個使用者不同的測試站與正式站權限？
**A**: 在 Firebase Realtime Database 中，獨立設定該使用者的 `role` 和 `roleDev`。例如：
```json
{
  "role": "editor",      // 正式站: editor
  "roleDev": "developer" // 測試站: developer
}
```

### Q5: 如何確認當前環境？
**A**: 
1. 程式中使用 `isProduction()` 或 `getCurrentEnvironment()`
2. UI 上查看環境標示（紅色=正式站，藍色=測試站）
3. Console 執行 `process.env.REACT_APP_ENV`

## 相關文件

- **完整分析報告**: `SecurityAnalysis-EnvironmentPermissionSeparation.md`
- **環境工具函數**: `src/utils/environmentUtils.js`（待建立）
- **權限工具函數**: `src/utils/permissionUtils.js`（待建立）
- **資料遷移腳本**: `scripts/migrateUserRoles.js`（待建立）

## 聯絡資訊

- **技術負責人**: [待填寫]
- **產品負責人**: [待填寫]
- **預計完成日期**: [開始日期 + 4 週]

---

**最後更新**: 2025-10-02  
**版本**: 1.0（根據團隊決策調整）
