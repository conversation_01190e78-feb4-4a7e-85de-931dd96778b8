// 取得LandID
import { Api, readOntoData } from "../api/land/Api";

const getLandID = (landName, landSerialNumber) =>
  new Promise((resolve, reject) => {
    const apiStr = Api.getBasicInfo()
      .replace("{landName}", landName)
      .replace("{landSerialNumber}", landSerialNumber);
    readOntoData(apiStr)
      .then((result) => {
        const findLandId = result.data.find((obj) => obj.landId);
        resolve(findLandId ? findLandId.landId : findLandId);
      })
      .catch((err) => reject(err));
  });

export default getLandID;
