import Act from "../actions";

const initState = {
  landType: [], // 選擇要下載土地權利或是土地標示資料
  dataFormType: [
    {
      type: "type1",
      check: true,
      label: {
        LandMark:
          "特定期間特定地區各筆土地標示之變化（含變更原因、地目則別、面積及地租）",
        LandRights: "特定期間特定地區各筆土地權利之變化（含變更原因、業主）",
      },
    },
    {
      type: "type2",
      check: false,
      label: {
        LandMark:
          "特定期間特定土地標示之變化（含變更原因、地目則別、面積及地租）",
        LandRights: "特定期間特定土地權利之變化（含變更原因、業主）",
      },
    },
    {
      type: "type3",
      check: false,
      label: {
        LandMark:
          "特定年份特定地區各筆土地之標示（含變更原因、地目則別、面積及地租變化）",
        LandRights: "特定年份特定地區各筆土地之權利（含變更原因、業主）",
      },
    },
  ], // 確認要搜尋的下載資料欄位
  dwLandName: "", // 土名
  dwLandSerialNumber: "", // 地號
  startYear: "", // 開始年分
  endYear: "", // 結束年分
  selectDateType: "single", // 選擇搜尋年份方式
};

const authorityPageReducer = (state = initState, action) => {
  switch (action.type) {
    case Act.SET_LANDTYPE:
      return { ...state, landType: action.payload };
    case Act.SET_DATAFORMTYPE:
      return { ...state, dataFormType: action.payload };
    case Act.SET_DWLANDNAME:
      return { ...state, dwLandName: action.payload };
    case Act.SET_DWLANDSERIALNUMBER:
      return { ...state, dwLandSerialNumber: action.payload };
    case Act.SET_STARTYEAR:
      return { ...state, startYear: action.payload };
    case Act.SET_ENDYEAR:
      return { ...state, endYear: action.payload };
    case Act.SET_SELECTDATETYPE:
      return { ...state, selectDateType: action.payload };
    default:
      return state;
  }
};

export default authorityPageReducer;
