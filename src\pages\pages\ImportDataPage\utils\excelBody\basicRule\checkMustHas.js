import { specRow } from "../../../config";

/**
 * 檢查說明: 必填欄位
 * */
const checkMustHas = (cell, worksheet) => {
  const colLabel = worksheet
    .getRow(specRow.labelIdx)
    .getCell(cell.fullAddress.col).value;

  let tmpResStr = "";

  if (!cell.value) {
    const reason = "不能空白";
    tmpResStr += `${cell.address}, [${cell.value}], 欄位:${colLabel}，${reason}。\n`;
  }

  return tmpResStr;
};

export default checkMustHas;
