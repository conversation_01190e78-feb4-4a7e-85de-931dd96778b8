@use "../../../scss/common";

// 使用全域配色，移除重複定義

.AuthorityPage {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.AuthorityPage__tabWrapper {
  padding-bottom: 8px;
}

.AuthorityPage__panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.AuthorityPage__filterBar {
  display: flex;
  justify-content: flex-end;
}

.AuthorityPage__featureList {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;

  // 權限層級分組樣式
  .MuiChip-root {
    transition: all 0.2s ease;
    font-weight: 500;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    // 權限層級樣式統一透過 sx prop 定義，使用全域配色
    // 保持通用的懸停效果
    &.MuiChip-root {
      cursor: default;
      transition: all 0.2s ease;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-surface);
      }
    }
  }

  // 響應式調整
  @media (max-width: 768px) {
    gap: 4px;
    
    .MuiChip-root {
      font-size: 0.75rem;
    }
  }
}

.AuthorityPage__userName {
  font-weight: 600;
}

.AuthorityPage__placeholder {
  min-height: 240px;
  border: 1px dashed var(--color-border);
  border-radius: 12px;
  background-color: var(--color-surface);
  box-shadow: var(--shadow-surface);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 32px;
}

.AuthorityPage__placeholderContent {
  max-width: 420px;
}

.AuthorityPage__placeholderTitle {
  margin-bottom: 8px;
}

.ValidUser {
  border: 1px solid var(--color-border);
  border-radius: 12px;
  background-color: var(--color-surface);
  box-shadow: var(--shadow-surface);
  height: 100%;
}

.NoUser {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  p {
    font-size: 2rem;
    color: var(--color-text-secondary);
  }
}
