import React from 'react';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';

/**
 * PermissionSnackbar - 可重用的權限通知組件
 * 
 * @param {Object} props
 * @param {boolean} props.open - 是否顯示 Snackbar
 * @param {Function} props.onClose - 關閉時的回調函數
 * @param {string} props.message - 要顯示的訊息
 * @param {string} props.severity - 嚴重程度 ('error' | 'warning' | 'info' | 'success')
 * @param {number} props.autoHideDuration - 自動隱藏時間（毫秒），預設 4000
 * @param {string} props.anchorOrigin - 顯示位置，預設 'top-center'
 * @param {React.ReactNode} props.action - 自定義操作按鈕
 */
const PermissionSnackbar = ({
  open,
  onClose,
  message,
  severity = 'error',
  autoHideDuration = 4000,
  anchorOrigin = { vertical: 'top', horizontal: 'center' },
  action = null,
}) => {
  const handleClose = (event, reason) => {
    // 避免點擊外部時關閉
    if (reason === 'clickaway') {
      return;
    }
    onClose();
  };

  return (
    <Snackbar
      open={open}
      autoHideDuration={autoHideDuration}
      onClose={handleClose}
      anchorOrigin={anchorOrigin}
    >
      <Alert
        onClose={handleClose}
        severity={severity}
        variant="standard"
        sx={{ 
          width: '100%',
          minWidth: '300px',
          maxWidth: '600px',
          boxShadow: 3,
          '& .MuiAlert-message': {
            whiteSpace: 'pre-line',
            width: '100%',
          }
        }}
        action={
          action || (
            <IconButton
              size="small"
              aria-label="關閉"
              color="inherit"
              onClick={handleClose}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          )
        }
      >
        {message}
      </Alert>
    </Snackbar>
  );
};

export default PermissionSnackbar;
