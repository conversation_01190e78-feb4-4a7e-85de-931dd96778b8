/**
 * Header 權限檢查輔助工具
 * 用於檢查和調試菜單權限顯示
 */

import { allowGetIn, menus } from "../config/App-layout";
import role from "../config/App-role";

/**
 * 獲取指定角色可見的左側菜單項目
 * @param {string} userRole - 使用者角色
 * @returns {Array} 可見的菜單項目陣列
 */
export const getVisibleLeftMenus = (userRole) => {
  if (!menus.menuLeft) return [];
  return menus.menuLeft.filter((menu) => allowGetIn(menu, userRole));
};

/**
 * 獲取指定角色可見的右側菜單項目
 * @param {string} userRole - 使用者角色
 * @param {boolean} isLogin - 是否已登入
 * @returns {Array} 可見的菜單項目陣列
 */
export const getVisibleRightMenus = (userRole, isLogin = false) => {
  if (!menus.menuRight) return [];
  return menus.menuRight.filter(
    (menu) =>
      allowGetIn(menu, userRole) &&
      (isLogin ? menu.showOnLogin : !menu.showOnLogin)
  );
};

/**
 * 檢查使用者是否有權限查看特定菜單項目
 * @param {Object} menuItem - 菜單項目
 * @param {string} userRole - 使用者角色
 * @returns {boolean} 是否有權限
 */
export const canViewMenuItem = (menuItem, userRole) =>
  allowGetIn(menuItem, userRole);

/**
 * 獲取所有角色的菜單可見性矩陣（用於調試）
 * @returns {Object} 各角色的菜單可見性
 */
export const getPermissionMatrix = () => {
  const roles = [
    role.admin,
    role.editor,
    role.reader,
    role.developer,
    role.anonymous,
  ];
  const matrix = {};

  roles.forEach((r) => {
    matrix[r] = {
      leftMenus: getVisibleLeftMenus(r),
      rightMenus: getVisibleRightMenus(r, true), // 假設已登入
    };
  });

  return matrix;
};

export default {
  getVisibleLeftMenus,
  getVisibleRightMenus,
  canViewMenuItem,
  getPermissionMatrix,
};
