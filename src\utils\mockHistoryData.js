/**
 * 測試用假資料生成工具
 * 用於測試 History 頁面的 pagination 功能
 */

/**
 * 生成隨機日期字串 (YYYY-MM-DD 格式)
 * @param {Date} start - 起始日期
 * @param {Date} end - 結束日期
 * @returns {string} YYYY-MM-DD 格式的日期字串
 */
const randomDate = (start, end) => {
  const date = new Date(
    start.getTime() + Math.random() * (end.getTime() - start.getTime())
  );
  return date.toISOString().split('T')[0];
};

/**
 * 生成隨機時間字串 (HH:MM:SS 格式)
 * @returns {string} HH:MM:SS 格式的時間字串
 */
const randomTime = () => {
  const h = String(Math.floor(Math.random() * 24)).padStart(2, '0');
  const m = String(Math.floor(Math.random() * 60)).padStart(2, '0');
  const s = String(Math.floor(Math.random() * 60)).padStart(2, '0');
  return `${h}:${m}:${s}`;
};

/**
 * 生成使用者列表假資料
 * @param {number} count - 要生成的使用者數量
 * @returns {Array<{user: string}>} 使用者列表
 */
export const generateMockUserList = (count = 50) => {
  const users = [];
  const surnames = ['王', '李', '張', '劉', '陳', '楊', '黃', '趙', '周', '吳', '徐', '孫', '馬', '朱', '胡', '郭', '何', '林'];
  const names = ['明', '華', '芳', '麗', '娟', '敏', '靜', '秀', '英', '傑', '勇', '偉', '強', '軍', '波', '濤', '剛', '輝'];
  
  for (let i = 0; i < count; i += 1) {
    const surname = surnames[Math.floor(Math.random() * surnames.length)];
    const name1 = names[Math.floor(Math.random() * names.length)];
    const name2 = names[Math.floor(Math.random() * names.length)];
    users.push({
      user: `${surname}${name1}${name2}`
    });
  }
  
  return users;
};

/**
 * 生成使用者工作記錄假資料
 * @param {string} userName - 使用者名稱
 * @param {number} count - 要生成的記錄數量
 * @returns {Array<{hisId: string, time: string, landStr: string, action: string}>} 工作記錄列表
 */
export const generateMockUserRecords = (userName, count = 30) => {
  const records = [];
  const actions = [
    '新增土地資料',
    '編輯土地標示部',
    '編輯土地權利部',
    '刪除土地資料',
    '匯入土地資料',
    '匯出土地資料',
    '更新地籍圖',
    '修正座標資訊',
    '批次更新資料',
    '驗證土地資料'
  ];
  
  const landTypes = ['建', '田', '旱', '林', '養', '池', '鹽', '礦', '雜', '道'];
  
  for (let i = 0; i < count; i += 1) {
    const date = randomDate(new Date(2024, 0, 1), new Date());
    const time = randomTime();
    const landType = landTypes[Math.floor(Math.random() * landTypes.length)];
    const landNumber = String(Math.floor(Math.random() * 9999) + 1).padStart(4, '0');
    const action = actions[Math.floor(Math.random() * actions.length)];
    
    records.push({
      hisId: `his_${userName}_${i}_${Date.now()}`,
      time: `${date} ${time}`,
      landStr: `${landType}${landNumber}`,
      action
    });
  }
  
  // 按時間排序（新到舊）
  return records.sort((a, b) => new Date(b.time) - new Date(a.time));
};

/**
 * 生成日期列表假資料
 * @param {number} days - 要生成的天數
 * @returns {Array<{YYYYMMDD: string}>} 日期列表
 */
export const generateMockDateList = (days = 90) => {
  const dates = [];
  const today = new Date();
  
  for (let i = 0; i < days; i += 1) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    dates.push({
      YYYYMMDD: date.toISOString().split('T')[0]
    });
  }
  
  return dates;
};

/**
 * 生成日期記錄假資料
 * @param {string} dateStr - 日期字串 (YYYY-MM-DD)
 * @param {number} count - 要生成的記錄數量
 * @returns {Array<{hisId: string, user: string, action: string}>} 日期記錄列表
 */
export const generateMockDateRecords = (dateStr, count = 25) => {
  const records = [];
  const users = ['王明華', '李芳麗', '張娟敏', '劉靜秀', '陳英傑', '楊勇偉', '黃強軍', '趙波濤'];
  const actions = [
    '新增土地資料: 建0123',
    '編輯土地標示部: 田4567',
    '編輯土地權利部: 旱8901',
    '刪除土地資料: 林2345',
    '匯入土地資料: 批次匯入 50 筆',
    '匯出土地資料: 匯出查詢結果',
    '更新地籍圖: 雜6789',
    '修正座標資訊: 建0987',
    '批次更新資料: 更新 30 筆土地權利部',
    '驗證土地資料: 檢查 15 筆資料完整性'
  ];
  
  for (let i = 0; i < count; i += 1) {
    const user = users[Math.floor(Math.random() * users.length)];
    const action = actions[Math.floor(Math.random() * actions.length)];
    
    records.push({
      hisId: `his_date_${dateStr}_${i}_${Date.now()}`,
      user,
      action
    });
  }
  
  return records;
};

/**
 * 配置：是否使用假資料（開發測試用）
 * 設為 true 啟用假資料，false 使用真實 API
 */
export const USE_MOCK_DATA = false;

/**
 * 模擬 API 延遲（毫秒）
 */
export const MOCK_API_DELAY = 500;

/**
 * 模擬 API 呼叫
 * @param {Function} dataGenerator - 資料生成函數
 * @returns {Promise} 模擬的 API response
 */
export const mockApiCall = (dataGenerator) => new Promise((resolve) => {
  setTimeout(() => {
    resolve({
      data: dataGenerator(),
      head: [],
      total: '1'
    });
  }, MOCK_API_DELAY);
});
