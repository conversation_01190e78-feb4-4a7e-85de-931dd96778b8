/**
 * 時間工具函式
 * 用於格式化時間顯示，支援相對時間和時長計算
 */

/**
 * 格式化為相對時間（中文顯示）
 * @param {string|number} timestamp - 時間戳記（毫秒）
 * @returns {string} 相對時間字串，如「剛才」、「5 分鐘前」、「1 小時前」
 */
export const formatRelativeTime = (timestamp) => {
  if (!timestamp) return '-';
  
  const now = Date.now();
  const time = typeof timestamp === 'string' ? parseInt(timestamp, 10) : timestamp;
  const diff = now - time;
  
  // 小於 1 分鐘
  if (diff < 60000) {
    return '剛才';
  }
  
  // 小於 1 小時
  const minutes = Math.floor(diff / 60000);
  if (minutes < 60) {
    return `${minutes} 分鐘前`;
  }
  
  // 小於 24 小時
  const hours = Math.floor(minutes / 60);
  if (hours < 24) {
    return `${hours} 小時前`;
  }
  
  // 小於 2 天
  const days = Math.floor(hours / 24);
  if (days === 1) {
    return '昨天';
  }
  
  // 小於 7 天
  if (days < 7) {
    return `${days} 天前`;
  }
  
  // 超過 7 天，顯示日期
  const date = new Date(time);
  return date.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

/**
 * 計算從開始時間到現在的時長
 * @param {string|number} startTime - 開始時間戳記（毫秒）
 * @returns {number} 時長（毫秒）
 */
export const calculateDuration = (startTime) => {
  if (!startTime) return 0;
  
  const start = typeof startTime === 'string' ? parseInt(startTime, 10) : startTime;
  return Date.now() - start;
};

/**
 * 格式化時長顯示（中文）
 * @param {number} milliseconds - 時長（毫秒）
 * @returns {string} 格式化的時長字串，如「5 分鐘」、「1 小時 23 分鐘」
 */
export const formatDuration = (milliseconds) => {
  if (!milliseconds || milliseconds < 0) return '-';
  
  const totalMinutes = Math.floor(milliseconds / 60000);
  
  // 小於 1 分鐘
  if (totalMinutes < 1) {
    return '不到 1 分鐘';
  }
  
  // 小於 1 小時
  if (totalMinutes < 60) {
    return `${totalMinutes} 分鐘`;
  }
  
  // 超過 1 小時
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  
  if (minutes === 0) {
    return `${hours} 小時`;
  }
  
  return `${hours} 小時 ${minutes} 分鐘`;
};

/**
 * 檢查編輯時長是否過長（超過30分鐘）
 * @param {number} duration - 時長（毫秒）
 * @returns {boolean} 是否過長
 */
export const isLongEditingDuration = (duration) => {
  const THIRTY_MINUTES = 30 * 60 * 1000;
  return duration > THIRTY_MINUTES;
};

/**
 * 格式化完整日期時間
 * @param {string|number} timestamp - 時間戳記（毫秒）
 * @returns {string} 格式化的日期時間字串
 */
export const formatFullDateTime = (timestamp) => {
  if (!timestamp) return '-';
  
  const time = typeof timestamp === 'string' ? parseInt(timestamp, 10) : timestamp;
  const date = new Date(time);
  
  return date.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  });
};
