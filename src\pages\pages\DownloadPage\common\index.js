import { Api, readOntoData, fetchPOSTData } from "../../../../api/land/Api";
import { isEmpty } from "../../../../utils";
import { getOwners } from "../../EditPage/common";
import evtType from "../../../../utils/evtType";

export const getSearchAPI = (download) => {
  const { landType, dwLandName, dwLandSerialNumber, dataFormType } = download;
  const findType = landType.find((el) => el.select);
  const findFormType = dataFormType.find((el) => el.check);

  if (!findType || !findFormType) return "";

  let apiStr = "";
  switch (findType.property) {
    case evtType.LandMark: {
      switch (findFormType.type) {
        case "type1":
        case "type3":
          if (dwLandName) {
            apiStr = Api.getLMType1().replace("{landName}", dwLandName);
          }
          break;
        case "type2":
          if (dwLandName && dwLandSerialNumber) {
            apiStr = Api.getLMType2()
              .replace("{landName}", dwLandName)
              .replace("{landSerialNumber}", dwLandSerialNumber);
          }
          break;
        default:
          break;
      }
      break;
    }
    case evtType.LandRights: {
      switch (findFormType.type) {
        case "type1":
        case "type3":
          if (dwLandName) {
            apiStr = Api.getLRType1().replace("{landName}", dwLandName);
          }
          break;
        case "type2":
          if (dwLandName && dwLandSerialNumber) {
            apiStr = Api.getLRType2()
              .replace("{landName}", dwLandName)
              .replace("{landSerialNumber}", dwLandSerialNumber);
          }
          break;
        default:
          break;
      }
      break;
    }
    default:
      break;
  }
  return apiStr;
};

export const transKeyName = {
  landArea: "面積",
  landGC: "地目+則別",
  cause: "登記（變更）原因",
  landRent: "土地面積",
};

export const getLSNList = (dwLandName) =>
  new Promise((resolve, reject) => {
    const apiStr = Api.getLSList().replace("{landName}", dwLandName);
    readOntoData(apiStr).then((result) => {
      resolve(result.data);
    });
  });

/** 套用特殊規則
 * 1. 若是有子號的分割土地，滙出時登記次序1的時間自動標示為母號分割的時間
 * 2. 若非分割土地，滙出時登記次序1的時間自動標示為登記次序2的年份前一年
 * */
export const addFirstLMEventYear = (resultData) => {
  let tmpResultData = JSON.parse(JSON.stringify(resultData));
  // 登記次序1的事件，如果沒有年分，補上年份規則
  let allFirstLMEvents = tmpResultData.filter(
    (obj) => obj.landMarkNumber === "1"
  );
  allFirstLMEvents = allFirstLMEvents.map((obj) => {
    if (!obj.year) {
      // 找到登記次序2的事件
      if (obj.landSerialNumber.indexOf("-") >= 0) {
        // 分割出來的土地，找到原因是母號分割出來的事件時間
        const findObj = tmpResultData
          .filter(
            (el) => el.landSerialNumber === obj.landSerialNumber.split("-")[0]
          )
          .filter((el) => el.cause)
          .find(
            (el) =>
              el.cause.indexOf("分割出") >= 0 &&
              el.cause.indexOf(obj.landSerialNumber) >= 0
          );
        if (findObj) {
          return { ...obj, year: `${parseInt(findObj?.year)}` };
        }
      } else {
        // 非分割出來的土地
        const findObj = tmpResultData.find(
          (el) =>
            el.landMarkNumber === "2" &&
            el.landSerialNumber === obj.landSerialNumber
        );
        if (findObj && findObj.year) {
          return { ...obj, year: `${parseInt(findObj.year) - 1}` };
        }
      }
    }
    return obj;
  });
  // merge tmpResultData and allFirstLMEvents
  tmpResultData = tmpResultData.map((obj) => {
    const findObj = allFirstLMEvents.find(
      (el) =>
        el.landSerialNumber === obj.landSerialNumber &&
        el.landMarkNumber === obj.landMarkNumber
    );
    if (findObj) {
      if (findObj.year) {
        return { ...obj, year: findObj.year };
      }
    }
    return obj;
  });
  return tmpResultData;
};

// 取得在指定日期內，有變更資料內容的landID
export const getAllUpdatedLandID = (startDate, endDate) =>
  new Promise((resolve, reject) => {
    const tmpStartDate = startDate.toISOString().split("T")[0];
    const tmpEndDate = endDate.toISOString().split("T")[0];
    const apiStr = Api.getHisIDByDate()
      .replace("{startDate}", tmpStartDate)
      .replace("{endDate}", tmpEndDate);

    readOntoData(apiStr).then((result) => {
      const allUpdateLandID = result.data
        .map((obj) => ({
          landId: obj.landId,
          user: obj.user,
        }))
        .filter(
          (element, pos) =>
            result.data.findIndex((item) => item.landId === element.landId) ===
            pos
        );
      resolve(allUpdateLandID);
    });
  });

const getAllLandMarkInfo = (landName, landSerialNumber, landId) =>
  new Promise((resolve, reject) => {
    const apiStrLM = Api.getLandMarkID().replace("{landId}", landId);
    readOntoData(apiStrLM).then((result) => {
      if (!isEmpty(result.data)) {
        const LMIds = result.data.map((element) => element.landMarkId);
        const LMApiStr = Api.getLandMarkEvents().replace("{ids}", LMIds.join());
        readOntoData(LMApiStr).then((LMEvents) => {
          let tmpLMEvents = JSON.parse(JSON.stringify(LMEvents.data));
          tmpLMEvents = tmpLMEvents.map((obj) => {
            if (obj.predicate === "hasStartDate") {
              return { ...obj, value: obj.value.split("-")[0] };
            }
            return obj;
          });
          resolve(tmpLMEvents);
        });
      } else {
        resolve([]);
      }
    });
  });

const getAllLandRightInfo = (landId) =>
  new Promise((resolve, reject) => {
    const apiStrLR = Api.getLandRightsID().replace("{landId}", landId);
    readOntoData(apiStrLR).then(async (result) => {
      if (!isEmpty(result.data)) {
        const LRIds = result.data.map((element) => element.landRightsId);
        const LRApiStr = Api.getLandRightsEvents().replace(
          "{ids}",
          LRIds.join()
        );
        const latestOwnerList = await getOwners();
        readOntoData(LRApiStr).then((LREvents) => {
          let tmpResult = [];
          LREvents.data.forEach((obj) => {
            const findObj = tmpResult.find(
              (element) =>
                element.predicate === obj.predicate &&
                element.landRightsId === obj.landRightsId
            );
            if (!findObj) {
              if (obj.predicate === "hasOwner") {
                const newObj = JSON.parse(JSON.stringify(obj));
                newObj.value = [obj.value];
                tmpResult.push(newObj);
              } else if (obj.predicate === "hasStartDate") {
                const newObj = JSON.parse(JSON.stringify(obj));
                newObj.value = obj.value.split("-")[0];
                tmpResult.push(newObj);
              } else {
                tmpResult.push(obj);
              }
            } else {
              // 如果已經有相同landRightsId與predicate資料存在tmpResult3裡面，value改成用array方式儲存
              if (obj.predicate === "hasOwner") {
                findObj.value.push(obj.value);
              }
            }
          });

          // 置換owner ID
          tmpResult = tmpResult
            .map((obj) => {
              // 替換landRights的Owner ID
              if (obj.predicate === "hasOwner") {
                if (obj.value.length > 0) {
                  return {
                    ...obj,
                    value: obj.value.map(
                      (item) =>
                        latestOwnerList.find(
                          (ownerObj) => ownerObj.label === item
                        ).value
                    ),
                  };
                }
              }
              return obj;
            })
            .map((obj) => {
              if (obj.predicate === "hasOwner") {
                if (obj.value.length > 0) {
                  return { ...obj, value: obj.value.join("，") };
                }
              }
              return obj;
            });
          // console.log("tmpResult ", tmpResult);
          resolve(tmpResult);
        });
      } else {
        resolve([]);
      }
    });
  });

export const getAllLandData = (allUpdateLandID) =>
  new Promise((resolve, reject) => {
    const apiStr = Api.getPostLandData().replace(
      "{ids}",
      allUpdateLandID.join()
    );
    fetchPOSTData({
      apiStr,
      entry: {
        ids: allUpdateLandID.join(),
      },
    })
      .then(async (result) => {
        const tmpData = [];
        for (const landId of allUpdateLandID) {
          const tmpObj = {};
          // 蒐集Basic Info
          const findBscInfo = result.data.filter((el) => el.landId === landId);
          tmpObj.Land = findBscInfo;

          const findLandName = result.data.find(
            (el) => el.predicate === "landName" && el.landId === landId
          );
          const findLandSerialNumber = result.data.find(
            (el) => el.predicate === "landSerialNumber" && el.landId === landId
          );
          // 蒐集LandMark Info
          tmpObj.LandMark = await getAllLandMarkInfo(
            findLandName.value,
            findLandSerialNumber.value,
            landId
          );

          // 蒐集LandRight Info
          tmpObj.LandRights = await getAllLandRightInfo(landId);
          tmpData.push(tmpObj);
        }
        resolve(tmpData);
      })
      .catch((error) => {
        reject([]);
      });
  });
