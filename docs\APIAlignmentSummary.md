# API 對齊總結文檔

## 概述
根據 `docs/EXCEL_IMPORT_FORMAT.md` 的 API 規格文件，對 ImportDataPage 的驗證邏輯進行了全面調整。

## 文件來源
- **API 規格文件**: `docs/EXCEL_IMPORT_FORMAT.md` (490 行)
- **更新日期**: 2024

---

## 主要變更項目

### 1. DEFAULT_HEADERS 配置更新

**檔案**: `src/pages/pages/ImportDataPage/utils/excelTemplate/generateTemplate.js`

**變更內容**:

#### 欄位標籤更新（符合 API 規格）
| 欄位 ID | 舊標籤 | 新標籤 | 說明 |
|---------|--------|--------|------|
| `Land-->operator` | 經手人 | **經辦者** | API 標準用詞 |
| `Land-->number` | 案號 | **號數** | API 標準用詞 |
| `Land-->landSerialNumber` | 土地序號 | **地號** | API 標準用詞 |
| `Land-->hasEvent-->LandMark-->landMarkNumber` | 土地標示編號 | **地籍編號** | API 標準用詞 |
| `Land-->hasEvent-->LandMark-->cause` | 土地標示原因 | **地籍原因** | API 標準用詞 |
| `Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent` | 土地標示起始日期 | **地籍開始日期** | API 標準用詞 |
| `Land-->hasEvent-->LandMark-->landGrades` | 土地等級 (整數) | **地等** | API 標準用詞 |
| `Land-->hasEvent-->LandMark-->landCategory` | 土地類別 | **地目** | API 標準用詞 |
| `Land-->hasEvent-->LandMark-->landArea` | 土地面積 (小數) | **土地面積** | API 標準用詞 |
| `Land-->hasEvent-->LandMark-->landRent` | 土地租金 (小數) | **租金** | API 標準用詞 |
| `Land-->hasEvent-->LandRights-->landRightsNumber` | 土地權利編號 | **地權編號** | API 標準用詞 |
| `Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent` | 土地權利起始日期 | **地權開始日期** | API 標準用詞 |
| `Land-->hasEvent-->LandRights-->cause` | 土地權利原因 | **地權原因** | API 標準用詞 |
| `Land-->hasEvent-->LandRights-->hasOwner-->Owner` | 所有權人 | **業主** | API 標準用詞 |

#### 新增屬性標註
每個欄位新增以下屬性：
- **`required`**: 標註是否必填
  - `landName` 和 `landSerialNumber`: `true` (必填)
  - 其他所有欄位: `false` (選填)
  - `landMarkNumber` 和 `landRightsNumber`: 條件必填（註解標註）

- **`format`**: 標註特殊格式要求
  - 日期欄位: `"year"` (4位數年份)
  - 數值欄位: `"number"` (小數)
  - 業主欄位: `"multiple"` (多值，逗號分隔)

---

### 2. 日期驗證邏輯

**檔案**: 
- `src/pages/pages/ImportDataPage/utils/excelBody/complexRule/checkDateEvt.js`
- `src/pages/pages/ImportDataPage/utils/excelBody/basicRule/checkIsInt.js`

**驗證結果**: ✅ **已符合 API 規格**

- 使用 `Number.isInteger(cell.value)` 檢查日期是否為整數（4位數年份）
- 接受格式: `1920`, `1925`, `1930` 等
- 拒絕格式: `"1920-01-01"`, `"YYYY-MM-DD"`, 非整數值

**範例**:
```javascript
// ✅ 正確格式
1920
1925
1930

// ❌ 錯誤格式
"1920-01-01"
"YYYY-MM-DD"
1920.5
```

---

### 3. 必填欄位邏輯

**檔案**: `src/pages/pages/ImportDataPage/utils/excelBody/complexRule/checkLandNSN.js`

**變更前**:
```javascript
// 舊邏輯：只有當 landMarkNumber 或 landRightsNumber 有值時才必填
const specialColVal = getLMLRNumber(cell, worksheet);
if (specialColVal.includes("1")) {
  tmpStr = checkMustHas(cell, worksheet);
}
```

**變更後**:
```javascript
// 新邏輯：landName 和 landSerialNumber 永遠必填
tmpStr = checkMustHas(cell, worksheet);
```

**API 規格說明**:
- `landName` 和 `landSerialNumber` **必須同時存在**
- 這是創建 `Land` 實體的必要條件
- 不再依賴其他欄位的存在性

**移除的依賴**:
- 移除了 `getLMLRNumber` 的導入和使用
- 簡化了驗證邏輯

---

### 4. 模板文件結構

**檔案**: `src/pages/pages/ImportDataPage/utils/excelTemplate/generateTemplate.js`

#### 4.1 generateTemplate() 函數（空白模板）

**變更內容**:

**Row 1 (標題列)**:
- 內容: 欄位 ID（`Land-->collectionPlace` 等）
- 樣式: 藍色字體 (#0066CC)、淺藍背景 (#E7F3FF)、粗體
- 凍結: 是（`ySplit: 1`）

**Row 2-3 (範例數據列)**:
- 內容: 兩筆完整的範例數據
- 樣式: 斜體灰色字體 (#808080)、淺黃背景 (#FFFFF0)
- 目的: 提供使用者參考格式

**Row 4+ (實際數據區)**:
- 內容: 10 列空白儲存格（供使用者填寫）
- 樣式: 淺灰色邊框 (#D0D0D0)

**範例數據內容**:
```javascript
// Row 2 範例
{
  "Land-->collectionPlace": "國家檔案局",
  "Land-->operator": "張三",
  "Land-->landName": "板橋林家",
  "Land-->landSerialNumber": "001",
  // 日期使用4位數年份
  "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent": "1920",
  "Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent": "1920",
  // 業主使用逗號分隔
  "Land-->hasEvent-->LandRights-->hasOwner-->Owner": "林維源,林爾嘉",
  // ...其他欄位
}

// Row 3 範例
{
  "Land-->collectionPlace": "國史館臺灣文獻館",
  "Land-->operator": "李四",
  "Land-->landName": "霧峰林家",
  "Land-->landSerialNumber": "002",
  "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent": "1925",
  "Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent": "1925",
  "Land-->hasEvent-->LandRights-->hasOwner-->Owner": "林朝棟",
  // ...其他欄位
}
```

#### 4.2 generateExample() 函數（範例文件）

**變更內容**:
- 使用相同的範例數據陣列
- Row 1: 欄位 ID（同模板）
- Row 2-3: 固定範例數據（同模板）
- Row 4+: 額外填入3筆範例數據（給使用者更多參考）

**範例數據特點**:
- ✅ 日期格式: 4位數年份 (`1920`, `1925`, `1930`)
- ✅ 多值格式: 逗號分隔無空格 (`林維源,林爾嘉`)
- ✅ 數值格式: 整數或小數 (`12.5`, `150.75`)
- ✅ 布林格式: `Y` 或 `N`
- ✅ 來源格式: 0-5 的整數

---

## API 規格關鍵要點

### 必填欄位
只有以下欄位為必填（必須同時存在）:
- `Land-->landName` (土地名稱)
- `Land-->landSerialNumber` (地號)

### 條件必填欄位
以下欄位在創建對應事件時必填：
- `Land-->hasEvent-->LandMark-->landMarkNumber` (地籍編號) - 創建 LandMark 事件時
- `Land-->hasEvent-->LandRights-->landRightsNumber` (地權編號) - 創建 LandRights 事件時

### 欄位格式要求

| 欄位類型 | 格式 | 範例 | 說明 |
|---------|------|------|------|
| 日期 | 4位數整數 | `1920` | 不接受 `YYYY-MM-DD` 格式 |
| 布林值 | `Y` 或 `N` | `Y`, `N` | 典權、耕作權 |
| 來源 | 0-5 整數 | `0`, `1`, `2`, `3`, `4`, `5` | 數據來源代碼 |
| 數值 (整數) | 整數 | `3`, `12` | 地等 |
| 數值 (小數) | 浮點數 | `12.5`, `150.75` | 面積、租金 |
| 多值 | 逗號分隔 | `林維源,林爾嘉` | 業主欄位（無空格） |

### Excel 結構
- **Row 1**: 欄位 ID (必須保留，系統識別用)
- **Row 2-3**: 範例數據 (可刪除)
- **Row 4+**: 實際數據（從這一行開始讀取）

---

## 檔案變更清單

### 修改的檔案
1. ✅ `src/pages/pages/ImportDataPage/utils/excelTemplate/generateTemplate.js`
   - 更新 `DEFAULT_HEADERS` 配置
   - 修改 `generateTemplate()` 函數
   - 修改 `generateExample()` 函數

2. ✅ `src/pages/pages/ImportDataPage/utils/excelBody/complexRule/checkLandNSN.js`
   - 移除條件判斷邏輯
   - 改為永遠檢查必填

3. ✅ `src/pages/pages/ImportDataPage/utils/excelBody/complexRule/checkDateEvt.js`
   - 驗證邏輯確認（無需修改，已符合規格）

4. ✅ `src/pages/pages/ImportDataPage/utils/excelBody/basicRule/checkIsInt.js`
   - 驗證邏輯確認（無需修改，已符合規格）

### 無需修改的檔案
- `src/pages/pages/ImportDataPage/utils/excelBody/checkBody.js` - 欄位驗證配置正確
- `src/pages/pages/ImportDataPage/config/index.js` - `specRow` 配置正確 (`startRow: 4`)

---

## 驗證測試建議

### 1. 必填欄位測試
測試案例：
```javascript
// ✅ 應通過驗證
{ landName: "板橋林家", landSerialNumber: "001" }

// ❌ 應失敗（缺少必填欄位）
{ landName: "板橋林家" } // 缺 landSerialNumber
{ landSerialNumber: "001" } // 缺 landName
{ } // 都缺
```

### 2. 日期格式測試
測試案例：
```javascript
// ✅ 應通過驗證
{ "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent": 1920 }
{ "Land-->hasEvent-->LandRights-->hasStartDate-->DateEvent": 1925 }

// ❌ 應失敗（格式錯誤）
{ "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent": "1920-01-01" }
{ "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent": "YYYY-MM-DD" }
{ "Land-->hasEvent-->LandMark-->hasStartDate-->DateEvent": 1920.5 }
```

### 3. 多值欄位測試
測試案例：
```javascript
// ✅ 應通過驗證
{ "Land-->hasEvent-->LandRights-->hasOwner-->Owner": "林維源,林爾嘉" }
{ "Land-->hasEvent-->LandRights-->hasOwner-->Owner": "單一業主" }

// ⚠️ 注意：系統目前接受任何字串格式
// 如需嚴格驗證逗號分隔格式，需額外開發驗證器
```

### 4. 模板下載測試
測試步驟：
1. 點擊「下載空白模板」按鈕
2. 開啟 Excel 檔案
3. 檢查 Row 1 是否有欄位 ID
4. 檢查 Row 2-3 是否有範例數據（斜體灰色字體）
5. 檢查日期欄位是否顯示 4位數年份（如 `1920`）
6. 檢查業主欄位是否顯示逗號分隔格式（如 `林維源,林爾嘉`）

---

## 相關文檔

- **API 規格**: `docs/EXCEL_IMPORT_FORMAT.md`
- **錯誤訊息優化**: `docs/ErrorMessageOptimization.md`
- **Excel 格式差異**: `docs/ExcelFormatDifference.md`
- **Excel 標題檢查清單**: `docs/ExcelHeaderChecklist.md`
- **驗證元件說明**: `docs/ValidationErrors.README.md`
- **零錯誤但失敗問題**: `docs/ZeroErrorsButFailed.md`

---

## 後續建議

### 1. 待實作功能
- 多值欄位（業主）的嚴格格式驗證
  - 檢查是否為逗號分隔
  - 驗證無多餘空格
  - 檢查每個值是否非空

### 2. 測試覆蓋
- 單元測試：各驗證器函數
- 整合測試：完整上傳流程
- E2E 測試：使用者操作流程

### 3. 使用者體驗優化
- 模板預覽功能
- 即時格式提示
- 自動修正常見錯誤（如日期格式）

---

## 變更歷史

| 日期 | 版本 | 變更內容 | 負責人 |
|------|------|----------|--------|
| 2024-12-XX | 1.0 | 初始版本，根據 API 文件完成對齊 | GitHub Copilot |

