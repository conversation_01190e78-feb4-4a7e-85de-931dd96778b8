# Phase 2 整合測試清單

## 📋 測試準備

### 測試環境設定
- [ ] 確認 `.env.development` 設定：
  ```
  REACT_APP_ENV=production  (測試正式站限制)
  REACT_APP_IMPORT=true
  ```
- [ ] 確認 Firebase 資料庫已更新 (所有使用者有 roleDev)
- [ ] 準備測試帳號

### 測試帳號準備
- [ ] **Developer 帳號**: <EMAIL> (測試受限功能)
- [ ] **Admin 帳號**: <EMAIL> (測試完整權限)
- [ ] **Editor 帳號**: <EMAIL> (測試基本編輯)
- [ ] **Multi-role 帳號**: developer,editor (測試優先級)

---

## 🧪 測試場景 1: Developer 在正式站

### 環境設定
```bash
REACT_APP_ENV=production
```

### 測試項目

#### 1.1 EditPage - 開始編輯按鈕
- [ ] 進入 EditPage
- [ ] 選擇一筆土地資料
- [ ] 點擊「點擊開始編輯」按鈕
- [ ] **預期**: 顯示 alert "Developer 角色在正式站僅能查看資料，不可編輯"
- [ ] **預期**: 不會進入編輯狀態

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

#### 1.2 EditPage - 儲存按鈕
- [ ] 進入 EditPage (假設已有編輯中資料)
- [ ] 點擊「儲存」按鈕
- [ ] **預期**: 顯示 alert "Developer 角色在正式站僅能查看資料，不可編輯"
- [ ] **預期**: 資料不會儲存

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

#### 1.3 GisPage - 修改座標
- [ ] 進入 GisPage
- [ ] 修改任意一個座標值 (X 或 Y)
- [ ] 點擊「儲存變更」按鈕
- [ ] **預期**: 顯示 alert "Developer 角色在正式站僅能查看資料，不可編輯"
- [ ] **預期**: SaveModal 不會開啟
- [ ] **預期**: 座標不會儲存到資料庫

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

#### 1.4 ImportDataPage - 頁面存取
- [ ] 嘗試進入 ImportDataPage
- [ ] **預期**: 無法看到正常的匯入功能
- [ ] **預期**: 顯示紅色 Alert 警告頁面
- [ ] **預期**: 警告內容包含：
  - "無法存取匯入功能"
  - "Developer 角色在正式站不允許匯入資料"
  - 顯示當前環境: "正式站" (紅色 badge)
  - 顯示您的角色: "Developer"
  - 建議行動: "在測試站執行匯入作業"

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

#### 1.5 AuthorityPage - 權限管理
- [ ] 進入 AuthorityPage
- [ ] **預期**: 頁面可以進入
- [ ] **預期**: 頁面頂部顯示黃色警告 Banner
- [ ] **預期**: 警告內容: "唯讀模式：僅 Admin 可編輯使用者權限。您目前只能檢視權限設定。"
- [ ] 嘗試勾選任何使用者的權限 checkbox
- [ ] **預期**: 所有 checkbox 都是 disabled (灰色)
- [ ] 點擊 disabled 的 checkbox
- [ ] **預期**: 可能顯示 tooltip "僅 Admin 可編輯使用者權限"

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

## 🧪 測試場景 2: Developer 在測試站

### 環境設定
```bash
REACT_APP_ENV=development
```

### 測試項目

#### 2.1 EditPage - 完整權限
- [ ] 進入 EditPage
- [ ] 選擇一筆土地資料
- [ ] 點擊「點擊開始編輯」
- [ ] **預期**: 成功進入編輯狀態，不顯示警告
- [ ] 修改任意欄位
- [ ] 點擊「儲存」
- [ ] **預期**: 成功儲存，不顯示警告

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

#### 2.2 GisPage - 完整權限
- [ ] 進入 GisPage
- [ ] 修改任意座標值
- [ ] 點擊「儲存變更」
- [ ] **預期**: 開啟 SaveModal，不顯示警告
- [ ] 確認儲存
- [ ] **預期**: 成功儲存座標

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

#### 2.3 ImportDataPage - 完整權限
- [ ] 進入 ImportDataPage
- [ ] **預期**: 可以看到正常的匯入功能
- [ ] **預期**: 沒有警告 Alert
- [ ] 上傳測試檔案
- [ ] **預期**: 檔案可以正常驗證和匯入

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

#### 2.4 AuthorityPage - 仍然唯讀
- [ ] 進入 AuthorityPage
- [ ] **預期**: 仍然顯示唯讀模式警告 (環境無關)
- [ ] **預期**: 所有 checkbox 仍然 disabled
- [ ] **原因**: Developer 永遠無法管理使用者權限

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

## 🧪 測試場景 3: Admin 在正式站

### 環境設定
```bash
REACT_APP_ENV=production
```

### 測試項目

#### 3.1 EditPage - 完整權限
- [ ] 進入 EditPage
- [ ] 選擇土地資料
- [ ] 點擊「點擊開始編輯」
- [ ] **預期**: 成功編輯，無警告
- [ ] 修改並儲存
- [ ] **預期**: 成功儲存，無警告

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

#### 3.2 GisPage - 完整權限
- [ ] 進入 GisPage
- [ ] 修改座標
- [ ] 點擊「儲存變更」
- [ ] **預期**: 成功儲存，無警告

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

#### 3.3 ImportDataPage - 完整權限
- [ ] 進入 ImportDataPage
- [ ] **預期**: 正常顯示匯入功能
- [ ] 上傳檔案
- [ ] **預期**: 可以正常匯入

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

#### 3.4 AuthorityPage - 管理權限
- [ ] 進入 AuthorityPage
- [ ] **預期**: 沒有唯讀警告
- [ ] **預期**: 所有 checkbox 可以操作 (不是 disabled)
- [ ] 嘗試修改某個使用者的權限
- [ ] **預期**: 可以勾選/取消勾選
- [ ] **預期**: 權限變更成功儲存到 Firebase

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

## 🧪 測試場景 4: 多選角色 (developer,editor)

### 測試帳號
建立一個測試帳號，role 設為: `"developer,editor"`

### 環境設定
```bash
REACT_APP_ENV=production
```

### 測試項目

#### 4.1 權限優先級測試 - 編輯功能
- [ ] 使用 "developer,editor" 帳號登入
- [ ] 進入 EditPage
- [ ] 點擊「點擊開始編輯」
- [ ] **預期**: 顯示 alert 禁止編輯
- [ ] **原因**: Developer 的環境限制優先於 Editor 的編輯權限

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

#### 4.2 權限優先級測試 - 匯入功能
- [ ] 使用 "developer,editor" 帳號
- [ ] 嘗試進入 ImportDataPage
- [ ] **預期**: 顯示警告頁面，無法進入
- [ ] **原因**: Developer 在正式站無匯入權限

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

#### 4.3 測試站環境下的多選角色
切換環境:
```bash
REACT_APP_ENV=development
```

- [ ] 使用相同的 "developer,editor" 帳號
- [ ] 進入 EditPage
- [ ] **預期**: 可以正常編輯 (Developer 在測試站有完整權限)
- [ ] 進入 ImportDataPage
- [ ] **預期**: 可以正常匯入

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

## 🧪 測試場景 5: 環境切換測試

### 測試項目

#### 5.1 環境變數生效確認
- [ ] 檢查當前 `.env.development` 的 `REACT_APP_ENV` 值
- [ ] 重新啟動開發伺服器: `npm start`
- [ ] 在 Console 輸入: `console.log(process.env.REACT_APP_ENV)`
- [ ] **預期**: 輸出當前環境值
- [ ] 使用 `environmentUtils.getCurrentEnvironment()` 確認
- [ ] **預期**: 返回正確的環境字串

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

#### 5.2 角色載入確認
- [ ] 登入任意帳號
- [ ] 在 Console 輸入檢查:
  ```javascript
  console.log('User:', window.store?.getState()?.user);
  console.log('Current Role:', window.store?.getState()?.user?.currentRole);
  console.log('Current Roles:', window.store?.getState()?.user?.currentRoles);
  ```
- [ ] **預期**: 
  - `currentRole`: 單一字串 (如 "developer")
  - `currentRoles`: 陣列 (如 ["developer"])
  - 正式站使用 `role` 欄位
  - 測試站使用 `roleDev` 欄位

**測試結果**: ⬜ 通過 / ⬜ 失敗  
**備註**: _______________

---

## 📊 測試結果統計

### 場景 1: Developer 在正式站
- 總測試項目: 5
- 通過: ___ / 5
- 失敗: ___ / 5

### 場景 2: Developer 在測試站
- 總測試項目: 4
- 通過: ___ / 4
- 失敗: ___ / 4

### 場景 3: Admin 在正式站
- 總測試項目: 4
- 通過: ___ / 4
- 失敗: ___ / 4

### 場景 4: 多選角色
- 總測試項目: 3
- 通過: ___ / 3
- 失敗: ___ / 3

### 場景 5: 環境切換
- 總測試項目: 2
- 通過: ___ / 2
- 失敗: ___ / 2

### 總計
- **總測試項目**: 18
- **總通過**: ___ / 18
- **總失敗**: ___ / 18
- **通過率**: ____%

---

## 🐛 發現的問題

### 問題 1
- **頁面**: _______________
- **問題描述**: _______________
- **重現步驟**: _______________
- **預期行為**: _______________
- **實際行為**: _______________
- **嚴重程度**: ⬜ Critical / ⬜ High / ⬜ Medium / ⬜ Low

---

### 問題 2
- **頁面**: _______________
- **問題描述**: _______________
- **重現步驟**: _______________
- **預期行為**: _______________
- **實際行為**: _______________
- **嚴重程度**: ⬜ Critical / ⬜ High / ⬜ Medium / ⬜ Low

---

## ✅ 測試完成簽核

- **測試執行人**: _______________
- **測試日期**: 2025-10-02
- **測試環境**: 
  - Node.js 版本: _______________
  - React 版本: _______________
  - Firebase SDK 版本: _______________
- **測試結論**: ⬜ 通過 / ⬜ 有問題需修正
- **備註**: _______________

---

## 📝 下一步行動

- [ ] 修正測試中發現的問題
- [ ] 重新測試失敗的項目
- [ ] 部署到測試環境進行驗收
- [ ] 準備 Phase 3 UI/UX 優化
- [ ] 撰寫使用者操作手冊
