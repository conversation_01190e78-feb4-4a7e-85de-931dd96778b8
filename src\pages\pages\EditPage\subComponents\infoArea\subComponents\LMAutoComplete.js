import React, { useContext, useState } from "react";

// material ui
import Autocomplete from "@mui/material/Autocomplete";
import Chip from "@mui/material/Chip";
import TextField from "@mui/material/TextField";

// utils
import {
  disableEditFunction,
  disableFontColor,
  getOptions,
  getOwners,
} from "../../../common";
import { landRightsPlaceHolder } from "../../../common/phDes";
import { Api, createOntoData } from "../../../../../../api/land/Api";
import { StoreContext } from "../../../../../../store/StoreProvider";
import evtType from "../../../../../../utils/evtType";

function LmAutoComplete({ info, handleChange, index }) {
  const [state] = useContext(StoreContext);
  const [options, setOptions] = useState([]); // cause選單
  const [ownerOption, setOwnerOption] = useState([]); // owner選單

  const defaultReason = [
    "保存登記",
    "公業保存登記",
    "移轉-不明",
    "移轉-贈與",
    "移轉-法令",
    "移轉-買賣（杜賣、賣杜、賣渡、買得皆統一輸入為買賣）",
    "移轉-相續",
    "移轉-共有物分割",
    "移轉-放領（戰後）",
    "持分移轉（不用輸入原因）",
  ];

  return (
    <Autocomplete
      multiple
      disabled={disableEditFunction(state)}
      options={info.predicate === "cause" ? options : ownerOption}
      renderInput={(params) => (
        <TextField
          {...params}
          variant="standard"
          placeholder={
            info.value.length === 0 ? landRightsPlaceHolder[info.predicate] : ""
          }
          onClick={async () => {
            if (info.predicate === "cause") {
              let optionList = [];
              optionList = await getOptions(evtType.LandRights, info.predicate);
              let tmpOption = [];
              optionList.forEach((element) => {
                const tmpValue = element.label.split("，");
                tmpValue.forEach((value) => {
                  if (!tmpOption.includes(value)) {
                    tmpOption.push(value);
                  }
                });
              });

              // defaultReason和tmpOption不重複顯示相同原因
              let tmpDefaultReason = JSON.parse(JSON.stringify(defaultReason));
              tmpDefaultReason = tmpDefaultReason.filter((element) => {
                const tmpArr = tmpOption.filter((option) => option === element);
                return tmpArr.length === 0;
              });
              tmpOption = tmpOption.filter((el) => el !== "");
              const newOptions = [...tmpDefaultReason, ...tmpOption];
              setOptions(newOptions.sort());
            } else if (info.predicate === "hasOwner") {
              let optionList = [];
              optionList = await getOwners();
              const tmpOption = [];
              optionList.forEach((element) => {
                if (!tmpOption.find((obj) => obj.value === element.value)) {
                  tmpOption.push(element);
                }
              });
              const sortOptions = tmpOption.sort(
                (cur, next) => cur.label - next.label
              );
              setOwnerOption(sortOptions.map((item) => item.value));
            }
          }}
          inputProps={{
            ...params.inputProps,
            ...disableFontColor,
          }}
          multiline
        />
      )}
      onChange={async (event, value) => {
        const newData = { ...info, index };
        // hasOwner欄位新增一個Owner時建立instance
        if (info.predicate === "hasOwner") {
          const findName = value.filter(
            (name) => !info.value.includes(name)
          )[0];
          if (findName) {
            const latestOwnerList = await getOwners();
            const findOwnerID = latestOwnerList.find(
              (obj) => obj.value === findName
            );
            if (!findOwnerID) {
              // 沒找到ID，新增一個Owner
              const entrySrc = {
                graph: "south",
                classType: "Owner",
                srcId: "",
                value: { label: findName },
              };
              createOntoData(Api.restfulCRUD(), entrySrc);
            }
          }
        }
        handleChange(value, newData);
      }}
      freeSolo
      value={info.value}
      renderTags={(value, getTagProps) =>
        value.map((option, idx) => (
          <Chip
            variant="outlined"
            label={option}
            {...getTagProps({ index: idx })}
            style={{ opacity: 1 }}
          />
        ))
      }
    />
  );
}

export default LmAutoComplete;
