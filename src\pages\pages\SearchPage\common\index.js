import pathConfig from "../../../../config/route-path";

export const returnHref = (element) =>
  `${pathConfig.edit.url}?landId=${element.landId}`;

// 找出字串中有字母，過濾字母前面的數字字串並回傳
const splitAlpha = (str) => {
  const regex = /[a-zA-Z]/g;
  const alphaIndex = str.search(regex);
  return alphaIndex >= 0 ? str.substring(0, alphaIndex) : str;
};

// 比對"1-3"、"2-6"、"123"這類型的數字字串
export const sortingNumberWithBar = (cur, next) => {
  const newCur =
    cur?.landSerialNumber?.indexOf("-") >= 0
      ? cur.landSerialNumber
      : `${cur?.landSerialNumber || 0}-0`;
  const newNext =
    next?.landSerialNumber?.indexOf("-") >= 0
      ? next.landSerialNumber
      : `${next?.landSerialNumber || 0}-0`;
  const splitCur = newCur.split("-").map((num) => splitAlpha(num));
  const splitNext = newNext.split("-").map((num) => splitAlpha(num));
  if (splitCur[0] === splitNext[0]) {
    return splitCur[1] - splitNext[1];
  }
  return splitCur[0] - splitNext[0];
};
