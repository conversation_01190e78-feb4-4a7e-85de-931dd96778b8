{"brokenTestObjects": [{"approved": true, "testObjectId": "Object Repository/Mandarin/v1_web/Page_/div__MuiBackdrop-root MuiBackdrop-invisible_09aa02", "brokenLocator": "// 1. 點擊開啟下拉\r\nWebUI.click(findTestObject('Object Repository/dropdown_button'))\r\n\r\n// 2. 等待選單出現\r\nWebUI.waitForElementVisible(findTestObject('Object Repository/dropdown_menu_container'), 10)\r\n\r\n// 3. 用 JS 找選項文字並點擊\r\nString targetText = \"選項2\"  // 你想選的文字\r\n\r\nString script = \"\"\"\r\n  const options = document.querySelectorAll('li.MuiMenuItem-root');\r\n  for (let option of options) {\r\n    if(option.textContent.trim() === '${targetText}') {\r\n      option.click();\r\n      return true;\r\n    }\r\n  }\r\n  return false;\r\n\"\"\"\r\n\r\nBo<PERSON><PERSON> clicked = WebUI.executeJavaScript(script, null)\r\nif (!clicked) {\r\n  KeywordUtil.markFailed(\"找不到選項文字：${targetText}\")\r\n}\r\n", "brokenLocatorMethod": "CSS", "proposedLocator": "//div[2]/div", "proposedLocatorMethod": "XPATH", "recoveryMethod": "XPATH", "pathToScreenshot": "Reports/Self-healing/Object Repository/Mandarin/v1_web/Page_/div__MuiBackdrop-root MuiBackdrop-invisible_09aa02_position.png"}, {"approved": true, "testObjectId": "Object Repository/Mandarin/v1_web/Page_/input__mui-65", "brokenLocator": "//input[@id='mui-65']", "brokenLocatorMethod": "XPATH", "proposedLocator": "//div[@id='root']/div/div[2]/div/div[3]/div/table/tbody/tr/td[3]/div/div/input", "proposedLocatorMethod": "XPATH", "recoveryMethod": "XPATH", "pathToScreenshot": "Reports/Self-healing/Object Repository/Mandarin/v1_web/Page_/input__mui-65_idRelative.png"}, {"approved": true, "testObjectId": "Object Repository/Mandarin/v1_web/Page_/input__mui-125", "brokenLocator": "//input[@id='mui-125']", "brokenLocatorMethod": "XPATH", "proposedLocator": "//div[@id='root']/div/div[2]/div/div[3]/div/table/tbody/tr[11]/td[3]/div/div/input", "proposedLocatorMethod": "XPATH", "recoveryMethod": "XPATH", "pathToScreenshot": "Reports/Self-healing/Object Repository/Mandarin/v1_web/Page_/input__mui-125_idRelative.png"}, {"approved": true, "testObjectId": "Object Repository/Mandarin/v1_web/Page_/input__mui-126", "brokenLocator": "//input[@id='mui-126']", "brokenLocatorMethod": "XPATH", "proposedLocator": "//div[@id='root']/div/div[2]/div/div[3]/div/table/tbody/tr[11]/td[4]/div/div/input", "proposedLocatorMethod": "XPATH", "recoveryMethod": "XPATH", "pathToScreenshot": "Reports/Self-healing/Object Repository/Mandarin/v1_web/Page_/input__mui-126_idRelative.png"}, {"approved": true, "testObjectId": "Object Repository/Mandarin/v1_web/Page_/input__mui-225", "brokenLocator": "//input[@id='mui-225']", "brokenLocatorMethod": "XPATH", "proposedLocator": "//div[@id='root']/div/div[2]/div/div[3]/div/table/tbody/tr[11]/td[3]/div/div/input", "proposedLocatorMethod": "XPATH", "recoveryMethod": "XPATH", "pathToScreenshot": "Reports/Self-healing/Object Repository/Mandarin/v1_web/Page_/input__mui-225_idRelative.png"}, {"approved": true, "testObjectId": "Object Repository/Mandarin/v1_web/Page_/input__mui-226", "brokenLocator": "//input[@id='mui-226']", "brokenLocatorMethod": "XPATH", "proposedLocator": "//div[@id='root']/div/div[2]/div/div[3]/div/table/tbody/tr[11]/td[4]/div/div/input", "proposedLocatorMethod": "XPATH", "recoveryMethod": "XPATH", "pathToScreenshot": "Reports/Self-healing/Object Repository/Mandarin/v1_web/Page_/input__mui-226_idRelative.png"}, {"approved": true, "testObjectId": "Object Repository/Mandarin/v1_web/Page_/input__mui-3", "brokenLocator": "//input[@id='mui-3']", "brokenLocatorMethod": "XPATH", "proposedLocator": "//div[@id='root']/div/div[2]/div/div/div/div/input", "proposedLocatorMethod": "XPATH", "recoveryMethod": "XPATH", "pathToScreenshot": "Reports/Self-healing/Object Repository/Mandarin/v1_web/Page_/input__mui-3_idRelative.png"}]}