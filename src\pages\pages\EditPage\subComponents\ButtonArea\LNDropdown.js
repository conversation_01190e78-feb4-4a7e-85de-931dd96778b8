import React, { useContext, useState, useEffect } from "react";

// material ui
import Grid from "@mui/material/Grid";
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";

// uitls
import { StoreContext } from "../../../../../store/StoreProvider";
import { Api, readOntoData } from "../../../../../api/land/Api";
import Act from "../../../../../store/actions";

function LNDropdown() {
  const [state, dispatch] = useContext(StoreContext);
  const { landName, isLock, lockUser } = state.edit;
  const { displayName } = state.user;

  const [LNList, setLNList] = useState([]);

  useEffect(() => {
    const apiStr = Api.getLNList();
    readOntoData(apiStr).then((result) => {
      setLNList(result.data);
    });
  }, []);

  const handleChange = (event, newValue) => {
    let tmpValue = newValue;
    if (newValue === null) {
      tmpValue = "";
    }
    dispatch({
      type: Act.SET_LANDID,
      payload: "",
    });
    dispatch({
      type: Act.SET_LANDNAME,
      payload: tmpValue,
    });
    dispatch({
      type: Act.SET_LANDSERIALNUMBER,
      payload: "",
    });
    dispatch({
      type: Act.SET_SELECTIONINDEX,
      payload: 0,
    });
  };

  return (
    <Grid item xs={5}>
      <Autocomplete
        disablePortal
        options={LNList.map((element) => element.landName)}
        renderInput={(params) => (
          <TextField {...params} label="土名（舊地段名）" />
        )}
        value={landName}
        onChange={handleChange}
        disabled={isLock && displayName === lockUser}
      />
    </Grid>
  );
}

export default LNDropdown;
