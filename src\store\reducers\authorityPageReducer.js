import Act from "../actions";

const initState = {
    roles: ["<PERSON>", "Editor", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"],
    autPageLoading: "",
    userInfo: [],
};

const authorityPageReducer = (state = initState, action) => {
    switch (action.type) {
      case Act.SET_AUTPAGELOADING:
        return { ...state, autPageLoading: action.payload };
      case Act.SET_USERINFO:
        return { ...state, userInfo: action.payload };
      case Act.SET_CLIENTUSERINFO:
        return { ...state, clientUserInfo: action.payload };
      default:
        return state;
    }
};

export default authorityPageReducer;