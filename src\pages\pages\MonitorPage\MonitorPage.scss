// MonitorPage Styles
@import '../../../scss/common.scss';

.monitor-page {
  padding: var(--spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 100px);

  @media (max-width: 768px) {
    padding: var(--spacing-md);
  }

  .monitor-header {
    margin-bottom: var(--spacing-lg);
  }

  // 刷新按鈕動畫
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
